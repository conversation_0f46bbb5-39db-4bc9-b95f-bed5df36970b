import { NestedKey } from '@atinc/ngx-styx';

export const i18nSourceDefinition = {
    wiki: {
        question: '吗？',
        title: {
            default: '知识管理',
            page: ' | 知识管理'
        },
        desk: {
            newSpace: '新建空间',
            spaces: {
                all: '全部空间',
                organization: '组织空间',
                team: '团队空间',
                personal: '个人空间',
                archived: '归档空间',
                sharing: '共享中',
                share: '共享'
            },
            share: {
                title: '与我共享',
                sharedTime: '共享时间',
                sharedBy: '共享人',
                viewOnly: '仅查看',
                editable: '可编辑'
            },
            collections: {
                collectionTime: '收藏时间'
            },
            recent: {
                title: '最近查看',
                visitTime: '访问时间',
                viewTime: '查看时间',
                editTime: '编辑时间',
                invalid: '已失效'
            }
        },
        draft: {
            title: '草稿',
            notExist: '草稿不存在',
            delete: '删除草稿',
            publish: '发布',
            abandon: '放弃草稿',
            saveHint: '通过模板创建新页面时会将当前页面编辑的内容保存为草稿',
            deleteConfirm: '草稿删除后无法恢复，已发布的页面不受影响。'
        },
        action: {
            newBoard: '新建画板',
            newDoc: '新建文档',
            newTable: '新建表格',
            newGroup: '新建分组',
            newFromTemplate: '从模板新建'
        },
        page: {
            type: '页面类型',
            parent: '父页面',
            space: '所属空间',
            group: '分组',
            table: '表格',
            discussion: '会话',
            attachment: {
                upload: '本地上传',
                dragHint: '将文件拖拽至此上传附件',
                title: '页面附件',
                name: '附件',
                count: '{{count}}个附件'
            },
            copy: {
                withChildren: '连同子页面一起复制',
                select: '选择页面',
                toSpace: '复制至空间',
                batchHint: '批量复制时会同时复制子项，通过拖拽页面或点击选择父级将其复制至新位置',
                hint: '通过拖拽页面或点击选择父级将其复制至新位置',
                title: '复制页面',
                validation: {
                    spaceRequired: '所选空间不能为空',
                    pageRequired: '所属页面不能为空'
                }
            },
            edit: {
                title: '编辑提示',
                notifyFollowers: '页面有较大更新，通知页面关注者',
                publishOptions: '发布选项',
                noPermission: '您没有操作该功能的权限,请联系企业拥有者或者管理员',
                publish: '发布',
                publishing: '发布中...',
                lastSaved: '{{display_name}} 最后保存于 {{updated_at}}'
            },
            shared: {
                title: '页面共享'
            },
            sharing: {
                title: '共享',
                viewOnly: '仅查看',
                editable: '可编辑',
                configuring: '设置中...',
                selectMembers: '选择成员、部门或团队',
                permissions: '页面权限',
                independentPermission: '设置独立权限，控制空间成员的可见范围',
                inheritPermission: '设置权限后（除默认权限），子页面默认继承父页面权限，同时支持设置独立权限',
                defaultPermission: '默认权限',
                defaultWithParent: '当前页面的权限默认与父页面权限一致',
                defaultWithSpace: '默认与空间权限一致',
                allMemberViewEdit: '所有的空间成员均可查看和编辑',
                allMemberViewEditHint: '设置后，该空间的所有成员都能查看和编辑此页面',
                allMemberViewPartEdit: '所有的空间成员均可查看，部分人可以编辑',
                allMemberViewPartEditHint: '设置后，该空间的所有成员都能查看此页面，但只有添加的指定成员能编辑',
                specifiedMember: '只有指定的空间成员可以查看或编辑',
                specifiedMemberHint: '设置后，添加的指定空间成员可以查看或者编辑此页面',
                shareWithChildren: '同时共享子页面，展示为页面树',
                pageLink: '页面链接',
                nonSpaceMemberHint: '添加访问权限，非空间成员可在【与我共享】界面查看或编辑',
                switchConfirm: '切换后，当前设置将不生效，确认切换吗？',
                exitConfirm: '退出后，当前页面的权限设置将不生效，确认退出吗？'
            },
            history: {
                version: '版本',
                showDiff: '显示内容差异',
                showPublished: '仅显示已发布版本',
                restore: '恢复此版本',
                saveNamed: '保存命名版本'
            },
            info: {
                title: '页面信息',
                info: '信息',
                compare: '版本比对',
                visitRecord: '访问记录',
                charCount: '字符数',
                charCountWithSpace: '字符数（计空格）',
                charCountNoSpace: '字符数（不计空格）',
                totalWords: '总字数',
                charCountWithSpaceHint: '（计空格）',
                charCountNoSpaceHint: '（不计空格）'
            },
            move: {
                title: '移动页面',
                toSpace: '移动至空间',
                alert: '移动页面时会同时移动子项，通过拖拽页面或点击选择父级将其移至新位置'
            },
            dialog: {
                edit: '编辑（E）',
                askAI: '问拼扣',
                minimize: '最小化',
                cancelMinimize: '取消最小化',
                present: '演示（{{key}}+Shift+P）',
                copyLink: '复制链接',
                openInSpace: '空间中打开',
                comment: '评论',
                hideComment: '收起评论'
            },
            version: {
                saveNamed: '保存为命名版本',
                all: '全部版本',
                named: '命名版本',
                compare: '与版本',
                restore: {
                    description: '该操作将产生一个新的页面版本，内容与{{name}}相同',
                    confirm:
                        '确认恢复版本<span class="styx-confirm-target styx-confirm-target-primary text-truncate single-line">{{name}}</span>吗?'
                }
            },
            create: {
                child: '新建子页面',
                above: '上方新建页面',
                below: '下方新建页面'
            },
            delete: {
                description: '删除页面会一起删除子页面，可以通过空间设置里的回收站进行恢复。',
                confirmMultiple:
                    '确认删除选择的<span class="styx-confirm-target styx-confirm-target-danger text-truncate">{{count}}</span>个页面吗？',
                confirmSingle:
                    '<确认删除页面<span class=\\\"styx-confirm-target styx-confirm-target-danger text-truncate\\\">{{name}}</span>？',
                byUser: '{{display_name}}于{{time}}删除了页面'
            },
            name: {
                placeholder: '输入标题'
            },
            import: {
                sizeLimit: '文件大小不能超过 1 G',
                success:
                    '<p class="styx-confirm-container">已导入<span class="styx-confirm-target styx-confirm-target-primary">{{count}}</span>个页面，点击查看页面详情。</p>',
                attachmentSizeLimit: '附件内容大小不能超过 {{size}}'
            },
            export: {
                file: '{{type}} 文件',
                success:
                    '<div class="styx-confirm-target-wrap">已将页面导出为{{content}}，点击下载<span class="styx-confirm-target styx-confirm-target-primary download-page-dialog text-truncate">{{fileName}}</span></div>'
            },
            total: {
                count: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个页面</span>`
            },
            operation: {
                edit: '编辑（E）',
                ai: '问拼扣',
                present: '演示（{key}+Shift+P）',
                presentMode: '演示模式',
                favorite: '收藏页面',
                unfavorite: '取消收藏',
                lock: '锁定页面',
                fullWidth: '宽屏模式',
                print: '打印页面',
                export: '导出为',
                details: '查看详情'
            },
            publish: {
                success: '发布成功',
                time: '发布于'
            },
            reading: '阅读 · {{count}}',
            noPermission: '没有权限!',
            template: {
                namePlaceholder: '输入模板名称'
            },
            untitled: {
                document: '无标题文档',
                board: '无标题画板'
            }
        },
        presentation: {
            exit: '退出演示模式',
            timer: '关灯计时',
            pointer: '激光笔'
        },
        discussion: {
            comments: '{{display_name}}等人共发表了 {{count}} 条评论',
            mention: {
                placeholder: '@提及成员（Ctrl+Enter发送）'
            },
            header: {
                commentCount: '共{{count}}条评论',
                solveDiscussion: '解决会话',
                reopenDiscussion: '重新打开'
            },
            footer: {
                resolvedBy: '已由 {{name}} 解决于 {{time}}'
            },
            action: {
                reply: '回复',
                delete: '删除'
            },
            reply: {
                prefix: '回复 @{{name}} : \n >{{content}}'
            },
            version: {
                name: '版本名称'
            }
        },
        collaboration: {
            editing: '人正在编辑'
        },
        guider: {
            page: {
                operations: '，页面相关操作都在这里：页面共享、查看历史版本、锁定最终版本、导出文件，更多操作等你发现。',
                editor: '，进入编辑器进行文档编写。编辑器提供丰富的页面元素、支持Markdown语法、多人协同编辑并实时保存协作内容。',
                draft: '，空间中已编辑未发布的页面都展示在这里，页面编辑过程中自动保存至草稿。',
                new: '，为空间添加新的页面。提供结构化页面树，如需为某页面添加子页面，点击页面的更多-新建进行操作。',
                editPage: '编辑页面',
                accessSpace: '访问空间',
                createSpace: '新建空间'
            },
            space: {
                settings: '，进行空间、页面模板、角色权限配置等全局管理。',
                shared: '，右侧内容区展示开启共享的页面列表，如果成员将你加入到某个页面的共享中，可以从这里进行查看。',
                list: '，右侧内容区展示我参与的全部空间列表，点击空间查看空间的页面树及页面详情。',
                purpose: '，构建团队知识体系，可以是企业制度规范，可以是项目文档，也可以是接口文档，随心所欲。'
            }
        },
        knowledgeGraph: {
            reference: '引用',
            referenced: '被引用',
            title: '知识图谱',
            pageInfo: '页面信息',
            relatedItems: '关联事项',
            pageAttachments: '页面附件'
        },
        space: {
            brand: '空间',
            spaceName: '空间名称',
            identifier: '空间标识',
            description: '输入空间描述',
            identifierHint: '空间标识将作为页面编号的前缀',
            namePlaceholder: '输入空间名称',
            member: '空间成员',
            template: '空间模板',
            sharing: '空间共享',
            component: '空间组件',
            directory: '目录管理',
            statistics: '数据统计',
            nameRequired: '空间名称不能为空',
            identifierRequired: '空间标识不能为空',
            identifierExists: '该标识已被空间使用，请使用其他标识',
            removeMemberHint: '移除后该成员将无法再编辑当前空间，该成员产生的数据不受影响。',
            archiveHint: '归档后如果想重新使用该空间，可以通过配置中心由管理员进行激活。',
            deleteHint: '删除后如果想找回空间，可以通过配置中心由管理员进行恢复。',
            baselineHint: '基线设立后，当前基线下页面版本不再变更。',
            componentConfigHint: '修改当前空间组件的配置后，不会同步修改已有用户的自定义配置',
            delete: '删除空间',
            activate: '激活空间',
            archive: '归档空间',
            move: '移动空间',
            info: '空间信息',
            copy: '复制空间',
            exportTree: '导出页面树',
            stencils: '页面模板',
            exportPage: {
                select: '选择导出页面 · {{selectPagesCount}} ',
                maxTip: '最多可选 {{count}} 个页面，批量导出为 PDF'
            },
            identifierPattern: '英文字母/下划线/数字/连接线（不超过15字符）',
            newSpaceDescription:
                '空间是记录信息和知识的页面集合，通过组织页面层级将知识系统化、结构化，在知识管理层面助力企业更快更好的发布产品。',
            settings: {
                move: {
                    description: '如果要变更空间的所属关系及可见范围，你可以移动它。移动后不影响空间下的页面。'
                },
                archive: {
                    description: '如果此空间已经结束了，你可以归档它。归档后如果想重新使用该空间，可以通过配置中心由管理员进行激活。'
                },
                activate: {
                    description: '如果想重新使用该空间，你可以激活它。'
                },
                delete: {
                    description: '如果此空间已经不再需要了，你可以删除它。删除后如果想找回空间，可以通过配置中心由管理员进行恢复。'
                },
                directory: {
                    level: '层级',
                    levelTooltip: '页面层级',
                    showAll: '展示全部层级',
                    showLevel: '展示 {{level}} 级',
                    viewPage: '查看页面'
                },
                pageShared: {
                    spaceMember: '空间成员',
                    nonSpaceMember: '非空间成员',
                    publicShared: '公开共享'
                },
                spaceShared: {
                    title: '共享空间'
                },
                share: {
                    success: '外部共享内容已更新',
                    logoSizeLimit: 'logo文件不能大于1M'
                },
                memberDetails: {
                    department: '部门',
                    position: '职位'
                },
                identifier: {
                    pattern: '英文字母/下划线/数字/连接线（不超过15字符）'
                }
            },
            empty: {
                title: '未关联空间',
                desc: '当前{{type}}未关联空间，关联空间后，可以查看空间的页面列表'
            },
            relate: {
                title: '关联空间',
                select: '请选择空间',
                success: '{{title}}成功',
                cancelConfirm: '确认取消关联空间{{name}}' // 感觉没用到
            },
            selector: {
                empty: '空'
            },
            numberOfSpaces: '<span class="text-secondary">{{ count }}</span><span class="text-muted ml-1">个空间</span>'
        },
        template: {
            center: '模板中心',
            organization: '组织模板',
            space: '空间模板',
            name: '模板名称',
            saveAs: '另存为模板',
            orgScope: '（组织所有空间可用）',
            spaceScope: '（当前空间可用）',
            create: '从模板新建页面',
            exitConfirm: {
                title: '确定要退出编辑模板 {{name}} 吗？',
                description: '退出编辑将无法保存修改的内容，且不能找回。'
            },
            count: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个模版</span>`
        },
        error: {
            unknown: '未知错误',
            invalidImageType: '不被允许的图片类型',
            imageTooLarge: '图片太大了',
            operationFailed: '操作失败',
            noPermission: '你没有该页面操作权限！',
            pageLocked: '该页面已锁定，不能进行该操作！',
            pageBeingEdited: '当前页面正在被编辑',
            networkDisconnected: '网络已中断',
            waitForReconnect: '请等待重新连接后继续编辑',
            confirmExit: '确定要退出编辑吗？',
            waitForUpload: '退出编辑将无法保存上传内容，请等待上传再退出',
            messageServiceDisconnected: '消息服务已中断',
            permissionRemoved: '该页面编辑权限被移除，请联系管理员',
            pageDeleted: '该页面已被删除，请联系管理员',
            pageNotExist: '页面不存在或者你没有权限查看该页面',
            noAccessPermission: '你没有该页面的访问权限',
            deletePermissionDenied: '你没有页面的删除权限！',
            movePermissionDenied: '你没有页面的移动权限！',
            copyPermissionDenied: '你没有页面的复制权限！',
            editPermissionDenied: '你没有该页面编辑权限！',
            contentExceedLimit: '内容超限，请重新再试',
            unauthorized: '未授权使用',
            moveError: '移动失败',
            moveNoPermission: '你没有目标页面编辑权限！',
            exportError: '导出失败',
            exportLinkExpired: '下载链接过期，请重新导出',
            spaceNameRequired: '空间名称不能为空',
            spaceIdentifierRequired: '空间标识不能为空',
            spaceIdentifierExists: '该标识已被空间使用，请使用其他标识',
            templateNameRequired: '模板名称不能为空',
            templateScopeRequired: '使用范围不能为空',
            titleLengthExceed: '标题长度不能超过32字符',
            title: {
                operationFailed: '操作失败',
                editFailed: '编辑失败',
                deleteFailed: '删除失败',
                moveFailed: '移动失败',
                copyFailed: '复制失败',
                createFailed: '创建失败',
                publishFailed: '发布失败',
                pageBeingEdited: '当前页面正在被编辑'
            },
            message: {
                noPermission: '你没有该页面操作权限！',
                pageLocked: '该页面已锁定，不能进行该操作！',
                restoreConflict: '恢复会导致编辑的内容丢失，请等待其他用户退出编辑后再进行恢复！',
                deleteNoPermission: '你没有页面的删除权限！',
                moveNoPermission: '你没有页面的移动权限！',
                copyNoPermission: '你没有页面的复制权限！',
                editNoPermission: '你没有该页面编辑权限！',
                collaborativeEditLimit: '检测到当前页面已有{{expect_value}}人正在协同编辑，为保证编辑体验，请稍后编辑！',
                editPageLimit: '检测到当前你已打开{{expect_value}}个编辑页面，为保证编辑体验，请关闭其他页面！',
                usageLimit: '使用次数已达到上限',
                unauthorized: '未授权使用',
                contentExceedLimit: '内容超限，请重新再试',
                sensitiveContent: '内容包含敏感词，请检查后重试',
                retryLater: '操作失败，请重新再试'
            }
        },
        import: {
            title: '导入页面',
            cancel: '取消导入',
            failed: '导入失败',
            complete: '导入完成',
            fileNotExist: '文件不存在',
            invalidFormat: '文件格式错误',
            supportMarkdown: '支持导入以 .markdown，.md，.mark，.txt 结尾的文件',
            supportZip: '支持导入以 .zip 结尾的文件',
            supportDocx: '支持导入以 .docx 结尾的文件',
            supportConfluence: '支持 Confluence 空间中以 HTML 格式导出的 zip 文件',
            supportExcel: '支持导入以 .xlsx，.csv 结尾的文件'
        },
        export: {
            title: '导出页面',
            cancel: '取消导出',
            retry: '重新导出',
            failed: '导出失败',
            success: '导出成功',
            downloading: '下载中...',
            exporting: '页面导出中...',
            linkExpired: '下载链接过期，请重新导出',
            emptyPages: '导出页面不能为空'
        },
        baseline: {
            comparison: '版本比对',
            pages: {
                plan: '规划页面'
            },
            remove: {
                confirm: {
                    multiple:
                        '确认移出选择的<span class="styx-confirm-target styx-confirm-target-danger text-truncate">{{count}}</span>个页面吗？',
                    single: '确认移出页面{{name}}吗？',
                    description: '移出后可以重新规划页面。'
                }
            }
        },
        statistics: {
            title: '数据统计',
            overview: '数据概览',
            newThisWeek: '本周新增',
            memberDetails: '成员明细',
            pageDetails: '页面明细',
            member: {
                name: '姓名',
                createdPages: '创建页面数',
                editedPages: '参与编辑页面数',
                department: '部门',
                position: '职位',
                count: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个成员</span>`
            },
            page: {
                name: '页面名称',
                versions: '版本数',
                words: '字数',
                reads: '阅读量',
                comments: '评论量'
            },
            deleteTime: '删除时间',
            deleteBy: '删除人',
            restoreSuccess: '恢复成功'
        },
        resource: {
            productRequirement: '产品需求',
            productRequirementCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个产品需求</span>`,
            workOrder: '工单',
            workOrderCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个工单</span>`,
            workItemCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个工作项</span>`,
            testCase: '测试用例',
            testCaseCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个测试用例</span>`,
            objective: '目标',
            objectiveCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个目标</span>`,
            relatedItems: '关联事项',
            relatedItemsCount: `<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个关联事项</span>`
        },
        sort: {
            byViewTime: '按查看时间排序',
            byPublishTime: '按发布时间排序',
            byCreateTime: '按创建时间排序',
            byTitle: '按标题排序'
        },
        spaceSharing: {
            title: '空间共享',
            enabled: '已开启',
            hint: '开启共享后，获得链接的人可以访问该空间。',
            displaySettings: '展示设置',
            contentSettings: '内容设置',
            brandLogo: '品牌标识',
            brandLogoHint: '（仅支持JPG、PNG格式图片，建议100x30）',
            uploadLogo: '点击上传',
            copyright: '版权信息',
            showCopyright: '是否显示版权信息',
            customPages: '自定义页面',
            allPages: '全部页面',
            selectSharePages: '选择共享页面',
            sharePagesRequired: '共享页面不能为空',
            exitConfirm: '退出后，当前空间外部共享内容的设置将不生效，确认退出吗？'
        },
        common: {
            confirm: {
                delete: '确定删除',
                deleteComment: '确定要删除评论吗？',
                exit: '确定要退出编辑吗？',
                restore: '确认恢复',
                publish: '确认发布',
                moveOut: '确认移出'
            },
            status: {
                reading: '阅读 · {{reading}}',
                publishedAt: '发布于 {{updated_at}}'
            },
            action: {
                openInSpace: '空间中打开',
                scanQRCode: '扫一扫',
                viewInMiniApp: '在小程序查看页面',
                searchPage: '输入页面标题搜索',
                browseRecent: '最近浏览',
                newPage: '新建页面',
                saveAsTemplate: '另存为模板',
                saveNamedVersion: '保存命名版本',
                publishWithNotify: '发布并通知关注者',
                publishPage: '发布页面'
            },
            count: {
                subPages: '等 {{count}} 个页面',
                includeSubPages: '包含 {{count}} 个子页面'
            },
            property_setting: {
                update_success: '更新成功'
            },
            maxView: {
                back: '返回页面'
            }
        },
        configuration: {
            title: '空间配置',
            manage: {
                title: '空间管理',
                shareSettings: '共享设置',
                deleteHint: '删除后如果想找回空间，可以通过配置中心由管理员进行恢复。',
                column: {
                    sharedBy: '共享人',
                    sharedAt: '共享时间'
                }
            },
            space: {
                componentPermission: '组件权限',
                configHint: '修改组件的全局配置后，不会同步修改已有数据的配置'
            },
            review: {
                description: '自定义空间内的评审流程及规则',
                configHint: '修改评审配置后，不会影响已有数据；开启本地化配置后，当前评审配置不再受全局配置影响'
            },
            stencil: {
                delete: {
                    description: '删除后无法恢复。'
                }
            },
            tags: {
                deleteDescription: '标签删除后不可恢复。共 {{related_count}} 个页面正在使用此标签，删除后会从对应页面中移除。'
            }
        },
        plugin: {
            alert: '提示框',
            alertDesc: '使用彩色提示框高亮重点内容',
            attachment: '已有文件',
            attachmentDesc: '支持插入附件列表中的内容',
            attachmentCloud: '本地文件',
            attachmentCloudDesc: '支持 Office、PDF 等文件',
            audio: '本地音频',
            audioDesc: '支持在线播放音频',
            date: '日期',
            mindmap: '思维导图',
            board: '画板',
            flowchart: '流程图',
            drawio: 'Draw.io',
            noGraphics: '无图形',
            formula: 'LaTex 公式',
            formulaDesc: '支持插入 LaTex 语法公式',
            tagDesc: '自定义状态标签',
            layout: '布局',
            layoutDesc: '使用布局分栏展示页面内容',
            outline: '大纲',
            pageOutline: '页面大纲',
            pageOutlineDesc: '插入本页面的大纲',
            saveDraft: '保存草稿',
            fontSize: '字号',
            insert: '插入',
            productManage: '产品管理',
            projectManage: '项目管理',
            testManage: '测试管理',
            wikiManage: '知识管理',
            collaborativeSpace: '协作空间',
            relationIdea: '产品需求',
            relationIdeaList: '产品需求列表',
            relationObjectiveList: '目标列表',
            relationPageTree: '页面树',
            relationTestCaseList: '测试用例列表',
            relationTicketList: '工单列表',
            relationWorkItemList: '工作项列表',
            insight: '效能度量',
            relationReport: '报表',
            searchReplace: '搜索替换',
            textDiagram: '文本绘图',
            toggleList: '折叠列表',
            video: '视频',
            localVideo: '本地视频',
            graphics: '图形',
            thirdPartyService: '第三方服务',
            placeholder: "输入 '/' 快速插入内容",
            newNode: '新建节点',
            end: '结束',
            yes: '是',
            no: '否',
            process: '过程',
            decision: '判断'
        },
        attachments: {
            tabs: {
                picture: '图片',
                audio: '音频',
                video: '视频'
            }
        },
        validator: {
            space: {
                required: '所选空间不能为空'
            },
            page: {
                required: '所属页面不能为空'
            }
        },
        pagination: {
            range: '第 '
        },
        permission: {
            type: {
                readonly: '仅查看',
                editable: '可编辑'
            },
            page: {
                default: {
                    name: '默认权限',
                    desc: '当前页面的权限默认与空间权限一致。'
                },
                allReadAndEdit: {
                    name: '所有的空间成员均可查看和编辑',
                    desc: '设置后，该空间的所有成员都能查看和编辑此页面。'
                },
                allReadSpecificEdit: {
                    name: '所有的空间成员均可查看，部分人可以编辑',
                    desc: '设置后，该空间的所有成员都能查看此页面，但只有添加的指定成员能编辑。'
                },
                specificReadAndEdit: {
                    name: '只有指定的空间成员可以查看或编辑',
                    desc: '设置后，添加的指定空间成员可以查看或者编辑此页面。'
                }
            }
        },
        property: {
            review: {
                status: '评审状态',
                result: '评审结果'
            },
            importance: '重要程度'
        },
        pricing: {
            expired: {
                description: '提供结构化空间来记载信息和知识，便于团队沉淀经验、共享资源'
            },
            features: {
                pageExport: {
                    title: '页面导出',
                    subTitle: '将页面内容导出为文件，支持 PDF、Word 和 Markdown 格式',
                    offlineReading: '离线阅读',
                    contentStyle: '无差别内容样式'
                },
                pageTreeExport: {
                    title: '导出页面树',
                    subTitle: '将页面内容批量导出为 PDF 文件，最多可选 100 个页面'
                },
                pageTemplate: {
                    title: '页面模板',
                    subTitle: '让团队的知识沉淀标准化',
                    orgTemplate: '组织模板管理',
                    spaceTemplate: '空间模板管理'
                },
                pageShare: {
                    title: '页面共享',
                    subTitle: '灵活设置不同角色的页面权限，促进企业内外部知识资源的传播共享',
                    memberPermission: '支持给空间成员设置页面独立权限',
                    externalPermission: '支持给空间外部成员设置共享权限',
                    publicShare: '支持生成共享链接对外公开页面'
                },
                embedment: {
                    title: '插入本地音频/视频文件',
                    subTitle: '支持 mp4、mkv、webm、mov、mp3、wma、wav、ape、flac、ogg、m4r、m4a 格式',
                    onlinePlay: '视频/音频文件在线播放',
                    richContent: '丰富页面的内容，展示多元化信息'
                },
                spaceShare: {
                    title: '空间共享',
                    subTitle: '生成共享链接使知识资源传播更广泛',
                    expiry: '设置有效期',
                    password: '设置访问密码',
                    qrCode: '扫码阅读'
                }
            }
        },
        review: {
            title: '标题',
            versionCompare: '版本比对',
            removeConfirm: {
                title: '确认移出',
                action: '移出',
                multiple:
                    '确认移出选择的<span class="styx-confirm-target styx-confirm-target-danger text-truncate">{{count}}</span>个页面吗？',
                single: '<span class="styx-confirm-nowrap">确认移出页面</span>{{name}}吗？',
                description: '移出后可以重新添加{{text}}。'
            }
        },
        tag: {
            delete: {
                description: '标签删除后不可恢复。共 {{related_count}} 个页面正在使用此标签，删除后会从对应页面中移除。'
            }
        },
        relation: {
            disconnect: {
                description: '取消关联后可以重新建立关联。',
                confirm: '确认取消与页面{name}的关联吗？'
            },
            pageTitle: '搜索页面标题',
            pageDeleted: '该页面已被删除或没有权限查看',
            reportDeleted: '该报表已被删除或没有权限查看',
            moreWorkItems: '更多工作项',
            workItemTitle: '搜索工作项标题或编号',
            workItemDeleted: '该工作项已被删除或没有权限查看',
            moreCases: '更多用例',
            caseTitle: '搜索测试用例标题或编号',
            caseDeleted: '该测试用例已被删除或没有权限查看',
            moreRequirements: '更多需求',
            requirementTitle: '搜索产品需求标题或编号',
            requirementDeleted: '该产品需求已被删除或没有权限查看',
            moreTickets: '更多工单',
            ticketTitle: '搜索工单标题或编号',
            ticketDeleted: '该工单已被删除或没有权限查看',
            moreObjectives: '更多目标',
            objectiveTitle: '搜索目标标题',
            objectiveDeleted: '该目标已被删除或没有权限查看'
        },
        media: {
            title: '插入本地音频/视频文件',
            onlinePlay: '视频/音频文件在线播放'
        },
        member: {
            all: '所有人'
        },
        document: {
            wordCount: '共 {{count}} 字',
            emoji: {
                change: '更改表情',
                standardMode: '标准模式',
                remove: '移除表情',
                add: '添加表情'
            },
            placeholder: '请输入内容',
            imageError: {
                sizeLimit: '不支持上传50M以上图片。',
                formatError: '不支持上传的图片格式。'
            }
        },
        shortcuts: {
            title: '快捷键',
            textFormat: '文本格式',
            markdownSyntax: 'Markdown 语法',
            canvas: '画布',
            fontFormat: '文字格式',
            markdown: {
                heading1: '标题1',
                heading2: '标题2',
                heading3: '标题3',
                heading4: '标题4',
                heading5: '标题5',
                heading6: '标题6',
                bold: '**粗体**',
                italic: '*斜体*',
                strikethrough: '~~删除线~~'
            },
            actions: {
                zoom: '缩放',
                scroll: '滚动',
                actualSize: '实际大小',
                insertSubset: '插入子集',
                insertSibling: '插入同级',
                undo: '撤销',
                redo: '重做',
                quickInsert: '快速插入菜单',
                saveDraft: '保存草稿',
                increaseIndent: '增加缩进',
                decreaseIndent: '减少缩进',
                emoji: '表情符号',
                mention: '提及',
                bold: '粗体',
                italic: '斜体',
                underline: '下划线',
                inlineCode: '行内代码',
                selectCell: '选择单元格',
                selectCellRange: '选择单元格区域',
                multiSelectCell: '多选单元格',
                strikethrough: '删除线',
                orderedList: '有序列表',
                unorderedList: '无序列表',
                todoList: '待办列表',
                codeBlock: '代码块',
                divider: '分割线',
                editBoard: '编辑画板',
                softEnter: '换行(软回车)',
                expandCollapseNode: '展开/折叠节点'
            }
        },
        material: {
            use: '使用',
            useCase: '使用场景',
            library: '素材库',
            all: '全部素材'
        },
        preview: {
            createItem: '创建事项',
            comment: '发起评论'
        },
        search: {
            noResult: '未搜索到任何数据',
            replace: {
                next: '下一个',
                previous: '上一个',
                replace: '替换',
                replaceAll: '全部替换',
                input: '请输入',
                replaceTo: '替换为',
                find: '查找'
            }
        },
        plugins: {
            alert: {
                backgroundColor: '背景色',
                deleteEmoji: '删除表情',
                addEmoji: '添加表情',
                danger: '危险'
            },
            attachment: {
                linkFailed: '文件关联失败，通过附件列表关联或重新上传',
                deleted: '文件已被删除',
                select: '选择已有文件',
                upload: '从本地上传'
            },
            diagramBoard: {
                exportImage: '导出图片'
            },
            formula: {
                input: '输入 LaTeX 公式',
                empty: '公式不能为空',
                invalid: '非法 LaTeX 公式'
            },
            label: {
                inputName: '输入标签名称',
                set: '设置标签',
                pluginName: '标签'
            },
            layout: {
                twoColumns: '两列',
                threeColumns: '三列',
                leftSidebar: '左边栏',
                rightSidebar: '右边栏',
                bothSidebars: '左右边栏'
            },
            outline: {
                list: '列表',
                showNodes: '显示节数',
                level1: '一级',
                level2: '二级',
                level3: '三级',
                level4: '四级',
                levelTitle: '大纲层级',
                options: '大纲选项'
            },
            toggleList: {
                clickToInput: '点击输入内容',
                inputTitle: '输入折叠标题',
                placeholder: '请输入...'
            },
            textDiagram: {
                templates: {
                    sequence: {
                        name: '时序图(Sequence)',
                        actors: {
                            user: '用户',
                            browser: '浏览器',
                            server: '服务端'
                        },
                        actions: {
                            inputUrl: '输入 URL',
                            requestServer: '请求服务器',
                            renderTemplate: '模板渲染',
                            returnHtml: '返回 HTML',
                            comment: '这是一个注释'
                        }
                    },
                    useCase: {
                        name: '用例图(Use Case)'
                    },
                    class: {
                        name: '类图(Class)'
                    },
                    flow: {
                        name: '流程图(Flow)'
                    },
                    activity: {
                        name: '活动图(Activity)'
                    },
                    component: {
                        name: '组件图(Component)'
                    },
                    state: {
                        name: '状态图(State)'
                    },
                    object: {
                        name: '对象图(Object)'
                    }
                }
            }
        },
        plaitEditor: {
            toolbar: {
                image: '图片',
                arrow: {
                    endpoint: '终点',
                    convert: '转换',
                    startPoint: '起点',
                    border: '边框',
                    type: '类型'
                },
                common: {
                    fill: '节点填充',
                    shape: '切换图形',
                    link: {
                        tooltip: '链接',
                        urlPlaceholder: '请输入链接',
                        textPlaceholder: '请输入文本',
                        invalidUrl: '请输入正确的链接'
                    },
                    node: {
                        fill: '节点填充',
                        border: '节点边框'
                    },
                    text: {
                        alignment: '对齐方式',
                        color: '字体颜色'
                    }
                },
                draw: {
                    border: '边框',
                    fill: '填充'
                },
                mind: {
                    summary: '概要',
                    nodeShape: '节点形状',
                    branch: '分支',
                    structure: '结构'
                },
                multiple: {
                    border: '边框',
                    fill: '填充'
                },
                swimlane: {
                    border: '边框',
                    fill: '填充',
                    lanes: '泳道数'
                }
            },
            shape: {
                recentlyUsed: '最近使用'
            },
            mainToolbar: {
                connection: '连线',
                mindmap: '思维导图 M'
            },
            rightTopToolbar: {
                themeColor: '主题配色',
                changeTheme: '更换主题'
            },
            settingPanel: {
                borderOther: {
                    width: '粗细',
                    color: '边框颜色'
                },
                nodeOther: {
                    shape: '节点形状'
                },
                text: {
                    alignment: '对齐方式',
                    color: '文本颜色'
                },
                draw: {
                    fillColor: '填充颜色',
                    shape: '形状'
                },
                mind: {
                    branchType: '分支类型',
                    layout: '结构'
                },
                distribution: '分布',
                border: '边框',
                swimlane: '泳道',
                endpoint: '端点',
                connection: '连线',
                node: '节点',
                branch: '分支',
                displayMode: '展示方式',
                previewConfig: '预览页配置',
                borderWidth: '边框粗细',
                displayModeEnum: {
                    auto: '自动适配',
                    sameAsEdit: '与编辑一致'
                }
            },
            arrangement: {
                leftAlign: '左对齐',
                centerAlign: '左右居中',
                verticalDistribute: '垂直均分'
            },
            contextMenu: {
                addText: '添加文字',
                rightAlign: '右对齐',
                horizontalDistribute: '水平均分',
                topAlign: '顶对齐',
                middleAlign: '上下居中',
                bottomAlign: '底对齐',
                addShape: '添加图形',
                addLine: '添加连线',
                addMindmap: '添加思维导图',
                paste: '粘贴',
                cut: '剪切',
                copy: '拷贝',
                arrange: '排列',
                addSummary: '添加概要',
                child: '子级',
                sibling: '同级'
            },
            group: {
                group: '成组',
                ungroup: '取消成组'
            },
            geometry: {
                basic: {
                    title: '基础图形',
                    rectangle: '矩形',
                    roundRectangle: '圆⻆矩形',
                    circle: '圆形',
                    triangle: '三⻆形',
                    diamond: '菱形',
                    parallelogram: '平行四边形',
                    trapezoid: '梯形',
                    cross: '十字',
                    pentagon: '五边形',
                    hexagon: '六边形',
                    octagon: '八边形',
                    star: '五⻆星',
                    annotation: '标注',
                    cloud: '云朵'
                },
                arrow: {
                    left: '左箭头',
                    right: '右箭头',
                    bidirectional: '双向箭头',
                    pentagon: '五边箭头',
                    flow: '流程箭头'
                },
                swimlane: {
                    vertical: '垂直泳道',
                    horizontal: '水平泳道'
                },
                flowchart: {
                    process: '过程',
                    decision: '决策',
                    terminator: '起止符',
                    predefinedProcess: '预定义流程',
                    document: '多文档',
                    manualInput: '手动输入',
                    preparation: '准备',
                    data: '数据',
                    database: '数据库',
                    hardDisk: '硬盘',
                    internalStorage: '内部存储',
                    manualLoop: '手动循环',
                    delay: '延迟',
                    storedData: '存储数据',
                    connector: '连接器',
                    or: '汇总连接',
                    offPageConnector: '页外连接符'
                },
                uml: {
                    note: '批注',
                    package: '包',
                    frame: '组合片段',
                    comment: '注释',
                    container: '容器',
                    class: '类',
                    activeClass: '活动类',
                    simpleClass: '简单类',
                    interface: '接口',
                    reflection: '反射',
                    requirement: '需求接口',
                    provided: '预留接口',
                    port: '端口',
                    fork: '分支合并',
                    component: '组件盒'
                },
                text: {
                    bold: '加粗',
                    leftAlign: '左对齐',
                    centerAlign: '居中对齐',
                    rightAlign: '右对齐'
                }
            },
            zIndex: {
                bottom: '置底',
                forward: '上移一层',
                backward: '下移一层'
            }
        },
        empty: {
            space: {
                title: '无法访问空间',
                subtitle: '空间不存在或者已过期'
            },
            page: {
                title: '无法访问页面',
                subtitle: '页面不存在或者已过期'
            }
        },
        password: {
            title: '访问密码',
            placeholder: '请输入密码',
            required: '请输入访问密码',
            verifyFailed: '访问密码验证失败，请重新输入'
        },
        pageNav: {
            previous: '上一篇：',
            next: '下一篇：',
            empty: '无'
        },
        directoryTree: {
            title: '目录'
        },
        outside: {
            signup: '注册',
            page: {
                empty: '无'
            }
        },
        aiTable: {
            dataPickerPlaceholder: '选择日期',
            linkTooltip: '链接',
            invalidLinkFormat: '链接格式不正确',
            linkRequired: '链接不能为空',
            linkText: '文本',
            linkUrl: '链接',
            inputText: '输入文本',
            inputUrl: '输入链接',
            fieldColumnName: '表格列名',
            fieldColumnNamePlaceholder: '输入列名称',
            fieldType: '列类型',
            allowMultipleMembers: '允许选择多个成员',
            cancel: '取消',
            apply: '应用',
            fieldNameRequired: '列名不能为空',
            fieldNameDuplicate: '列名已存在',
            viewNameExist: '视图名称已存在',
            confirm: '确定',
            copiedCells: '已复制 {count} 个单元格',
            invalidPasteContent: '粘贴内容不符合当前类型',
            pasteOverMaxRecords: '粘贴内容超过最大记录数',
            pasteOverMaxFields: '粘贴内容超过最大字段数',
            fieldTypeText: '单行文本',
            fieldTypeRichText: '多行文本',
            fieldTypeSelect: '单选',
            fieldTypeMultiSelect: '多选',
            fieldTypeNumber: '数字',
            fieldTypeDate: '日期',
            fieldTypeMember: '成员',
            fieldTypeProgress: '进度',
            fieldTypeRate: '评分',
            fieldTypeLink: '链接',
            fieldTypeAttachment: '附件',
            fieldTypeCreatedBy: '创建人',
            fieldTypeCreatedAt: '创建时间',
            fieldTypeUpdatedBy: '更新人',
            fieldTypeUpdatedAt: '更新时间',
            copyField: '复制列',
            removeRecords: '删除行',
            copy: '复制',
            duplicateView: '复制视图',
            copySuffix: '副本',
            paste: '粘贴',
            tableView: '表格视图',
            editField: '编辑列',
            removeField: '删除列',
            sort: {
                title: '排序',
                autoSort: '自动排序',
                optionAsc: '选项正序',
                optionDesc: '选项倒序',
                numberAsc: '按 1 → 9 排序',
                numberDesc: '按 9 → 1 排序',
                selectAsc: '按选项正序排序',
                selectDesc: '按选项倒序排序',
                defaultAsc: '按 A → Z 排序',
                defaultDesc: '按 Z → A 排序',
                selectColumn: '选择一列进行排序'
            },
            filter: {
                byColumn: '按本列筛选',
                removeCondition: '取消筛选'
            },
            insertUpward: '向上插入',
            insertDownward: '向下插入',
            upward: '行',
            downward: '行',
            fieldGroupBase: '基础',
            fieldGroupAdvanced: '高级',
            rowAddFilterTooltip: '本记录已被筛选过滤，点击该记录以外位置将被隐藏',
            none: '不展示',
            countAll: '记录总数',
            countAllResult: '{{statValue}} 条记录',
            filled: '已填写数',
            filledResult: '已填写 {{statValue}}',
            empty: '未填写数',
            emptyResult: '未填写 {{statValue}}',
            unique: '唯一数',
            uniqueResult: '唯一数 {{statValue}}',
            percentFilled: '已填写占比',
            percentFilledResult: '已填写 {{statValue}}%',
            percentEmpty: '未填写占比',
            percentEmptyResult: '未填写 {{statValue}}%',
            percentUnique: '唯一占比',
            percentUniqueResult: '唯一 {{statValue}}%',
            sum: '求和',
            sumResult: '求和 {{statValue}}',
            max: '最大值',
            maxResult: '最大值 {{statValue}}',
            min: '最小值',
            minResult: '最小值 {{statValue}}',
            average: '平均值',
            averageResult: '平均值 {{statValue}}',
            checked: '已勾选',
            checkedResult: '已勾选 {{statValue}}',
            unChecked: '未勾选',
            unCheckedResult: '未勾选 {{statValue}}',
            percentChecked: '已勾选占比',
            percentCheckedResult: '已勾选 {{statValue}}%',
            percentUnChecked: '未勾选占比',
            percentUnCheckedResult: '未勾选 {{statValue}}%',
            earliestTime: '最早时间',
            earliestTimeResult: '最早时间 {{statValue}}',
            latestTime: '最晚时间',
            latestTimeResult: '最晚时间 {{statValue}}',
            dateRangeOfDays: '时间范围(日)',
            dateRangeOfDaysResult: '时间范围 {{statValue}} 天',
            dateRangeOfMonths: '时间范围(月)',
            dateRangeOfMonthsResult: '时间范围 {{statValue}} 月',
            selectedRecordsCount: '已经选择 {count} 条记录',
            selectedCellsCount: '已经选择 {count} 个单元格',
            stat: '统计',
            fieldTypeCheckbox: '复选框'
        }
    }
};

export type I18nSourceDefinitionType = NestedKey<typeof i18nSourceDefinition>;
