import { Id, Is, PilotScopeType, PrincipalMemberItem, Timestamp, Visibility } from '@atinc/ngx-styx';
import { StyxAddonInfo } from '@atinc/ngx-styx/addon';
import { PageInfo } from './page-info';

export class SpaceInfo {
    _id?: string;

    name?: string;

    name_pinyin?: string;

    description?: string;

    checkpoints?: SpaceCheckpoints;

    icon?: string;

    color?: string;

    identifier?: string;

    members?: Array<any>;

    members_be_user_group?: PrincipalMemberItem[];

    is_favorite?: number;

    is_archived?: number;

    is_shared?: Is;

    type?: string;

    permissions?: string;

    index?: PageInfo;

    created_by?: string;

    created_at?: number;

    updated_by?: string;

    updated_at?: number;

    shortId?: string;

    expired_at?: string;

    enable_expired?: number;

    expired?: {
        date: Timestamp;
        with_time: Is;
    };

    scope_type?: PilotScopeType;

    visibility?: Visibility;

    user_group_ids?: string[];

    addons?: StyxAddonInfo[];

    category_id?: Id;
}

export class SpaceSettingInfo {
    title?: string;
    logo?: string; // 上传logo返回的id
    is_show_copyright?: Is;
    is_show_all_pages?: Is;
    shared_pages?: Id[];
}

export interface SpaceManageInfo extends SpaceInfo {
    memberCount?: number;
}

export interface SpaceCheckpoints {
    space_member_setting: Is;
}

export type WikiSpaceCheckpoints = 'space_member_setting';

export interface SpaceOverviewInfo {
    total_count: number;
    weekly_new_count: number;
    document_count: number;
    board_count: number;
    table_count: number;
}
