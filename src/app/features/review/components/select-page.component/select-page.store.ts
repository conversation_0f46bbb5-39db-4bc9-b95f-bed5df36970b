import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { PilotBaseEntity, ResponseData, StyxReviewSelectionConfirmStore } from '@atinc/ngx-styx';
import { StyxComplexSelectionModel, StyxRelationSelectionStore, StyxRelationSelectionSuiteInfo } from '@atinc/ngx-styx/relation';
import { Action, PaginationInfo } from '@tethys/store';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceApiService } from '@wiki/app/services';
import { ReviewApiService } from '@wiki/app/services/review-api.service';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { SafeAny } from 'ngx-tethys/types';
import { of } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class WikiReviewSelectPageStore extends StyxRelationSelectionStore {
    pilotInfo: PilotBaseEntity;

    suites: ThyTreeNodeData<StyxRelationSelectionSuiteInfo>;

    private spaceApiService = inject(SpaceApiService);
    private reviewApiService = inject(ReviewApiService);
    private selectionConfirmStore = inject(StyxReviewSelectionConfirmStore);

    constructor(public http: HttpClient) {
        super(http);
    }

    initializePilot(pilot: PilotBaseEntity): any {
        this.pilotInfo = pilot;
    }

    fetchSelectablePilots = (keywords?: string) => {
        return this.spaceApiService.fetchSpace(this.pilotInfo?._id).pipe(
            map((data: ResponseData) => {
                this.suites = [data.value];
                return [data.value];
            })
        );
    };

    fetchSelectableItems = (params?: SafeAny) => {
        const spaceId = params.view.pilot_ids[0];
        return this.reviewApiService.fetchReviewPageTree(spaceId).pipe(
            map((data: PageInfo[]) => {
                this.initializeWithReferences(data as PageInfo & { _id: string }[], null, {
                    pageIndex: 1,
                    pageSize: data.length,
                    pageCount: data.length,
                    count: data.length
                } as PaginationInfo);
                return data;
            })
        );
    };

    @Action()
    fetchSelectedPageIds() {
        const ids = this.selectionConfirmStore.snapshot?.entities?.map(item => item._id) ?? [];
        if (!this.selectionConfirmStore.snapshot?.reviewMode?.instanceId) {
            return of(ids);
        }
        return this.reviewApiService
            .fetchReviewSelectedPageIds(
                this.pilotInfo?._id,
                this.selectionConfirmStore.snapshot?.reviewMode?.instanceId,
                this.selectionConfirmStore.snapshot?.reviewMode?.subjectType
            )
            .pipe(
                map((data: ResponseData) => {
                    return [...ids, ...data.value];
                })
            );
    }

    @Action()
    relateItems(selections: StyxComplexSelectionModel) {
        return this.selectionConfirmStore.savePlaceholderObjects(selections);
    }
}
