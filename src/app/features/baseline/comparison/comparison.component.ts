import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    HostBinding,
    inject,
    Inject,
    OnInit,
    Optional,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import {
    ComparisonEntityInfo,
    Direction,
    PilotEntryStore,
    PropertyType,
    ResponseData,
    STYX_BASELINE_COMPARISON_PRINCIPAL_TABLE_STORE_TOKEN,
    STYX_BASELINE_COMPARISON_SUBORDINATE_TABLE_STORE_TOKEN,
    StyxBaselineComparisonComponent,
    StyxBaselineComparisonTableType,
    StyxBaselinesStore,
    StyxPropertyIdentifier,
    StyxPropertySchema,
    StyxTableRow,
    StyxTagApiService,
    StyxTagScene,
    StyxTranslateService,
    StyxViewQueryService,
    StyxViewQueryStore,
    WikiBroadObjectTypes,
    css,
    helpers
} from '@atinc/ngx-styx';
import { PropertyItemPageTypeDisplayerComponent } from '@wiki/app/components/page-type/page-type.component';
import { API_PREFIX } from '@wiki/app/constants';
import { DISABLED_PROPERTY } from '@wiki/app/constants/baseline';
import { PageInfo } from '@wiki/app/entities/page-info';
import { PageApiService } from '@wiki/app/services';
import { IsPagePipe } from '@wiki/common/pipe/page.pipe';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { SafeAny } from 'ngx-tethys/types';
import { map, tap } from 'rxjs';
import { PageHistoryComponent } from '../../page/components';
import { IsPlanPagePipe } from '../pipes/baseline.pipe';
import { WikiBaselineOperationService } from '../services/operation.service';
import { BaselineComparisonSubordinateStore } from '../stores/comparison-subordinate.store';
import { BaselineComparisonStore } from '../stores/comparison.store';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-baseline-comparison',
    templateUrl: './comparison.component.html',
    providers: [
        StyxViewQueryService,
        StyxViewQueryStore,
        {
            provide: STYX_BASELINE_COMPARISON_PRINCIPAL_TABLE_STORE_TOKEN,
            useClass: BaselineComparisonStore
        },
        {
            provide: STYX_BASELINE_COMPARISON_SUBORDINATE_TABLE_STORE_TOKEN,
            useClass: BaselineComparisonSubordinateStore
        }
    ],
    standalone: false
})
export class WikiBaselineComparisonComponent implements OnInit, AfterViewInit {
    private translate = inject(StyxTranslateService);

    @HostBinding('class') className = `${css.layout} wiki-baseline-comparison`;

    customSchemas: Record<StyxPropertyIdentifier, StyxPropertySchema> = {};

    baselineId: string;

    sortConfig = {
        sortBy: 'position',
        sortDirection: Direction.default
    };

    private comparisonData: { planPrincipals: PageInfo[]; planSubordinates: PageInfo[] };

    get apiPrefix(): string {
        return `${API_PREFIX}/pages`;
    }

    @ViewChild(StyxBaselineComparisonComponent)
    comparisonComponent: StyxBaselineComparisonComponent;

    sortMenusAction = () =>
        this.pageApiService
            .fetchSortProperties()
            .pipe(map(properties => properties.filter(property => !['principal_version', 'tag_ids'].includes(property.key))));

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private styxViewQueryService: StyxViewQueryService,
        private pageApiService: PageApiService,
        private route: ActivatedRoute,
        private thyDialog: ThyDialog,
        private viewContainerRef: ViewContainerRef,
        private cdr: ChangeDetectorRef,
        private isPagePipe: IsPagePipe,
        private isPlanPagePipe: IsPlanPagePipe,
        private baselineOperationService: WikiBaselineOperationService,
        private pageEventBus: PageEventBus,
        @Optional()
        @Inject(STYX_BASELINE_COMPARISON_PRINCIPAL_TABLE_STORE_TOKEN)
        private principalStore: BaselineComparisonSubordinateStore,
        @Optional()
        @Inject(STYX_BASELINE_COMPARISON_SUBORDINATE_TABLE_STORE_TOKEN)
        private subordinateStore: BaselineComparisonSubordinateStore,
        public baselinesStore: StyxBaselinesStore,
        private styxTagApiService: StyxTagApiService,
        private destroyRef: DestroyRef
    ) {}

    ngOnInit() {
        this.customSchemas = {
            [PropertyType.relationCount]: {
                config: (row: PageInfo) => {
                    return {
                        object_type: WikiBroadObjectTypes.page,
                        principal_id: row?._id
                    };
                }
            },
            [PropertyType.select]: {
                displayer: PropertyItemPageTypeDisplayerComponent
            }
        };
        this.route.parent.paramMap
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                map(params => {
                    return params.get('baselineId');
                })
            )
            .subscribe(baselineId => {
                this.baselineId = baselineId;
                this.initializeStyxViewQuery();
            });
        this.initializeStyxViewQuery();
        this.subscribeGlobalPage();
    }

    ngAfterViewInit(): void {
        this.comparisonComponent?.comparisonStore?.entities$
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: ComparisonEntityInfo[]) => {
                this.buildComparisonData(data);
            });
    }

    private buildComparisonData(data: ComparisonEntityInfo[]) {
        const planPages = this.comparisonComponent?.comparisonTableData?.value;
        const planPrincipals = planPages.filter(page => !!page.principal).map(page => page.principal);
        const planSubordinates = planPages.filter(page => !!page.subordinate).map(page => page.subordinate);
        this.comparisonData = {
            planPrincipals,
            planSubordinates
        };
    }

    private initializeStyxViewQuery() {
        this.styxViewQueryService
            .initialize({
                cacheUniqueKey: `wiki-baseline-comparison-${this.baselineId}`,
                filterMode: 'customize',
                disabledLogic: false,
                selectableProperties: () =>
                    this.pageApiService
                        .fetchProperties()
                        .pipe(map(properties => properties.filter(property => !DISABLED_PROPERTY.includes(property.key)))),
                selectableOptions: {
                    ['tag_ids']: () =>
                        this.styxTagApiService
                            .fetchSceneTags(API_PREFIX, WikiBroadObjectTypes.page, {
                                scene: StyxTagScene.pilot_with_related_global,
                                scope_id: this.pilotStore.snapshot.detail._id
                            })
                            .pipe(map((data: ResponseData) => data.value))
                },
                selectableSearchScopeOptions: [{ key: 'name', name: this.translate.instant<I18nSourceDefinitionType>('styx.title') }]
            })
            .initializeQueryContent({
                search: { scopes: ['name'] }
            });
    }

    comparisonRow(originVersionId: string, targetVersionId: string, principalId: string) {
        this.thyDialog.open(PageHistoryComponent, {
            initialState: {
                title: this.translate.instant<I18nSourceDefinitionType>('wiki.page.info.compare'),
                isVersionComparison: true,
                spaceId: this.pilotStore.snapshot.detail._id,
                pageId: principalId,
                originVersionId,
                targetVersionId
            },
            size: ThyDialogSizes.full,
            viewContainerRef: this.viewContainerRef
        });
    }

    private isPlanPage(row: StyxTableRow) {
        const planPrincipals = this.principalStore.snapshot.entities;
        const planSubordinates = this.subordinateStore.snapshot.entities;
        const isPlan = this.isPlanPagePipe.transform([...planPrincipals, ...planSubordinates], row);
        return isPlan;
    }

    onRowClick = (event: SafeAny) => {
        const { row, tableComponent, type } = event;
        const isPlanPage = this.isPlanPage(row);
        if (this.isPagePipe.transform(row) && isPlanPage) {
            const isSameBaseline = this.comparisonComponent?.baseline?._id === this.comparisonComponent?.selectedTargetBaseline?._id;
            const isSubordinate = type === StyxBaselineComparisonTableType.subordinate;
            const disablePageEdit = !isSameBaseline || !isSubordinate;
            this.baselineOperationService.openBaselinePage(row, disablePageEdit);
        } else {
            tableComponent?.expandChildren(event.event, row);
            this.cdr.detectChanges();
        }
    };

    private containsPlanPage(planPages: any[], row: StyxTableRow) {
        const children = row?.children ?? [];
        for (let i = 0; i < children.length; i++) {
            if (planPages.some(item => item._id === children[i]._id) || this.containsPlanPage(planPages, children[i])) {
                return true;
            }
        }
        return false;
    }

    private getPlanningStatus(type: StyxBaselineComparisonTableType, row: StyxTableRow) {
        const isPrincipal = type === StyxBaselineComparisonTableType.principal;
        const { planPrincipals, planSubordinates } = this.comparisonData;
        const planItems = isPrincipal ? planPrincipals : planSubordinates;
        const isPlan = planItems.some(item => item._id === row._id);
        const isContainsPlanPage = this.containsPlanPage(planItems, row);
        return {
            isPlan,
            isContainsPlanPage
        };
    }

    rowClassName = (type: StyxBaselineComparisonTableType, row: StyxTableRow) => {
        const { isPlan, isContainsPlanPage } = this.getPlanningStatus(type, row);
        let rowClass = '';

        if (this.isPagePipe.transform(row) && !isPlan) {
            rowClass = `${rowClass} wiki-not-plan-page`;
        }
        if (!isContainsPlanPage) {
            rowClass = `${rowClass} table-row-hide`;
        }
        if (!row?.is_visibility) {
            rowClass = `${rowClass} wiki-table-row-disabled`;
        }
        return rowClass;
    };

    isDisplayColumn = (type: StyxBaselineComparisonTableType, propertyKey: string, row: StyxTableRow) => {
        const { isPlan, isContainsPlanPage } = this.getPlanningStatus(type, row);
        const notPlanDisplayColumns = ['name'];

        if (isPlan) {
            return true;
        }
        if (isContainsPlanPage) {
            return notPlanDisplayColumns.includes(propertyKey);
        } else {
            return false;
        }
    };

    subscribeGlobalPage() {
        if (!this.pageEventBus) {
            return;
        }
        this.pageEventBus
            .onPageInfoChange$()
            .pipe(
                tap((page: PageInfo) => {
                    const isSameBaseline =
                        this.comparisonComponent?.baseline?._id === this.comparisonComponent?.selectedTargetBaseline?._id;
                    const subordinatePage = this.subordinateStore.snapshot.entities.find(item => item._id === page._id);
                    const isSpace = page?.space_id === subordinatePage?.space_id;
                    if (isSameBaseline && isSpace && page && !!page?.is_published) {
                        this.subordinateStore.update(page._id, {
                            ...subordinatePage,
                            name: page.name,
                            emoji_icon: page.emoji_icon,
                            updated_at: page.updated_at,
                            updated_by: page.updated_by,
                            created_at: page.created_at,
                            created_by: page.created_by,
                            version_id: page?.version_id,
                            version_name: page?.version_name,
                            principal_version: page?.version_name
                        });
                    }

                    const parents: PageInfo[] = (this.comparisonComponent?.comparisonStore?.snapshot.references as any)?.principal_parents;
                    const parentPage: PageInfo = helpers.find(parents, { _id: page._id });

                    if (parentPage && page?.space_id === parentPage?.space_id) {
                        this.comparisonComponent?.comparisonStore?.updateWithReferences(
                            [],
                            {},
                            {
                                principal_parents: parents.map(item => {
                                    if (item._id === page._id) {
                                        return helpers.merge(item, page);
                                    }
                                    return item;
                                })
                            }
                        );
                    }
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe();
    }
}
