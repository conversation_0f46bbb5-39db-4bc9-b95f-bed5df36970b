import { ChangeDetectorRef, Component, DestroyRef, OnInit, TemplateRef, ViewChild, ViewContainerRef, inject } from '@angular/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import {
    Direction,
    GlobalUsersStore,
    PropertyInfo,
    PropertyType,
    ResponseData,
    SortConfigInfo,
    StyxBaselineApiService,
    StyxBaselinePivotsQuery,
    StyxBaselineService,
    StyxBaselineStatusType,
    StyxBaselinesStore,
    StyxColumnsStore,
    StyxPropertyService,
    StyxSortMenuComponent,
    StyxTableEmitEvent,
    StyxTagApiService,
    StyxTagScene,
    StyxTranslateService,
    StyxViewQueryService,
    UtilService,
    WikiBroadObjectTypes,
    cache,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { API_PREFIX } from '@wiki/app/constants';
import { DISABLED_PROPERTY } from '@wiki/app/constants/baseline';
import { PageInfo, PageSelectionFrom } from '@wiki/app/entities/page-info';
import { PageApiService } from '@wiki/app/services';
import { PageService } from '@wiki/app/services/util/page.service';
import { filterAndSortPageData } from '@wiki/app/util/tree';
import { IsPagePipe } from '@wiki/common/pipe/page.pipe';
import { ThySidebar } from 'ngx-tethys/layout';
import { ThyPopover } from 'ngx-tethys/popover';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { finalize, forkJoin, map, skip } from 'rxjs';
import { IsPlanPagePipe } from '../pipes/baseline.pipe';
import { WikiBaselineFeedService } from '../services/baseline-feed.service';
import { WikiBaselineOperationService } from '../services/operation.service';
import { BaselinePagesStore } from '../stores/pages.store';
import { BaselinePlanPagesStore } from '../stores/plan-pages.store';

@Component({
    selector: 'wiki-baseline-pivots',
    templateUrl: './pages.component.html',
    providers: [StyxColumnsStore, BaselinePagesStore, BaselinePlanPagesStore],
    host: {
        class: `thy-layout thy-layout-has-sidebar baseline-pages`
    },
    standalone: false
})
export class WikiBaselinePagesComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    loadingDone: boolean = true;

    sortConfig: SortConfigInfo = {
        sortBy: 'name',
        sortDirection: Direction.default
    };

    pageNodes: ThyTreeNodeData[] = [];

    baselineStatusType = StyxBaselineStatusType;

    private baselineId: string;

    private pages: PageInfo[] = [];

    private sortColumns: PropertyInfo[];

    private pageCacheKey: string;

    public isFlatMode = false;

    @ViewChild(ThySidebar, { static: true }) sidebarComponent: ThySidebar;

    @ViewChild('moreMenuTemplate', { static: true }) moreMenuTemplate: TemplateRef<any>;

    get baselineIsOpened() {
        return this.baselinesStore?.snapshot?.activeBaseline?.status === StyxBaselineStatusType.open;
    }

    get baselineIsComplete() {
        return this.baselinesStore?.snapshot?.activeBaseline?.status === StyxBaselineStatusType.complete;
    }

    get space() {
        return this.pilotStore?.snapshot?.detail;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public usersStore: GlobalUsersStore,
        public styxColumnsStore: StyxColumnsStore,
        public baselinesStore: StyxBaselinesStore,
        public pagesStore: BaselinePagesStore,
        private util: UtilService,
        private cdr: ChangeDetectorRef,
        private route: ActivatedRoute,
        private styxViewQueryService: StyxViewQueryService,
        private thyPopover: ThyPopover,
        private viewContainerRef: ViewContainerRef,
        private baselineService: StyxBaselineService,
        private pageApiService: PageApiService,
        private baselineOperationService: WikiBaselineOperationService,
        private baselineFeedService: WikiBaselineFeedService,
        private pageService: PageService,
        private baselineApiService: StyxBaselineApiService,
        private baselinePlanPagesStore: BaselinePlanPagesStore,
        private isPlanPagePipe: IsPlanPagePipe,
        private isPagePipe: IsPagePipe,
        private styxTagApiService: StyxTagApiService,
        private destroyRef: DestroyRef,
        private styxPropertyService: StyxPropertyService
    ) {
        this.registerProperties();
    }

    registerProperties() {
        (this.styxPropertyService as any).componentsKeyMap[PropertyType.tag].config = {
            broadObjectType: WikiBroadObjectTypes.page
        };
    }

    ngOnInit() {
        this.initializeColumnConfig();
        this.fetchSortColumns();
        this.routeParamMap();
        this.changeBaselineStatus();
        this.viewQuery();
        this.planPageCompleted();

        this.pagesStore.entities$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(pages => {
            const principal_parents = this.pagesStore.snapshot?.references?.principal_parents ?? [];
            let pageNodes = pages;
            if (!this.isFlatMode) {
                pageNodes.push(...principal_parents);
                this.pages = pageNodes;
                this.buildPageNodes();
            } else {
                this.pages = pageNodes;
                this.pageNodes = pageNodes;
            }
        });
    }

    private routeParamMap() {
        this.route.parent.paramMap
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                map(params => {
                    return params.get('baselineId');
                })
            )
            .subscribe(baselineId => {
                this.baselineId = baselineId;
                this.pageCacheKey = `wiki:baseline-${baselineId}-pages`;

                const cacheSortConfig = this.getCache();
                if (cacheSortConfig) {
                    this.sortConfig = cacheSortConfig;
                }
                this.initializeStyxViewQuery();
            });
    }

    private changeBaselineStatus() {
        this.baselinesStore
            .select$(state => state.activeBaseline?.status)
            .pipe(skip(1), takeUntilDestroyed(this.destroyRef))
            .subscribe(activeBaselineStatus => {
                if (activeBaselineStatus === StyxBaselineStatusType.complete) {
                    this.pagesStore.pureCompleteBaseline();
                }
            });
    }

    private viewQuery() {
        this.styxViewQueryService.queryStore.configs$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(data => {
            this.isFlatMode =
                !!data?.search?.keywords || data?.conditions.length > 0 || this.sortConfig?.sortDirection !== Direction.default;
            this.fetchBaselinePages();
        });
    }

    private planPageCompleted() {
        // 接收大数据量规划完成的 feed 后关闭弹窗
        this.baselineFeedService.planPageCompletedFeed$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
            this.fetchBaselinePages();
            this.util.dialog.close();
        });
    }

    private initializeColumnConfig() {
        this.styxColumnsStore.config({
            apiPrefix: this.baselineService.baselineDetailColumnApiPrefix,
            scopeId: `${this.space._id}`,
            lockMax: 5,
            frozenColumns: ['name'],
            unremovableColumns: ['name']
        });
    }

    buildPageNodes(keywords?: string) {
        const parentPages = this.pagesStore.snapshot.references?.principal_parents ?? [];
        const pages = this.pages.map(page => {
            return {
                ...page,
                hideColumn: parentPages.some(item => item._id === page._id)
            };
        });
        const nodes = filterAndSortPageData<ThyTreeNodeData>(
            pages,
            keywords ?? '',
            this.sortConfig,
            parentPages.map(item => item._id)
        );
        this.pageNodes = keywords ? nodes.filter(node => this.isPagePipe.transform({ type: node.type })) : nodes;
    }

    private fetchBaselinePages() {
        this.loadingDone = false;
        this.cdr.detectChanges();
        const queryParams: StyxBaselinePivotsQuery = {
            ...this.styxViewQueryService.queryStore.snapshot.configs,
            sort_by: this.sortConfig?.sortBy,
            sort_direction: this.sortConfig?.sortDirection
        };
        forkJoin([this.pagesStore.fetchPrincipals(this.baselineId, queryParams), this.styxColumnsStore.fetchColumns()])
            .pipe(
                finalize(() => {
                    this.loadingDone = true;
                    this.cdr.markForCheck();
                })
            )
            .subscribe({
                next: () => {},
                error: error => {
                    this.util.defaultErrorHandler()(error);
                }
            });
    }

    private initializeStyxViewQuery() {
        this.styxViewQueryService
            .initialize({
                cacheUniqueKey: `wiki-baseline-pages-${this.baselineId}`,
                filterMode: 'customize',
                disabledLogic: false,
                selectableOptions: {
                    ['tag_ids']: () =>
                        this.styxTagApiService
                            .fetchSceneTags(API_PREFIX, WikiBroadObjectTypes.page, {
                                scene: StyxTagScene.pilot_with_related_global,
                                scope_id: this.space._id
                            })
                            .pipe(map((data: ResponseData) => data.value))
                },
                selectableSearchScopeOptions: [{ key: 'name', name: this.translate.instant<I18nSourceDefinitionType>('styx.title') }],
                selectableProperties: () =>
                    this.pageApiService
                        .fetchProperties()
                        .pipe(map(properties => properties.filter(property => !DISABLED_PROPERTY.includes(property.key))))
            })
            .initializeQueryContent({
                search: { scopes: ['name'] }
            });
    }

    private fetchSortColumns() {
        this.pageApiService
            .fetchSortProperties()
            .pipe(map(properties => properties.filter(property => !['principal_version', 'tag_ids'].includes(property.key))))
            .subscribe({
                next: (data: PropertyInfo[]) => {
                    this.sortColumns = data;
                },
                error: error => this.util.defaultErrorHandler()(error)
            });
    }

    /**
     * 规划页面
     */
    openPlanPage(event: Event) {
        const selectedPageIds = this.pagesStore.snapshot.entities.map(item => item._id);
        this.pageService.openPageSelection({
            from: PageSelectionFrom.baseline,
            selectMultiple: true,
            isParentChildLinkageEnabled: true,
            showEditableVersionColumn: true,
            selectedSpaceId: this.space._id,
            selectedIds: selectedPageIds,
            freezeIds: selectedPageIds,
            baselineId: this.baselineId,
            fetchSelectablePageVersions$: (pageId: string) => {
                return this.baselinePlanPagesStore.fetchVersions(pageId);
            },
            confirmHandle: (selectPages: PageInfo[]) => {
                if (selectPages.length !== 0) {
                    this.baselineApiService
                        .planPrincipals(this.baselineId, {
                            selectedObjects: selectPages
                        })
                        .subscribe({
                            next: () => {
                                this.fetchBaselinePages();
                            },
                            error: error => {
                                this.util.defaultErrorHandler()(error);
                            }
                        });
                }
            }
        });
    }

    openSort(event: Event) {
        this.thyPopover.open(StyxSortMenuComponent, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            insideClosable: true,
            originActiveClass: 'active',
            viewContainerRef: this.viewContainerRef,
            initialState: {
                sortMenus: this.sortColumns,
                sortBy: this.sortConfig.sortBy,
                sortDirection: this.sortConfig.sortDirection,
                onSort: (e: Event, sortBy: string, sortDirection: Direction) => {
                    this.onSort(sortBy, sortDirection);
                }
            }
        });
    }

    private initialSortConfig(sortBy: string, direction: Direction) {
        this.sortConfig = {
            sortBy,
            sortDirection: direction
        };
        this.setCache(this.sortConfig);
    }

    private getCache(): SortConfigInfo {
        return cache.get(`${this.pageCacheKey}`) || null;
    }

    private setCache(cacheConfig: SortConfigInfo) {
        cache.set(`${this.pageCacheKey}`, { ...(this.getCache() || {}), ...cacheConfig });
    }

    onSort = (sortBy: string, direction: Direction) => {
        this.isFlatMode = direction !== Direction.default;
        this.initialSortConfig(sortBy, direction);
        this.fetchBaselinePages();
    };

    onRowClick = (event: StyxTableEmitEvent) => {
        const row = event.row;
        this.baselineOperationService.openBaselinePage(row, true);
    };

    moreActionsResolve = (row: PageInfo) => {
        return this.isPlanPagePipe.transform(this.pagesStore.snapshot.entities, row);
    };

    openMoreMenu = (event: Event, row: PageInfo) => {
        this.util.popover.open(this.moreMenuTemplate, {
            initialState: {
                row
            },
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            insideClosable: true,
            originActiveClass: 'active',
            viewContainerRef: this.viewContainerRef
        });
    };

    removePages(selections: string[]) {
        this.baselineOperationService.removePagesFromBaseline(this.baselineId, selections, () => {
            this.fetchBaselinePages();
        });
    }

    removePage(row: PageInfo) {
        const baselinePage = this.pagesStore.snapshot.entities.find(item => item._id === row._id);
        this.baselineOperationService.removePagesFromBaseline(this.baselineId, baselinePage, () => {
            this.fetchBaselinePages();
        });
    }
}
