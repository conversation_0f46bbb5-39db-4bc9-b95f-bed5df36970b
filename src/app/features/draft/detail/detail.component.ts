import { AfterViewInit, ChangeDetectorRef, Component, computed, DestroyRef, HostBinding, inject, OnInit, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { Id, Is, PilotEntryStore, css, helpers } from '@atinc/ngx-styx';
import { DialogDispatcher } from '@wiki/common/services';
import { PageInfo } from '@wiki/app/entities/page-info';
import { PageContext } from '@wiki/app/services/context/page.context';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { WikiPageContent } from '@wiki/common/interface/page';
import { IsDocumentPipe } from '@wiki/common/pipe/page.pipe';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { PageDocumentData, PageEventBus } from '@wiki/common/services/page-event-bus';
import { PageStore } from '@wiki/common/stores/page.store';
import { initPageStoreProviders } from '@wiki/common/util/init-page-store-provider';
import { THE_MODE_PROVIDER } from '@worktile/theia';
import { of } from 'rxjs';
import { catchError, finalize } from 'rxjs/operators';
import { PageDraftService } from '../../page/services/page-draft.service';
import { PageDraftDetailStore } from './page-draft-detail.store';
import { WikiCommonPreviewComponent } from '@wiki/common/components/preview/preview.component';

@Component({
    selector: 'wiki-page-draft-detail',
    templateUrl: './detail.component.html',
    providers: [
        THE_MODE_PROVIDER,
        PageDraftService,
        {
            provide: WikiPluginContext,
            useClass: PageContext
        },
        DialogDispatcher,
        ...initPageStoreProviders(PageDraftDetailStore)
    ],
    standalone: false
})
export class PageDraftDetailComponent implements OnInit, AfterViewInit {
    @HostBinding(`class`)
    className = `${css.layout} ${css.displayFlexRowFill} flex-row draft-detail-container`;

    commonDocumentContentContainerClass = 'common-document-content-container';

    public loadingDone = false;

    public spaceIdentifier: string;

    private draftIdOrShortId: string;

    hasContentToc = false;

    isCollapsedToc = true;

    get draftPage(): PageInfo {
        return this.pageDraftDetailStore.snapshot.page;
    }

    get pageContent(): WikiPageContent {
        return this.pageDraftDetailStore.snapshot.content;
    }

    readonly editor = computed(() => this.wikiCommonPreviewComponent()?.editor());

    private wikiCommonPreviewComponent = viewChild(WikiCommonPreviewComponent);

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        public pagesStore: PagesStore,
        public pageDraftDetailStore: PageStore,
        private pageDraftService: PageDraftService,
        public globalPageStore: PageEventBus,
        private errorService: WikiErrorService,
        private cdr: ChangeDetectorRef,
        private isDocument: IsDocumentPipe,
        private destroyRef: DestroyRef
    ) {}

    ngOnInit() {
        this.spaceIdentifier = this.pilotStore.snapshot.detail.identifier;
        this.draftIdOrShortId = this.route.snapshot.paramMap.get('draftIdOrShortId');
        this.fetchDraftPage();
        this.subscribeDraftPublish();
    }

    ngAfterViewInit() {
        this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            if (params.draftIdOrShortId && this.draftIdOrShortId !== params.draftIdOrShortId) {
                this.draftIdOrShortId = params.draftIdOrShortId;
                this.fetchDraftPage();
            }
        });
    }

    private fetchDraftPage() {
        this.loadingDone = false;
        this.pageDraftDetailStore
            .fetchPage(this.draftIdOrShortId, {
                only_draft: true
            })
            .pipe(
                finalize(() => (this.loadingDone = true)),
                catchError(error => {
                    if (error.code === 404) {
                        this.pageDraftDetailStore.pureClearPage();
                    } else {
                        this.errorService.defaultErrorHandler(error);
                    }
                    return of(false);
                })
            )
            .subscribe();
    }

    subscribeDraftPublish() {
        this.globalPageStore
            .onPageDocumentChange$()
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: PageDocumentData) => {
                if (data.isPublish && (data.shortId === this.draftIdOrShortId || data.pageId === this.draftIdOrShortId)) {
                    this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'pages', this.draftIdOrShortId]);
                }
            });
    }

    breadcrumbSelect(data: { id: Id }) {
        this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'pages', data.id]);
    }

    editPage() {
        const id = this.draftPage.short_id ?? this.draftPage._id;
        this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'draft-pages', id, 'edit']);
    }

    deletePageDraft() {
        this.pageDraftService.deleteDraft(this.spaceIdentifier, this.draftPage);
    }

    publishPage() {
        this.pageDraftService
            .publishDraft(this.draftPage.space_id, this.draftPage._id, Is.no, data => {
                if (
                    this.pagesStore.snapshot.entities[0].space_id === this.draftPage.space_id &&
                    !helpers.find(this.pagesStore.snapshot.entities, { _id: this.draftPage._id })
                ) {
                    this.pagesStore.pureAddPages(data.value);
                }
                this.syncDraftPageStore(data.value);
                this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'pages', this.draftIdOrShortId]);
            })
            .subscribe();
    }

    syncDraftPageStore(value) {
        if (!this.draftPage) {
            return;
        }
        const { published_at, published_by, updated_at, updated_by, is_published } = value;
        let relationContents = {};
        if (this.isDocument.transform({ type: this.draftPage.type })) {
            const {
                relationWorkItemStore,
                relationPageStore,
                relationTestCaseStore,
                relationIdeaStore,
                relationTicketStore,
                relationObjectiveStore
            } = this.pageDraftDetailStore;
            relationContents = {
                relation_pages: relationPageStore.relationPages(),
                relation_work_items: {
                    value: relationWorkItemStore.snapshot.entities,
                    references: relationWorkItemStore.snapshot.references
                },
                relation_test_cases: {
                    value: relationTestCaseStore.snapshot.entities,
                    references: relationTestCaseStore.snapshot.references
                },
                relation_ideas: {
                    value: relationIdeaStore.snapshot.entities,
                    references: relationIdeaStore.snapshot.references
                },
                relation_tickets: {
                    value: relationTicketStore.snapshot.entities,
                    references: relationTicketStore.snapshot.references
                },
                relation_objectives: {
                    value: relationObjectiveStore.snapshot.entities,
                    references: relationObjectiveStore.snapshot.references
                }
            };
        }

        this.globalPageStore.emitPageInfo({
            ...this.pageDraftDetailStore.snapshot.page,
            published_at,
            published_by,
            updated_at,
            updated_by,
            is_published
        });
        this.globalPageStore.emitPageDocument({
            isPublish: true,
            document: this.draftPage.document,
            pageId: this.draftPage._id,
            shortId: this.draftPage.short_id,
            ...relationContents
        });
    }

    // 处理布局

    tocCollapsedChange(collapsed: boolean) {
        this.isCollapsedToc = collapsed;
    }

    headersChange(headers: []) {
        this.hasContentToc = headers.length > 0 ? true : false;
        this.cdr.detectChanges();
    }
}
