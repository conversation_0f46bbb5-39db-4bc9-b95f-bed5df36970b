import {
    Component,
    HostBinding,
    OnInit,
    Input,
    ViewContainerRef,
    ElementRef,
    ViewChild,
    NgZone,
    AfterViewInit,
    inject
} from '@angular/core';
import { Router } from '@angular/router';
import { PilotEntryStore, StyxPivotBaselineInfo } from '@atinc/ngx-styx';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { BASELINE_STATUS, PaginationResponseData, ResponseData, StyxVersionEntity, UtilService } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { Element, Node } from 'slate';
import { PageInfo, PageOperationHistoryEntity } from '@wiki/app/entities/page-info';
import { textics } from '@wiki/common/util/textics';
import { PageApiService } from '@wiki/app/services';
import { PageHistoryComponent } from '../history/history.component';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { debounceTime, finalize, fromEvent } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { IsDocumentPipe } from '@wiki/common/pipe/page.pipe';
import { API_PREFIX } from '@wiki/app/constants';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

interface PageMetaInfo {
    words: number;
    chars: number;
    spaces: number;
}

const SCROLL_BUFFER = 10;

@Component({
    selector: 'wiki-page-info',
    templateUrl: './page-info.component.html',
    providers: [CdkScrollable],
    standalone: false
})
export class PageInfoComponent implements OnInit, AfterViewInit {
    @HostBinding('class') class = 'wiki-page-info';

    @Input() page: PageInfo;

    @Input() node: Element;

    @Input() close: () => void;

    baselineStatus = BASELINE_STATUS;

    activeTabKey: string = 'info';

    pageMetaInfo: PageMetaInfo = {
        words: 0,
        chars: 0,
        spaces: 0
    };

    loadingDone = false;

    baselines: StyxPivotBaselineInfo[];

    versions: StyxVersionEntity[];

    scrollLoadingDone = true;

    recordsLoadingDone = false;

    recordIndex: number = 0;

    recordTotalCount: number = 1;

    apiPrefix: string;

    accessToRecords: PageOperationHistoryEntity[] = [];

    takeUntilDestroyed = takeUntilDestroyed();

    @ViewChild('scroll', { static: true }) scroll: ElementRef;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private pageApiService: PageApiService,
        private router: Router,
        private thyDialog: ThyDialog,
        private viewContainerRef: ViewContainerRef,
        public elementRef: ElementRef,
        private util: UtilService,
        private ngZone: NgZone,
        private isDocumentPipe: IsDocumentPipe,
        private translate: StyxTranslateService
    ) {}

    ngOnInit() {
        this.apiPrefix = `${API_PREFIX}/pages/${this.page._id}`;
        if (this.isDocumentPipe.transform({ type: this.page.type })) {
            let word = '';
            const nodesEntry = Node.texts(this.node);
            const nodes = Array.from(nodesEntry);
            nodes.forEach((node: any) => {
                const text = node[0].text;
                word += /(\n)$/g.test(text) ? text : `${text}\n`;
            });
            this.pageMetaInfo = textics(word);
        }
        this.fetchJoinedBaselines();
        this.recordsLoadingDone = false;
        this.fetchAccessToRecords();
    }

    ngAfterViewInit(): void {
        this.registerScrollEvent();
    }

    activeTabChange(key: string) {
        this.activeTabKey = key;
    }

    fetchJoinedBaselines() {
        this.loadingDone = false;
        this.pageApiService.fetchJoinedBaselines(this.pilotStore.snapshot.detail._id, this.page._id).subscribe((data: ResponseData) => {
            this.baselines = data.value;
            this.versions = data.references.versions;
            this.loadingDone = true;
        });
    }

    fetchAccessToRecords() {
        this.pageApiService
            .fetchAccessToRecords(this.page._id, this.recordIndex)
            .pipe(
                finalize(() => {
                    this.recordsLoadingDone = true;
                    this.scrollLoadingDone = true;
                }),
                this.takeUntilDestroyed
            )
            .subscribe({
                next: (data: PaginationResponseData) => {
                    this.accessToRecords = this.accessToRecords.concat(data.value);
                    this.recordTotalCount = data.page_count;
                },
                error: error => {
                    this.util.defaultErrorHandler()(error);
                }
            });
    }

    private registerScrollEvent() {
        this.ngZone.runOutsideAngular(() =>
            fromEvent(this.scroll.nativeElement, 'scroll')
                .pipe(debounceTime(100), this.takeUntilDestroyed)
                .subscribe(() => {
                    if (this.activeTabKey === 'info') {
                        this.scrolled();
                    }
                })
        );
    }

    private scrolled() {
        const { scrollTop, clientHeight, scrollHeight } = this.scroll.nativeElement;
        if (scrollTop + clientHeight + SCROLL_BUFFER >= scrollHeight) {
            this.ngZone.run(() => {
                this.scrollFetchAccessToRecords();
            });
        }
    }

    public scrollFetchAccessToRecords() {
        if (!this.scrollLoadingDone || !this.loadingDone || this.recordTotalCount <= this.recordIndex + 1) {
            return;
        }
        this.recordIndex = this.recordIndex + 1;
        this.scrollLoadingDone = false;
        this.fetchAccessToRecords();
    }

    openVersionCompare(baselineInfo: StyxPivotBaselineInfo): void {
        this.thyDialog.open(PageHistoryComponent, {
            initialState: {
                title: this.translate.instant<I18nSourceDefinitionType>('wiki.page.info.compare'),
                isVersionComparison: true,
                spaceId: this.page.space_id,
                pageId: this.page._id,
                targetVersionId: baselineInfo.version_id
            },
            size: ThyDialogSizes.full,
            viewContainerRef: this.viewContainerRef
        });
    }

    baselineClick(baselineInfo: StyxPivotBaselineInfo): void {
        const url = `/wiki/spaces/${this.pilotStore.snapshot.detail.identifier}/baselines/${baselineInfo.short_id}/pages`;
        this.router.navigateByUrl(url);
        this.close && this.close();
    }
}
