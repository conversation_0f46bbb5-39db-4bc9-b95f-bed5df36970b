import { ChangeDetectorRef, Component, EventEmitter, OnInit, Output, ViewChild, inject, signal } from '@angular/core';
import { ThyFileSelect } from 'ngx-tethys/upload';
import { DriveFileIconUrlPipe, StyxFileProcessManager, StyxTranslateService, NestedKey } from '@atinc/ngx-styx';
import { i18nSourceDefinition } from '@wiki/app/constants/i18n-source';
import { UploadPageType } from '@wiki/app/constants/page';
import { UploadPageOption } from '@wiki/app/entities/file-import';

@Component({
    selector: 'file-import, [file-import]',
    templateUrl: './file-import.component.html',
    host: {
        class: 'file-import'
    },
    providers: [DriveFileIconUrlPipe],
    standalone: false
})
export class FileImportComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Output() selectFiles: EventEmitter<{ files: File[]; uploadPageInfo: UploadPageOption }> = new EventEmitter();

    uploadPageInfo = signal<UploadPageOption | null>(null);

    uploadPageOptions: UploadPageOption[] = [
        {
            type: UploadPageType.Confluence,
            typeName: 'Confluence',
            wikiIcon: 'confluence',
            suffix: ['.zip'],
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportConfluence')
        },
        {
            type: UploadPageType.Markdown,
            typeName: 'Markdown',
            suffix: ['.markdown', '.md', '.mark', '.txt'],
            icon: 'markdown-fill',
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportMarkdown')
        },
        {
            type: UploadPageType.MarkdownZip,
            typeName: 'Markdown Zip',
            wikiIcon: 'markdown-zip',
            suffix: ['.zip'],
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportZip')
        },
        {
            type: UploadPageType.HTMLZip,
            typeName: 'HTML Zip',
            wikiIcon: 'html-zip',
            suffix: ['.zip'],
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportZip')
        },
        {
            type: UploadPageType.Word,
            typeName: 'Word',
            suffix: ['.docx'],
            globalIcon: 'doc',
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportDocx')
        },
        {
            type: UploadPageType.Excel,
            typeName: 'Excel',
            globalIcon: 'xls',
            suffix: ['.xlsx', '.csv'],
            tips: this.translate.instant<NestedKey<typeof i18nSourceDefinition>>('wiki.import.supportExcel')
        }
    ];

    @ViewChild('uploadOrigin') uploadOrigin: ThyFileSelect;

    constructor(
        private cdr: ChangeDetectorRef,
        public fileProcessManager: StyxFileProcessManager,
        public driveFileIconUrlPipe: DriveFileIconUrlPipe
    ) {}

    ngOnInit() {}

    onClickUploadFile(event, pageData) {
        this.uploadPageInfo.set(pageData);
        this.cdr.detectChanges();
        this.uploadOrigin.elementRef.nativeElement.click(event);
    }

    uploadPageFile(event: { files: File[] }) {
        this.selectFiles.emit({ files: event.files, uploadPageInfo: this.uploadPageInfo() });
    }
}
