import { HttpClient } from '@angular/common/http';
import {
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    Input,
    OnInit,
    Optional,
    Output,
    Signal,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    computed,
    inject,
    input,
    output
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router } from '@angular/router';
import {
    DateFormatPipe,
    DriveFileIconUrlPipe,
    DriveTypes,
    GlobalUsersStore,
    Is,
    PilotScopeType,
    ProcessRef,
    ResponseData,
    ReviewContentChangeInfo,
    ReviewObjectChangeType,
    ReviewSubjectResultState,
    StyxConfirmService,
    StyxFeedReceiver,
    StyxFileProcessManager,
    StyxPricingService,
    StyxReviewApiService,
    StyxReviewService,
    UtilService,
    WikiBroadObjectTypes,
    helpers,
    AIScenarioContext,
    AIScenarioObjectKind,
    StyxTranslateService,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { PlaitBoard } from '@plait/core';
import { getMindThemeColor } from '@plait/mind';
import { WikiKnowledgeGraphComponent } from '@wiki/app/components';
import { WikiAttachmentsDialogComponent } from '@wiki/app/components/attachments-dialog/attachments-dialog.component';
import { WikiRelatedResourceComponent } from '@wiki/app/components/resource/related-resource.component';
import { API_PREFIX, ExportFileType } from '@wiki/app/constants';
import { groupField } from '@wiki/app/constants/discussion';
import { FeedEventKeys } from '@wiki/app/constants/feed';
import { PagePermissionScopeType } from '@wiki/app/constants/page-sharing';
import { ReviewStateOptions } from '@wiki/app/constants/review';
import { PageInfo } from '@wiki/app/entities/page-info';
import { PageApiService, SpaceApiService } from '@wiki/app/services';
import { PageTreeService } from '@wiki/app/services/page-tree.service';
import { FullScreenService } from '@wiki/app/services/util/full-screen.service';
import { PageService } from '@wiki/app/services/util/page.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { CONTROL_KEY } from '@wiki/app/util/common';
import { getGroupByField } from '@wiki/app/util/discussion';
import { IsBoardPipe, IsDocumentPipe } from '@wiki/common/pipe/page.pipe';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { PageStore } from '@wiki/common/stores/page.store';
import { boardToImage } from '@wiki/common/util/board';
import { download } from '@wiki/common/util/download';
import { TheEditor } from '@worktile/theia';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { TinyDate } from 'ngx-tethys/util';
import { Subject, of } from 'rxjs';
import { catchError, filter, map, takeUntil, tap } from 'rxjs/operators';
import { Element } from 'slate';
import { PageDetailComponent } from '../detail.component';
import { PageSaveAsStencilComponent } from './save-as-stencil/save-as-stencil.component';
import { sanitizeFilename } from '../../../../util/file';
import { ThyStealthView } from '@tethys/cdk/dom';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

class ExportFileOption {
    type: ExportFileType;
    typeName: string;
    suffix: string;
    icon: string;
}

@Component({
    selector: 'wiki-page-detail-operations',
    templateUrl: './operations.component.html',
    providers: [DriveFileIconUrlPipe],
    standalone: false
})
export class PageDetailOperationsComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    selectPage: Signal<PageInfo | null> = input<PageInfo>(null);

    @Input() isMaxView: Is;

    @Input() editor: TheEditor;

    @Input() board: PlaitBoard;

    @Output() maxViewChange: EventEmitter<Is> = new EventEmitter();

    sidebarFixed = input<boolean>(false);

    sidebarChange = output();

    discussionCount: number;

    shortcutAdditions = {
        isPage: true
    };

    CONTROL_KEY = CONTROL_KEY;

    protected relationItemTotal: number = 0;

    ExportFileType = ExportFileType;

    documentExportOptions: ExportFileOption[] = [
        { type: ExportFileType.PDF, typeName: 'PDF', suffix: 'pdf', icon: 'file-pdf' },
        { type: ExportFileType.Word, typeName: 'Word', suffix: 'docx', icon: 'file-word' },
        { type: ExportFileType.Markdown, typeName: 'Markdown', suffix: 'md', icon: 'file-markdown' }
    ];

    boardExportOptions: ExportFileOption[] = [
        { type: ExportFileType.PDF, typeName: 'PDF', suffix: 'pdf', icon: 'file-pdf' },
        { type: ExportFileType.png, typeName: 'PNG', suffix: 'png', icon: 'file-png' },
        { type: ExportFileType.jpg, typeName: 'JPG', suffix: 'jpg', icon: 'file-jpg' }
    ];

    tableExportOptions: ExportFileOption[] = [
        { type: ExportFileType.excel, typeName: 'Excel', suffix: 'xlsx', icon: 'file-excel' },
        { type: ExportFileType.csv, typeName: 'Csv', suffix: 'csv', icon: 'file-csv' }
    ];

    pageExportOptions: Signal<ExportFileOption[]> = computed(() => {
        if (this.isDocumentPipe.transform({ type: this.selectPage()?.type })) {
            return this.documentExportOptions;
        } else if (this.isBoardPipe.transform({ type: this.selectPage()?.type })) {
            return this.boardExportOptions;
        } else {
            return this.tableExportOptions;
        }
    });

    popoverRef: ThyPopoverRef<any>;

    protected reviewStateOptions = ReviewStateOptions;

    get isHome() {
        return (
            (this.selectPage() && this.selectPage()._id) === (this.pagesStore.snapshot.homePage && this.pagesStore.snapshot.homePage._id)
        );
    }

    get commentCount() {
        return this.selectPage().comments.length + this.discussionCount;
    }

    get reviewGuidelineConfig() {
        return this.pageStore.snapshot?.review_guideline_config;
    }

    get isFavorite() {
        return this.selectPage().is_favorite;
    }

    get spaceScopeType() {
        return this.pilotStore.snapshot.detail.scope_type;
    }

    get spaceIdentifier() {
        return this.pilotStore.snapshot.detail.identifier;
    }

    get spaceId() {
        return this.pilotStore.snapshot.detail._id;
    }

    get nonMediaAttachmentCount() {
        return this.pageStore.snapshot?.page?.none_media_attachment_count ?? 0;
    }

    @ViewChild('moreOperate', { read: TemplateRef, static: true })
    moreOperateTemplate: TemplateRef<any>;

    @ViewChild('boardMoreOperate', { read: TemplateRef, static: true })
    boardMoreOperateTemplate: TemplateRef<any>;

    aiBarNodes: Node[];

    aiScenarioContext = inject(AIScenarioContext);

    @ViewChild(ThyStealthView, { static: true }) aiBarView: ThyStealthView;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        public usersStore: GlobalUsersStore,
        private pagesStore: PagesStore,
        private pageStore: PageStore,
        private pageService: PageService,
        private thyDialog: ThyDialog,
        @Optional() private pageDetail: PageDetailComponent,
        private pageApiService: PageApiService,
        private viewContainerRef: ViewContainerRef,
        private http: HttpClient,
        private util: UtilService,
        private pricingService: StyxPricingService,
        private fullScreenService: FullScreenService,
        private popover: ThyPopover,
        private fileProcessManager: StyxFileProcessManager,
        private driveFileIconUrlPipe: DriveFileIconUrlPipe,
        private feedReceiver: StyxFeedReceiver,
        private confirmService: StyxConfirmService,
        private spaceApiService: SpaceApiService,
        private isBoardPipe: IsBoardPipe,
        private isDocumentPipe: IsDocumentPipe,
        private elementRef: ElementRef,
        private reviewService: StyxReviewService,
        private destroyRef: DestroyRef,
        private styxReviewApiService: StyxReviewApiService,
        private globalPageStore: PageEventBus,
        public pageTreeService: PageTreeService
    ) {}

    ngOnInit() {
        this.pageStore
            .select$(state => state.discussions)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(() => {
                this.discussionCount = getGroupByField(
                    this.pageStore.snapshot.discussions.filter(item => item.is_resolved === 0),
                    groupField.nodeKey
                ).length;
            });
        this.pageStore
            .select$(state => state.page)
            .pipe(
                filter(page => !!page?.relation_total),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(page => {
                this.relationItemTotal = Object.values(page.relation_total).reduce((total, current) => total + (current ?? 0), 0);
            });
    }

    openAI() {
        this.aiBarNodes = this.aiBarView.rootNodes;
        this.aiScenarioContext.openPing({
            kind: AIScenarioObjectKind.principal,
            object: {
                ref_id: this.selectPage()._id,
                ref_type: 'page'
            },
            displayer: this.aiBarNodes
        });
    }

    openMoreOperations(event: MouseEvent) {
        const template = this.isBoardPipe.transform({ type: this.selectPage().type })
            ? this.boardMoreOperateTemplate
            : this.moreOperateTemplate;
        this.popoverRef = this.popover.open(template, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomRight',
            hasBackdrop: true,
            insideClosable: true,
            outsideClosable: true,
            manualClosure: true,
            viewContainerRef: this.viewContainerRef
        });
        this.popoverRef.afterClosed().subscribe(() => {
            this.popoverRef = null;
        });
    }

    editPage() {
        const id = this.selectPage().short_id ?? this.selectPage()._id;
        this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'pages', id, 'edit']);
    }

    deletePage() {
        const isToPage =
            this.pageStore.snapshot.page._id === this.selectPage()._id ||
            this.pageStore.snapshot.page.parent_ids.includes(this.selectPage()._id);
        this.pageService.deletePage(this.spaceIdentifier, this.selectPage(), isToPage);
    }

    copyPage() {
        this.pageService.openCopyPageDialog({
            selectPageOrIds: this.selectPage(),
            viewContainerRef: this.viewContainerRef,
            copySuccess: () => {
                this.pageTreeService.updatePageNodes(this.pagesStore.snapshot.entities);
            }
        });
    }

    movePage() {
        this.pageService.openMovePageDialog({
            selectPageOrIds: this.selectPage(),
            viewContainerRef: this.viewContainerRef,
            moveSuccess: (toSpaceId: string) => {
                if (toSpaceId !== this.spaceId) {
                    const space = this.pilotStore.snapshot.entities.find(space => space._id === this.selectPage().space_id);
                    this.router.navigate([
                        '/wiki/spaces/',
                        space.identifier,
                        'pages',
                        this.pagesStore.snapshot.homePage.short_id ?? this.pagesStore.snapshot.homePage._id
                    ]);
                }
                this.pageTreeService.updatePageNodes(this.pagesStore.snapshot.entities);
            }
        });
    }

    toggleLockPage() {
        if (this.selectPage().is_lock) {
            this.lockPage();
        } else {
            this.unlockPage();
        }
    }

    toggleMaxView() {
        this.maxViewChange.emit(this.isMaxView);
    }

    lockPage() {
        this.pageApiService.lockPage(this.selectPage()._id).subscribe((data: ResponseData<PageInfo>) => {
            if (data) {
                if (helpers.find(this.pagesStore.snapshot.entities, { _id: this.selectPage()._id })) {
                    this.pagesStore.update(this.selectPage()._id, { is_lock: Is.yes, permissions: data.value.permissions });
                }
                this.pageStore.pureUpdatePageById(this.selectPage()._id, { is_lock: Is.yes, permissions: data.value.permissions });
            }
        });
    }

    unlockPage() {
        this.pageApiService.unlockPage(this.selectPage()._id).subscribe(data => {
            if (data) {
                if (helpers.find(this.pagesStore.snapshot.entities, { _id: this.selectPage()._id })) {
                    this.pagesStore.update(this.selectPage()._id, { is_lock: Is.no, permissions: data.value.permissions });
                }
                this.pageStore.pureUpdatePageById(this.selectPage()._id, { is_lock: Is.no, permissions: data.value.permissions });
            }
        });
    }

    toggleSidebar($event: Event) {
        this.sidebarChange.emit();
    }

    openPageInfo() {
        const container = this.elementRef.nativeElement.closest('.page-detail-container') as HTMLElement;
        this.pageService.openPageInfo(container, this.selectPage(), {
            children: this.pageStore.snapshot.content || []
        } as Element);
    }

    openAttachment() {
        this.thyDialog.open(WikiAttachmentsDialogComponent, {
            initialState: {
                pageStatus: 'preview'
            },
            size: ThyDialogSizes.maxLg,
            viewContainerRef: this.viewContainerRef,
            restoreFocus: false
        });
    }

    saveAsStencil() {
        this.thyDialog.open(PageSaveAsStencilComponent, {
            initialState: { page: this.selectPage() },
            size: ThyDialogSizes.lg
        });
    }

    printPage() {
        setTimeout(() => {
            window.print();
        });
    }

    private getFileName() {
        const dateTimeAutoFormat = new DateFormatPipe();
        return sanitizeFilename(this.selectPage().name + '-' + dateTimeAutoFormat.transform(new TinyDate().getTime(), 'yyyyMMddhhmm'));
    }

    private openDownloadDialog(spaceId: string, fileName: string, url: string, option: ExportFileOption) {
        const downloadDialog = document.querySelector('.download-page-dialog');
        if (spaceId === this.spaceId && !downloadDialog) {
            this.openConfirm(url, option, fileName);
        }
    }

    exportBoard(option: ExportFileOption) {
        const fileName = this.getFileName();
        const processRef = this.fileProcessManager.addProcess(
            {
                name: fileName,
                icon: this.driveFileIconUrlPipe.transform({
                    type: DriveTypes.file,
                    addition: {
                        ext: option.suffix
                    }
                })
            },
            {
                cancelTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.cancel'),
                retryTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.retry'),
                retry: (processRef: ProcessRef) => {
                    this.exportBoardToImage(option, fileName, processRef);
                }
            }
        );
        this.exportBoardToImage(option, fileName, processRef);
    }

    exportBoardToImage(option: ExportFileOption, fileName: string, processRef: ProcessRef) {
        processRef.setProgress(1);
        const boardBackground = option.type === ExportFileType.png ? 'transparent' : getMindThemeColor(this.board).boardBackground;

        boardToImage(this.board, {
            fillStyle: boardBackground
        })
            .then(image => {
                processRef.complete(null, { url: image });
                this.openDownloadDialog(this.spaceId, fileName, image, option);
            })
            .catch(error => {
                processRef.error(this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportError'));
            });
    }

    exportPage(option: ExportFileOption) {
        const isImage = [ExportFileType.png, ExportFileType.jpg].includes(option.type);
        if (this.isBoardPipe.transform({ type: this.selectPage().type }) && isImage) {
            this.exportBoard(option);
            return;
        }
        if (this.pricingService.ensureCheckpointEnabled('pageExport')) {
            if (this.popoverRef) {
                this.popoverRef.close();
            }
            this.export(option).subscribe({
                next: (data: ResponseData) => {
                    const fileName = this.getFileName();
                    const processRef = this.fileProcessManager.addProcess(
                        {
                            name: fileName,
                            icon: this.driveFileIconUrlPipe.transform({
                                type: DriveTypes.file,
                                addition: {
                                    ext: option.suffix
                                }
                            })
                        },
                        {
                            cancelTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.cancel'),
                            retryTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.retry'),
                            retry: (processRef: ProcessRef) => {
                                this.retryExport(option, processRef, fileName);
                            }
                        }
                    );
                    this.registerExportPageFeedEvents(this.spaceId, data.value.job_id, processRef, option.type, fileName);
                },
                error: this.util.defaultErrorHandler()
            });
        }
    }

    export(option: ExportFileOption) {
        return this.spaceApiService
            .exportPage({
                space_id: this.spaceId,
                page_id: this.selectPage()._id,
                type: option.type
            })
            .pipe(
                catchError(error => {
                    this.util.defaultErrorHandler()(error);
                    return of(null);
                })
            );
    }

    retryExport(option: ExportFileOption, processRef: ProcessRef, pageName: string) {
        processRef.reset();
        this.export(option).subscribe((data: ResponseData) => {
            this.registerExportPageFeedEvents(this.spaceId, data.value.job_id, processRef, option.type, pageName);
        });
    }

    registerExportPageFeedEvents(spaceId: string, jodId: string, processRef: ProcessRef, type: ExportFileType, fileName: string) {
        let destroy$ = new Subject<boolean>();
        processRef.cancel$.subscribe(() => {
            this.spaceApiService
                .cancelExportPage(jodId)
                .pipe(
                    catchError(error => {
                        this.util.defaultErrorHandler()(error);
                        return of(null);
                    })
                )
                .subscribe();
            this.destroy(destroy$);
        });
        processRef.setProgress(1);
        this.feedReceiver
            .received()
            .pipe(takeUntil(destroy$))
            .subscribe(feed => {
                if (feed.data.jobId === jodId) {
                    processRef?.setId(jodId);
                    if (feed.data.error) {
                        processRef.error(this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportError'));
                        this.destroy(destroy$);
                    } else {
                        if (feed.event_key === FeedEventKeys.finishExport) {
                            const option: ExportFileOption = this.pageExportOptions().find(x => x.type === type);
                            const downloadUrl = feed.data.downloadUrl;
                            const expired = {
                                date: new TinyDate(feed.data.expired).getUnixTime(),
                                message: this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportLinkExpired')
                            };
                            processRef.complete(null, {
                                url: downloadUrl,
                                expired
                            });
                            this.destroy(destroy$);
                            this.openDownloadDialog(spaceId, fileName, downloadUrl, option);
                        }
                    }
                }
            });
    }

    destroy(destroy$: Subject<Boolean>) {
        destroy$.next(true);
        destroy$.unsubscribe();
    }

    openConfirm(
        downloadUrl: string,
        option: ExportFileOption,
        pageName: string,
        expired?: {
            date: number;
            message: string;
        }
    ) {
        const { typeName, suffix } = option;
        const fileName = `${pageName}.${suffix}`;
        const content = [ExportFileType.png, ExportFileType.jpg].includes(option.type)
            ? this.translate.instant<I18nSourceDefinitionType>('wiki.attachments.tabs.picture')
            : this.translate.instant<I18nSourceDefinitionType>('wiki.page.export.file', { type: typeName });
        this.confirmService.confirm({
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.export.success'),
            okText: this.translate.instant<I18nSourceDefinitionType>('common.download'),
            okLoadingText: this.translate.instant<I18nSourceDefinitionType>('wiki.export.downloading'),
            mainContent: this.translate.instant<I18nSourceDefinitionType>(`wiki.page.export.success`, { content, fileName }),
            action: () => {
                if (expired && new TinyDate().getUnixTime() >= expired.date) {
                    this.util.notify.error(this.translate.instant<I18nSourceDefinitionType>('common.failure'), expired.message);
                    return of(true);
                }
                return this.http.get(downloadUrl, { responseType: 'blob' }).pipe(
                    tap((blob: Blob) => {
                        download(blob, fileName);
                    }),
                    map(() => true),
                    catchError(error => {
                        this.util.defaultErrorHandler()(error);
                        return of(true);
                    })
                );
            }
        });
    }

    openPageSharing() {
        this.pageService.openPageSharing({
            isShared: this.selectPage().is_shared,
            spaceId: this.selectPage().space_id,
            pageId: this.selectPage()._id,
            shortId: this.selectPage().short_id,
            isConfiguration: false,
            viewContainerRef: this.viewContainerRef,
            spaceScopeType: this.spaceScopeType,
            selectType:
                this.spaceScopeType === PilotScopeType.personal ? PagePermissionScopeType.spaceOutside : PagePermissionScopeType.spaceInside
        });
    }

    openHistory() {
        const id = this.selectPage().short_id ?? this.selectPage()._id;
        this.router.navigate(['/wiki/spaces', this.spaceIdentifier, 'pages', id, 'versions']);
    }

    openRelatedResource() {
        this.util.dialog.open(WikiRelatedResourceComponent, {
            initialState: {
                page: this.selectPage(),
                editor: this.editor
            },
            size: ThyDialogSizes.superLg,
            viewContainerRef: this.viewContainerRef
        });
    }

    openFullScreen() {
        this.fullScreenService.setFullScreen('[fullscreen-target]');
    }

    favorite() {
        if (this.isFavorite) {
            this.pageStore.removeFavorite(this.selectPage()._id);
        } else {
            this.pageStore.addFavorite(this.selectPage()._id);
        }
    }

    fetchReviewGuidelines() {
        return this.styxReviewApiService.fetchReviewGuidelines('', `${API_PREFIX}/admin/spaces/${this.spaceId}/review`);
    }

    beforeReviewAction = () => {
        const reviewContent = [
            {
                _id: this.selectPage()._id,
                change_version: {
                    to: this.selectPage().version_id
                },
                change_type: ReviewObjectChangeType.add
            }
        ] as ReviewContentChangeInfo[];
        return of(reviewContent);
    };

    submitPageReview() {
        this.reviewService
            .createReview({
                apiPrefix: `${API_PREFIX}/spaces/${this.spaceId}/review`,
                subjectType: WikiBroadObjectTypes.page,
                beforeAction: this.beforeReviewAction
            })
            .subscribe(res => {
                if (res) {
                    this.globalPageStore.emitPageInfo({
                        ...this.selectPage(),
                        review_result_state: ReviewSubjectResultState.inProgress
                    });
                    this.util.dialog?.close();
                }
            });
    }

    openKnowledgeGraph() {
        this.thyDialog.open(WikiKnowledgeGraphComponent, {
            initialState: {},
            size: ThyDialogSizes.superLg,
            viewContainerRef: this.viewContainerRef,
            restoreFocus: false
        });
    }
}
