import { Component, HostBinding, Input, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import {
    AppRootContext,
    GlobalUsersStore,
    UtilService,
    Is,
    hasPermissionTransform,
    injectBizValidatorMaxLengths,
    StyxTranslateService
} from '@atinc/ngx-styx';
import { PageApiService } from '@wiki/app/services/page-api.service';
import { PageInfo } from '@wiki/app/entities/page-info';
import { TheDocument } from '@wiki/common/custom-types';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

export enum UseScope {
    global = 1,
    space = 2
}

@Component({
    selector: 'wiki-page-save-as-stencil',
    templateUrl: './save-as-stencil.component.html',
    standalone: false
})
export class PageSaveAsStencilComponent implements OnInit {
    @HostBinding(`class.wiki-page-save-as-stencil`) className = true;
    title: string;

    @Input()
    page: PageInfo;

    @Input()
    content: TheDocument;

    scope = UseScope.space;

    UseScope = UseScope;

    public saving = false;

    stencilMaxLength = injectBizValidatorMaxLengths().longTitle;

    public formValidatorConfig = {
        validationMessages: {
            name: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.error.templateNameRequired')
            },
            queryScope: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.error.templateScopeRequired')
            }
        }
    };

    constructor(
        private util: UtilService,
        public userStore: GlobalUsersStore,
        public appRootContext: AppRootContext,
        public pageApiService: PageApiService,
        private translate: StyxTranslateService
    ) {}

    ngOnInit() {
        this.title = this.page.name;
        if (!hasPermissionTransform(this.page.permissions, wikiPermissionPoints, 'space_stencil_setting')) {
            this.scope = UseScope.global;
        }
    }

    cancel() {
        this.util.dialog.close();
    }

    ok() {
        if (this.saving) {
            return;
        }
        this.saving = true;
        this.pageApiService
            .saveAsStencil(this.page._id, {
                is_global: this.scope === UseScope.global ? Is.yes : Is.no,
                name: this.title,
                content: this.content
            })
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe({
                next: () => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>(`common.saveSuccess`));
                    this.util.dialog.close();
                },
                error: error => {
                    this.util.defaultErrorHandler()(error);
                }
            });
    }
}
