import { Component, inject, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PilotEntryStore } from '@atinc/ngx-styx';
import { PagesStore } from '@wiki/app/stores/pages.store';

@Component({
    selector: 'wiki-pages-empty',
    template: '',
    standalone: false
})
export class WikiPagesEmptyComponent implements OnInit {
    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private pagesStore: PagesStore
    ) {}

    ngOnInit() {
        const space = this.pilotStore.snapshot.detail;
        const homePage = this.pagesStore.snapshot.homePage;
        const idOrIdentifier = space?.identifier;
        const id = homePage?.short_id ?? homePage?._id;

        if (!idOrIdentifier || !id || space._id !== homePage.space_id) {
            return;
        }
        this.router.navigate([this.router.url, id], { relativeTo: this.route, replaceUrl: true });
    }
}
