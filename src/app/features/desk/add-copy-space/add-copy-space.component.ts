import { Component, HostBinding, inject, Input, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
    AppRootContext,
    COLORS,
    GlobalApplicationPermissionPipe,
    injectBizValidatorMaxLengths,
    MemberItem,
    PilotScopeType,
    ResponseData,
    SubAppRootContext,
    Visibility,
    StyxTranslateService
} from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { ThyFormDirective } from 'ngx-tethys/form';
import { Observable, of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { SpaceInfo } from '../../../entities/space-info';
import { SpaceApiService } from '../../../services/space-api.service';
import { getNewSpaceTemplate } from '@wiki/app/constants';

@Component({
    selector: 'wiki-desk-add-copy-space',
    templateUrl: './add-copy-space.component.html',
    providers: [GlobalApplicationPermissionPipe],
    standalone: false
})
export class DeskAddOrCopySpaceComponent implements OnInit {
    translate = inject(StyxTranslateService);

    @HostBinding('class') class = 'thy-dialog-content';

    public visibility: Visibility;

    @Input() space: SpaceInfo;

    template = getNewSpaceTemplate(this.translate);

    public descriptionMaxLength = injectBizValidatorMaxLengths().multiLineText;

    public saving: boolean;

    public hasMembers: boolean = false;

    scopeTypes = [PilotScopeType.organization, PilotScopeType.team, PilotScopeType.personal];

    newSpace = { name: '', color: '' };

    public validatorConfig = {
        validationMessages: {
            spaceName: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.space.nameRequired')
            },
            'styx-input-identifier': {
                pattern: this.translate.instant<I18nSourceDefinitionType>('wiki.space.identifierPattern'),
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.space.identifierRequired')
            }
        }
    };

    public addonsResolver: () => Observable<string[]> = () => {
        return of([
            this.translate.instant<I18nSourceDefinitionType>('styx.page', { isTitle: true, isPlural: false }),
            this.translate.instant<I18nSourceDefinitionType>('styx.review', { isTitle: true, isPlural: false }),
            this.translate.instant<I18nSourceDefinitionType>('styx.baseline', { isTitle: true, isPlural: false })
        ]);
    };

    @Input() saveFn$: (value: SpaceInfo) => Observable<ResponseData<SpaceInfo>>;

    @ViewChild(ThyFormDirective, { static: true }) thyForm: ThyFormDirective;

    constructor(
        public appRootContext: AppRootContext,
        private spaceApiService: SpaceApiService,
        private subAppRootContext: SubAppRootContext,
        private globalApplicationPermission: GlobalApplicationPermissionPipe,
        private router: Router
    ) {}

    ngOnInit(): void {
        this.initializeSpace();
    }

    private pagePrefixError() {
        this.thyForm.validator.setElementErrorMessage(
            'styx-input-identifier',
            this.translate.instant<I18nSourceDefinitionType>('wiki.space.identifierExists')
        );
    }

    initializeSpace() {
        this.newSpace.name = this.space.name;
        this.newSpace.color = this.space.color || COLORS[0];
        this.space.scope_type = this.getScope();
        this.visibility = this.space.visibility || Visibility.private;
        this.hasMembers = this.space.scope_type !== PilotScopeType.personal;

        if (this.space._id) {
            this.fetchSpaceMembers();
        } else {
            this.space.members = [this.appRootContext.globalInfo.me];
        }
    }

    get isCopySpace() {
        return !!this.space._id;
    }

    getScope() {
        // 团队
        const isTeamSpace = this.router.url.includes('team');
        if (isTeamSpace || this.space?.scope_type === PilotScopeType.team) {
            return PilotScopeType.team;
        }
        // 个人
        const isPersonalSpace = this.router.url.includes('personal');
        if (isPersonalSpace || this.space?.scope_type === PilotScopeType.personal) {
            return PilotScopeType.personal;
        }
        const hasCreateGlobalSpacePermission = this.globalApplicationPermission.transform(
            this.subAppRootContext.snapshot.globalPermissions,
            'create_space'
        );
        return hasCreateGlobalSpacePermission ? PilotScopeType.organization : PilotScopeType.team;
    }

    colorSelect($event) {
        const _color = $event.color;
        this.space.color = _color;
    }

    confirmSelectMembers(members: MemberItem[]) {
        this.space.members = members;
    }

    saveSpaceAction$ = () => {
        const space: SpaceInfo = {
            scope_type: this.space.scope_type,
            name: this.newSpace.name,
            color: this.newSpace.color,
            visibility: this.visibility,
            identifier: this.space.identifier,
            category_id: this.space.category_id,
            description: this.space.description,
            user_group_ids: this.space.user_group_ids,
            members: this.space.members || []
        };
        if (this.space._id) {
            space._id = this.space._id;
        }

        if (this.hasMembers) {
            return this.saveFn$(space);
        } else {
            return this.spaceApiService.checkPrefixIsExist(this.space.identifier).pipe(
                mergeMap((data: ResponseData) => {
                    if (data.value) {
                        this.pagePrefixError();
                        this.saving = false;
                        return of(false);
                    } else {
                        return this.saveFn$(space);
                    }
                })
            );
        }
    };

    fetchSpaceMembers() {
        const payload = {
            pi: 0,
            ps: 2000,
            keywords: ''
        };
        this.spaceApiService.fetchSpaceMembers(this.space._id, payload).subscribe((data: ResponseData) => {
            this.space.members = data.value;
        });
    }

    scopeValueChange(value: string) {
        if (value) {
            this.space.user_group_ids = [value];
        }
    }

    selectScope(scopeType: PilotScopeType) {
        if (scopeType === PilotScopeType.personal) {
            this.visibility = Visibility.private;
            this.hasMembers = false;
        } else {
            this.hasMembers = true;
        }
        this.space = { ...this.space, scope_type: scopeType };
    }

    validateIndentifier$ = () =>
        this.spaceApiService.checkPrefixIsExist(this.space.identifier).pipe(
            mergeMap((data: ResponseData) => {
                if (data.value) {
                    this.pagePrefixError();
                }
                return of(data);
            })
        );
}
