<styx-create-pilot-item
  styxCheckpointKey="space"
  styxI18nTracking
  [styxActionText]="space?._id ? ('common.copy' | translate) : ('common.new' | translate)"
>
  <styx-create-pilot-item-intro
    [styxImgSrc]="'/assets/images/space/new-space.svg'"
    [styxTitle]="template.name"
    [styxDescription]="template.desc"
    [styxAddonsOrAction]="addonsResolver"
  >
  </styx-create-pilot-item-intro>

  <styx-create-pilot-item-form
    [styxForm]="thyForm"
    [styxPilot]="space"
    [styxMembers]="space.members"
    [styxMemberSelectable]="hasMembers"
    [styxCreateAction]="saveSpaceAction$"
    (styxMembersChange)="confirmSelectMembers($event)"
    [styxCheckIdentifierAction]="validateIndentifier$"
  >
    <form
      thyForm
      name="addEditSpaceForm"
      [thyFormValidatorConfig]="validatorConfig"
      #demoVerticalThyForm="thyForm"
      #demoVerticalForm="ngForm"
    >
      <thy-form-group thyLabelRequired styxI18nTracking [thyLabelText]="'styx.scopeType' | translate">
        <styx-pilot-scope-select
          name="scopeSelect"
          [(ngModel)]="space.scope_type"
          [styxScopeTypes]="scopeTypes | scopeTypesByPermission: 'create_space'"
          (styxValueChange)="scopeValueChange($event)"
          (ngModelChange)="selectScope($event)"
        ></styx-pilot-scope-select>
      </thy-form-group>
      <thy-form-group thyLabelRequired styxI18nTracking [thyLabelText]="'wiki.space.spaceName' | translate">
        <styx-pilot-input-name
          [(ngModel)]="newSpace"
          [styxIconName]="'book-fill'"
          styxColorSize="sm"
          thyAutofocus
          styxName="spaceName"
          name="name"
          required
          styxI18nTracking
          [styxPlaceholder]="'wiki.space.namePlaceholder' | translate"
          [styxResultNgModel]="spaceIdentifier"
        ></styx-pilot-input-name>
      </thy-form-group>
      <thy-form-group thyLabelRequired styxI18nTracking [thyLabelText]="'styx.visibility' | translate">
        <styx-pilot-visible-select
          name="styx-visible-select"
          [(ngModel)]="visibility"
          [styxDisabled]="space.scope_type | pilotScopeIsPersonal"
          [styxScope]="space.scope_type"
          [styxName]="template.noTitleName"
        ></styx-pilot-visible-select>
      </thy-form-group>
      <thy-form-group thyLabelRequired styxI18nTracking [thyLabelText]="'wiki.space.identifier' | translate">
        <styx-input-identifier
          #spaceIdentifier="ngModel"
          [(ngModel)]="space.identifier"
          name="identifier"
          styxI18nTracking
          [styxTooltip]="'wiki.space.identifierHint' | translate"
        ></styx-input-identifier>
      </thy-form-group>
      <thy-form-group styxI18nTracking [thyLabelText]="'styx.category' | translate">
        <styx-pilot-category-select name="categorySelect" [(styxValue)]="space.category_id"></styx-pilot-category-select>
      </thy-form-group>
      <thy-form-group styxI18nTracking [thyLabelText]="'styx.desc' | translate">
        <thy-input-group>
          <textarea
            thyInput
            name="description"
            class="form-control wiki-space-add-description"
            rows="3"
            [(ngModel)]="space.description"
            styxI18nTracking
            [placeholder]="'wiki.space.description' | translate"
            [maxlength]="descriptionMaxLength"
          ></textarea>
          <ng-template #suffix>
            <thy-input-count></thy-input-count>
          </ng-template>
        </thy-input-group>
      </thy-form-group>
    </form>
  </styx-create-pilot-item-form>
</styx-create-pilot-item>
