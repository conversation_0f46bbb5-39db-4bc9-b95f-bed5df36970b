import { Component, HostB<PERSON>ing, inject, ViewContainerRef, OnInit, QueryList, ViewChild, ViewChildren } from '@angular/core';
import { Router } from '@angular/router';
import {
    GlobalUsersStore,
    PilotScopeType,
    PilotStateType,
    StyxPilotEntryListComponent,
    StyxPilotScopeTypeNamePipe,
    UtilService,
    Visibility,
    css,
    StyxTranslateService,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThyTableSortDirection, ThyTableSortEvent } from 'ngx-tethys/table';
import { ThyTooltipDirective } from 'ngx-tethys/tooltip';
import { tap } from 'rxjs/operators';
import { SpaceService } from '../../../../app/services/util/space.service';
import { SpaceInfo } from '../../../entities/space-info';
import { DeskAddOrCopySpaceComponent } from '../add-copy-space/add-copy-space.component';

@Component({
    selector: 'wiki-desk-spaces',
    templateUrl: './spaces.component.html',
    providers: [StyxPilotScopeTypeNamePipe],
    standalone: false
})
export class DeskSpacesComponent implements OnInit {
    @HostBinding(`class`) className = `${css.layout} wiki-desk-spaces`;

    @ViewChild('pilotEntry') pilotEntryList: StyxPilotEntryListComponent;

    @ViewChildren(ThyTooltipDirective) tooltips: QueryList<ThyTooltipDirective>;

    sidebarCollapsed = false;

    scopeType: PilotScopeType;

    stateType: PilotStateType;

    styxTabs = [
        {
            key: 'all',
            name: this.translate.instant<I18nSourceDefinitionType>('styx.all'),
            type: 'all'
        },
        {
            key: 'star',
            name: this.translate.instant<I18nSourceDefinitionType>('styx.favorite'),
            type: 'star'
        },
        {
            key: 'history',
            name: this.translate.instant<I18nSourceDefinitionType>('styx.history'),
            type: 'history'
        }
    ];

    styxHeaderTitle: string;

    private viewContainerRef = inject(ViewContainerRef);

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public usersStore: GlobalUsersStore,
        private router: Router,
        public util: UtilService,
        public spaceService: SpaceService,
        private translate: StyxTranslateService
    ) {}

    ngOnInit() {
        const array = this.router.url.split('/');
        const routeKey = array[array.length - 1];
        switch (routeKey) {
            case 'spaces':
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.all');
                this.scopeType = undefined;
                return;
            case 'organization':
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.organization');
                this.scopeType = PilotScopeType.organization;
                return;
            case 'team':
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.team');
                this.scopeType = PilotScopeType.team;
                return;
            case 'personal':
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.personal');
                this.scopeType = PilotScopeType.personal;
                return;
            case 'archived':
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.archived');
                this.scopeType = undefined;
                this.stateType = PilotStateType.archived;
                return;
            default:
                this.styxHeaderTitle = this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.all');
                this.scopeType = undefined;
                return;
        }
    }

    createSpace() {
        const space = new SpaceInfo();
        this.util.dialog.open(DeskAddOrCopySpaceComponent, {
            size: ThyDialogSizes.maxLg,
            initialState: {
                saveFn$: (value: SpaceInfo) => {
                    return this.pilotStore.addEntry(value).pipe(
                        tap(data => {
                            this.router.navigate(['/wiki/spaces/', data.value.identifier]);
                        })
                    );
                },
                space
            },
            viewContainerRef: this.viewContainerRef
        });
    }

    goSpace(row: SpaceInfo) {
        this.router.navigate(['/wiki/spaces/', row.identifier]);
    }

    onSortChange($event: ThyTableSortEvent) {
        const { key, direction } = $event;
        if (direction === ThyTableSortDirection.asc || direction === ThyTableSortDirection.desc) {
            this.pilotEntryList.sortPilot({ key, direction });
        } else {
            this.pilotEntryList.sortPilot({ key: '', direction });
        }
    }

    openSettingsMenu(activeItem: SpaceInfo, event: Event) {
        this.spaceService.openSpaceSettingPopover(activeItem, {
            origin: event.currentTarget as HTMLElement,
            viewContainerRef: this.viewContainerRef
        });
    }

    newWindowOpen(row: SpaceInfo) {
        this.spaceService.newWindowOpenSpace(row?.identifier);
    }

    toggleFavorite(event: Event, row: SpaceInfo) {
        this.tooltips.forEach(item => {
            item.hide(0);
        });
        this.pilotEntryList.toggleFavorite(event, row);
    }

    isVisibilityPublic(row: SpaceInfo) {
        return row.visibility === Visibility.public;
    }
}
