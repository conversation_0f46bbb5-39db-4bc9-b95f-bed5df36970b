<styx-pilot-entries
  #pilotEntry
  [styxCreatePermissionKey]="'create_space'"
  [styxCache]="true"
  [styxTabs]="styxTabs"
  [styxI18nCountWithUnitKey]="'wiki.space.numberOfSpaces'"
>
  <ng-template #header>
    <thy-header styxSecondaryHeader thySize="xlg" [thyTitle]="styxHeaderTitle">
      <ng-template #headerOperation>
        <thy-nav thyType="thirdly">
          <a class="create-pilot-trigger" thyButton="primary" thyIcon="plus-bold" (click)="createSpace()" styxI18nTracking>{{
            'wiki.desk.newSpace' | translate
          }}</a>
        </thy-nav>
      </ng-template>
    </thy-header>
  </ng-template>
  <ng-template #table let-model="model" let-loadingDone="loadingDone" let-pagination="pagination">
    <thy-table
      thySize="lg"
      thyClassName="table-hover table-fixed"
      [thyModel]="model"
      [thyLoadingDone]="loadingDone"
      [thyRowClassName]="pilotEntry.generateRowClass"
      [thyEmptyOptions]="{ size: 'lg', message: ('common.noResult' | translate) }"
      (thyOnRowClick)="goSpace($event.row)"
      styxI18nTracking
      [thyPageIndex]="pagination?.pageIndex"
      [thyPageSize]="pagination?.pageSize"
      [thyPageTotal]="pagination?.count"
      (thyOnPageChange)="pilotEntry.pageChange($event)"
      (thyOnPageSizeChange)="pilotEntry.pageSizeChange($event)"
    >
      <thy-table-column
        thyWidth="auto"
        [thySortable]="true"
        [thyTitle]="'common.name' | translate"
        thyModelKey="name"
        thyClassName="text-truncate"
        [thySortable]="true"
        [thySortDirection]="pilotEntry.sortConfig.key === 'name' ? pilotEntry.sortConfig.direction : ''"
        (thySortChange)="onSortChange($event)"
        styxI18nTracking
      >
        <ng-template #cell let-row>
          <div class="space-title">
            <thy-icon
              class="font-size-lg mr-2"
              [thyIconName]="row?.type | spaceTemplateIcon"
              [ngStyle]="{ color: row?.color | mapColor }"
            ></thy-icon>
            <span class="text-truncate mr-2">{{ row?.name }}</span>
            <thy-icon
              class="font-size-sm text-placeholder mr-2"
              [thyIconName]="isVisibilityPublic(row) ? 'shield-user' : 'lock'"
            ></thy-icon>
            @if (row.is_shared) {
              <thy-tag
                [thyTooltip]="'wiki.desk.spaces.sharing' | translate"
                thyTheme="outline"
                thyColor="default"
                thyShape="pill"
                class="text-desc"
                styxI18nTracking
              >
                <thy-icon thyIconName="share" class="text-success" thyColor="success"></thy-icon>{{ 'styx.shared' | translate }}
              </thy-tag>
            }
          </div>
        </ng-template>
      </thy-table-column>

      <thy-table-column thyClassName="thy-operation-links" thyRowKey="1" thyWidth="15%" thyMinWidth="140px">
        <ng-template #cell let-row>
          <a class="mr-1" [ngClass]="{ visible: row.is_favorite }" href="javascript:;">
            <styx-star class="font-size-md" [styxIsStar]="row.is_favorite" (click)="toggleFavorite($event, row)"></styx-star>
          </a>
          <a
            class="mr-1"
            thyAction
            styxI18nTracking
            [thyTooltip]="'styx.openNew' | translate"
            [thyStopPropagation]
            (click)="newWindowOpen(row)"
          >
            <thy-icon thyIconName="publish"></thy-icon>
          </a>
          <a
            thyAction
            styxI18nTracking
            [thyTooltip]="'common.more' | translate"
            [thyStopPropagation]
            (click)="openSettingsMenu(row, $event)"
          >
            <thy-icon thyIconName="more-vertical"></thy-icon>
          </a>
        </ng-template>
      </thy-table-column>

      <thy-table-column thyWidth="16%" thyModelKey="identifier" styxI18nTracking [thyTitle]="'styx.identifier' | translate">
      </thy-table-column>
      <thy-table-column
        thyWidth="17%"
        thyMinWidth="180px"
        thyModelKey="scope_type"
        styxI18nTracking
        [thyTitle]="'styx.scopeType' | translate"
      >
        <ng-template #cell let-row>
          <span class="text-truncate flexible-text-container"> {{ row | pilotScopeTypeSimpleName }} </span>
        </ng-template>
      </thy-table-column>

      <thy-table-column
        thySecondary
        thyWidth="17%"
        [thySortable]="true"
        [thySortDirection]="pilotEntry.sortConfig.key === 'updated_at' ? pilotEntry.sortConfig.direction : ''"
        (thySortChange)="onSortChange($event)"
        thyModelKey="updated_at"
        styxI18nTracking
        [thyTitle]="'styx.updatedAt' | translate"
      >
        <ng-template #cell let-row>
          {{ row?.updated_at | dateTimeAutoFormat }}
        </ng-template>
      </thy-table-column>
    </thy-table>
    @if (loadingDone) {
      <spaces-guider></spaces-guider>
    }
  </ng-template>
</styx-pilot-entries>

<!-- 不能删除 router-outlet，给独立详情页使用 -->
<router-outlet></router-outlet>
