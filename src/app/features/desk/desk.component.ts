import { Component, HostBinding, OnInit, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
    PilotBaseEntity,
    PilotEntryStore,
    PilotScopeType,
    StyxHeadService,
    StyxTranslateService,
    SubAppRootContext,
    UtilService,
    css
} from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { getWikiTitles } from '../../constants/page';

interface MenuInfo {
    name?: string;
    key?: PilotScopeType;
    keyWords?: string;
    thyIconName?: string;
    color?: string;
    link?: string;
}

@Component({
    selector: 'wiki-desk',
    templateUrl: './desk.component.html',
    standalone: false
})
export class DeskComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    private pilotStore = inject(PilotEntryStore);

    @HostBinding(`class`) className = `${css.layout}`;

    spacesMenu: MenuInfo[] = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.all'),
            keyWords: 'spaces',
            thyIconName: 'view-tile',
            color: 'text-primary',
            link: './spaces'
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.organization'),
            key: PilotScopeType.organization,
            keyWords: 'organization',
            thyIconName: 'organization',
            color: 'text-success',
            link: './spaces/organization'
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.team'),
            key: PilotScopeType.team,
            keyWords: 'team',
            thyIconName: 'user-group',
            color: 'text-info',
            link: './spaces/team'
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.personal'),
            key: PilotScopeType.personal,
            keyWords: 'personal',
            thyIconName: 'user-bold',
            color: 'text-warning',
            link: './spaces/personal'
        }
    ];

    public archiveMenu = {
        name: this.translate.instant<I18nSourceDefinitionType>('wiki.desk.spaces.archived'),
        key: 'archived',
        keyWords: 'archived',
        thyIconName: 'archive',
        color: '#f6c659',
        link: './spaces/archived'
    };

    public configurationMenu = {
        name: this.translate.instant<I18nSourceDefinitionType>('styx.settings'),
        key: 'configuration',
        keyWords: 'configuration',
        thyIconName: 'horizontal-two-lines',
        link: '/admin/product/wiki/configuration',
        color: '#9C9CFB'
    };

    get routeKey() {
        const array = this.router.url.split('/');
        return array[array.length - 1];
    }

    constructor(
        public subAppRootContext: SubAppRootContext,
        private router: Router,
        public util: UtilService,
        private headService: StyxHeadService
    ) {}

    ngOnInit(): void {
        this.subAppRootContext.fetchGlobalPermissions();
        const { default: defaultTitle } = getWikiTitles(this.translate);
        this.headService.setTitle(defaultTitle);
    }

    openPilotItem(pilot: PilotBaseEntity) {
        this.router.navigateByUrl(`/wiki/spaces/${pilot.identifier}`);
    }

    starChange(pilot: { is_favorite: any; _id: any }) {
        if (pilot.is_favorite) {
            this.pilotStore.favoriteEntry(pilot._id).subscribe({
                next: () => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.settingsSuccess'));
                },
                error: error => this.util.defaultErrorHandler()(error)
            });
        } else {
            this.pilotStore.unfavoriteEntry(pilot._id).subscribe({
                next: () => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.cancelSuccess'));
                },
                error: error => this.util.defaultErrorHandler()(error)
            });
        }
    }
}
