import {
    ChangeDetectorRef,
    Component,
    DestroyRef,
    HostBinding,
    OnInit,
    Signal,
    ViewChild,
    WritableSignal,
    computed,
    signal,
    inject
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { GlobalUsersStore, cache, css, PilotEntryStore } from '@atinc/ngx-styx';
import { DRAFT_GROUP, PageTypes } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { PageTreeService } from '@wiki/app/services/page-tree.service';
import { PageDraftStore } from '@wiki/app/stores/draft-page.store';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { SPACE_SIDEBAR_COLLAPSED } from '@wiki/common/constants/page';
import { IsPagePipe } from '@wiki/common/pipe/page.pipe';
import { ThyTreeNode } from 'ngx-tethys/tree';
import { SpaceDraftSidebarComponent } from '../components/draft-sidebar/draft-sidebar.component';
import { SpacePageSidebarComponent } from '../components/page-sidebar/page-sidebar.component';
import { isLongId } from '@wiki/common/util/common';

export type ActiveType = 'page' | 'draft';

@Component({
    selector: 'space-detail',
    templateUrl: 'detail.component.html',
    host: {
        class: 'wiki-space-detail'
    },
    standalone: false
})
export class SpaceDetailComponent implements OnInit {
    @HostBinding(`class`)
    className = `${css.layout} ${css.displayFlexColumnFill} space-detail-container`;

    @ViewChild('pageSidebar', { read: SpacePageSidebarComponent, static: false })
    pageSidebar: SpacePageSidebarComponent;

    @ViewChild('draftSidebar', { read: SpaceDraftSidebarComponent, static: false })
    draftSidebar: SpaceDraftSidebarComponent;

    activeType: WritableSignal<ActiveType> = signal('page');

    isCollapsed: WritableSignal<boolean> = signal(false);

    spaceId: WritableSignal<string> = signal('');

    draftIdOrShortId: WritableSignal<string> = signal('');

    pageIdOrShortId: WritableSignal<string> = signal('');

    pageId: Signal<string | null> = computed(() => {
        const id = this.pageIdOrShortId();
        const page = this.pagesStore.state().entities.find(item => item._id === id || item.short_id === id);
        return page ? page?._id : null;
    });

    private get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    isHome = computed(() => {
        return this.pageId() === (this.homePage && this.homePage._id);
    });

    get currentSpace() {
        return this.pilotStore.snapshot?.detail;
    }

    get isEmpty() {
        return this.pageTreeService.pageNodes.length;
    }

    get draftTreeComponent() {
        return this.draftSidebar?.draftTreeComponent;
    }

    get pageTreeComponent() {
        return this.pageSidebar?.pageTreeComponent;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private cdr: ChangeDetectorRef,
        public userStore: GlobalUsersStore,
        private pagesStore: PagesStore,
        public pageTreeService: PageTreeService,
        public pageDraftStore: PageDraftStore,
        public isPage: IsPagePipe,
        private destroyRef: DestroyRef
    ) {}

    ngOnInit() {
        this.spaceId.set(this.currentSpace?._id);
        this.getParamId();
        this.setActiveTypeByUrl(this.router.routerState.snapshot.url);

        const cacheCollapseKey = `wiki-${SPACE_SIDEBAR_COLLAPSED}`;
        this.isCollapsed.set(cache.get(cacheCollapseKey));

        // 更新局部状态：页面或者草稿Id、Sidebar Active 状态
        this.router.events.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(event => {
            if (event instanceof NavigationEnd && this.route.snapshot.firstChild) {
                this.getParamId();
                this.setActiveTypeByUrl(this.router.routerState.snapshot.url);

                // 新建页面未发布，路由跳转后选择当前草稿
                if (this.activeType() === 'draft') {
                    this.pageTreeComponent?.setSelectedPage(this.pageId());
                    if (this.draftTreeComponent) {
                        this.draftTreeComponent.selectDraft(this.draftIdOrShortId());
                    }
                }
            }
        });

        this.pageTreeService.updatePageNodesCallback = (pageInfo: PageInfo) => {
            // 数据变更之后，等待渲染完成后进行选中
            setTimeout(() => {
                if (!pageInfo || this.isPage.transform({ type: pageInfo.type })) {
                    this.selectAndScrollToPage();
                }
                if (pageInfo?.type === PageTypes.group) {
                    const node = this.pageTreeComponent.getTreeNode(pageInfo._id);
                    const isDraftGroup = pageInfo._id === DRAFT_GROUP;
                    if (node && isDraftGroup) {
                        this.initDraftGroup(node);
                    }
                    if (!node && !isDraftGroup) {
                        // 删真实分组需要滚动到页面
                        this.selectAndScrollToPage();
                    }
                }
            }, 0);
        };

        this.pageTreeService.updateTreeNodeTitle = (pageInfo: PageInfo) => {
            this.pageTreeComponent && this.pageTreeComponent.updateTreeNodeTitle(pageInfo);
        };

        this.pageTreeService.selectCurrentPageNode = (pidOrShortId: string) => {
            if (this.activeType() === 'page' && this.pageTreeComponent) {
                this.pageTreeComponent.selectPage(pidOrShortId);
            }
        };

        this.pageTreeService.scrollToCurrentNode = () => {
            if (this.activeType() === 'page' && this.pageTreeComponent) {
                this.pageTreeComponent.scrollToCurrentNode();
            }
        };

        this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
            const idOrIdentifier = this.route.snapshot.params?.idOrIdentifier;
            if (isLongId(idOrIdentifier)) {
                let url = this.router.url;
                if (this.currentSpace) {
                    url = url.replace(idOrIdentifier, this.currentSpace.identifier);
                }
                this.router.navigate([url], { relativeTo: this.route, replaceUrl: true });
            }
        });
    }

    selectAndScrollToPage() {
        this.pageTreeService.selectCurrentPageNode(this.pageId());
        // 选中节点选中状态更新和展开后，进行可见性验证和滚动处理
        setTimeout(() => {
            this.pageTreeService.scrollToCurrentNode();
        }, 0);
    }

    initDraftGroup(node: ThyTreeNode) {
        (node as DraftInfo).type = PageTypes.group;
        this.pageTreeComponent.expandedTree(node);
        this.pageTreeComponent.onPageInputEdit(node as ThyTreeNode);
        const draftGroup = this.pageTreeComponent.getDraftGroup();
        if (draftGroup && this.pageTreeComponent.scrollNodeIsVisible(draftGroup)) {
            draftGroup.classList.add('rename-mode');
        } else {
            this.pageTreeComponent.scrollToDraftGroupNode();
            setTimeout(() => {
                this.pageTreeComponent.onPageInputEdit(node as ThyTreeNode);
                this.pageTreeComponent.getDraftGroup()?.classList.add('rename-mode');
            }, 10);
        }
    }

    getParamId() {
        this.pageIdOrShortId.set(this.route.snapshot.firstChild.firstChild?.paramMap.get('pageIdOrShortId'));
        this.draftIdOrShortId.set(this.route.snapshot.firstChild?.paramMap.get('draftIdOrShortId'));
    }

    collapsedChange(isCollapsed: boolean) {
        this.isCollapsed.set(isCollapsed);
    }

    private setActiveTypeByUrl(url: string) {
        const isDraft = url.includes('draft');
        this.activeType.set(isDraft ? 'draft' : 'page');
    }

    gotoDraftTree(type?: ActiveType) {
        this.activeType.set(type);
        this.cdr.detectChanges();
        this.draftSidebar.nativeElement.classList.add('sidebar-slither-to-left');
    }

    gotoPageTree(type?: ActiveType) {
        this.activeType.set(type);
        this.cdr.detectChanges();
        this.pageSidebar.nativeElement.classList.add('sidebar-slither-to-right');
    }
}
