<thy-header styxSecondaryHeader thyDivided styxI18nTracking [thyTitle]="'wiki.space.statistics' | translate"></thy-header>
<thy-content>
  <thy-header styxSecondaryHeader thySize="xlg" styxI18nTracking [thyTitle]="'wiki.statistics.overview' | translate"></thy-header>
  <div class="data-overview border mb-2">
    <thy-grid thyResponsive="self">
      <thy-grid-item thySpan="7">
        <thy-statistic
          class="d-flex justify-content-center align-items-center overview-item"
          styxI18nTracking
          [thyTitle]="'styx.page' | translate: { isPlural: false, isTitle: true }"
          [thyValue]="overview.total_count | number"
          [thyValueStyle]="{ 'font-size': '42px', color: '#333' }"
          thyTitlePosition="top"
        ></thy-statistic>
      </thy-grid-item>
      <thy-grid-item thySpan="7">
        <thy-statistic
          class="d-flex justify-content-center align-items-center overview-item"
          styxI18nTracking
          [thyTitle]="'wiki.statistics.newThisWeek' | translate"
          [thyValue]="overview.weekly_new_count | number"
          [thyValueStyle]="{ 'font-size': '42px', color: '#333' }"
          thyTitlePosition="top"
        ></thy-statistic>
      </thy-grid-item>
      <thy-grid-item thySpan="3">
        <thy-statistic
          class="d-flex flex-column justify-content-center align-items-center border-left"
          [thyValue]="overview.document_count | number"
          thyColor="#666"
          thyTitlePosition="top"
        >
          <ng-template #title>
            <styx-awesome-text
              styxI18nTracking
              [styxText]="'styx.document' | translate: { isPlural: false, isTitle: true }"
              styxIcon="file"
              [styxIconColor]="1 | pageIconColor"
            ></styx-awesome-text>
          </ng-template>
        </thy-statistic>
      </thy-grid-item>
      <thy-grid-item thySpan="3">
        <thy-statistic
          class="d-flex flex-column justify-content-center align-items-center"
          [thyValue]="overview.table_count | number"
          thyColor="#666"
          thyTitlePosition="top"
        >
          <ng-template #title>
            <styx-awesome-text
              [styxText]="'wiki.page.table' | translate: { isPlural: false, isTitle: true }"
              styxIcon="sketchpad"
              [styxIconColor]="4 | pageIconColor"
            ></styx-awesome-text>
          </ng-template>
        </thy-statistic>
      </thy-grid-item>
      <thy-grid-item thySpan="3">
        <thy-statistic
          class="d-flex flex-column justify-content-center align-items-center"
          [thyValue]="overview.board_count | number"
          thyColor="#666"
          thyTitlePosition="top"
        >
          <ng-template #title>
            <styx-awesome-text
              styxI18nTracking
              [styxText]="'styx.paintBoard' | translate: { isPlural: false, isTitle: true }"
              styxIcon="sketchpad"
              [styxIconColor]="3 | pageIconColor"
            ></styx-awesome-text>
          </ng-template>
        </thy-statistic>
      </thy-grid-item>
    </thy-grid>
  </div>

  <thy-tabs class="thy-layout d-flex mt-6" [thyActiveTab]="currentTabKey" (thyActiveTabChange)="activeTabChange($event)">
    <thy-tab id="page-details" styxI18nTracking [thyTitle]="'wiki.statistics.pageDetails' | translate">
      <wiki-page-details></wiki-page-details>
    </thy-tab>
    @if (!(pilotStore.snapshot?.detail?.scope_type | scopeTypeIsPersonal)) {
      <thy-tab id="member-details" styxI18nTracking [thyTitle]="'wiki.statistics.memberDetails' | translate">
        <wiki-member-details></wiki-member-details>
      </thy-tab>
    }
  </thy-tabs>
</thy-content>
