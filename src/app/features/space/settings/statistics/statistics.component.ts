import { Component, DestroyRef, HostBinding, inject, OnInit } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PaginationResponseData, PilotEntryStore, css } from '@atinc/ngx-styx';
import { SpaceOverviewInfo } from '@wiki/app/entities/space-info';
import { SpaceApiService } from '@wiki/app/services';
import { WikiErrorService } from '@wiki/app/services/error.service';

@Component({
    selector: 'wiki-statistics',
    templateUrl: './statistics.component.html',
    standalone: false
})
export class WikiStatisticsComponent implements OnInit {
    @HostBinding('class') className = `${css.layout} wiki-statistics`;

    overview: SpaceOverviewInfo = {
        total_count: 0,
        weekly_new_count: 0,
        document_count: 0,
        board_count: 0,
        table_count: 0
    };

    currentTabKey = 'page-details';

    get spaceId() {
        return this.pilotStore.snapshot.detail._id;
    }

    public pilotStore = inject(PilotEntryStore);

    constructor(
        private destroyRef: DestroyRef,
        private spaceApiService: SpaceApiService,
        private errorService: WikiErrorService
    ) {}

    ngOnInit() {
        this.getOverview();
    }

    getOverview() {
        this.spaceApiService
            .getOverviewData(this.spaceId)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (data: PaginationResponseData<SpaceOverviewInfo>) => {
                    if (data) {
                        this.overview = data.value;
                    }
                },
                error: error => {
                    this.errorService.defaultErrorHandler(error);
                }
            });
    }

    activeTabChange(event: string) {
        this.currentTabKey = event;
    }
}
