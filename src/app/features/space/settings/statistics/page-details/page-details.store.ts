import { inject, Injectable } from '@angular/core';
import { Direction, GlobalUsersStore, Id, PaginationResponseData, PilotEntryStore, helpers } from '@atinc/ngx-styx';
import { Action, EntityState, EntityStore } from '@tethys/store';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceApiService } from '@wiki/app/services';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { Observable, Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';

export interface PageDetailsState extends EntityState<PageInfo, any> {}

@Injectable()
export class PageDetailsStore extends EntityStore<PageDetailsState, PageInfo> {
    private pilotStore = inject(PilotEntryStore, { optional: true });

    pageInfoSubscriber: Subscription;

    constructor(
        public globalPageStore: PageEventBus,
        private globalUsersStore: GlobalUsersStore,
        private spaceApiService: SpaceApiService
    ) {
        super({ entities: [] });
        this.subscribeGlobalPage();
    }

    fetchPages(
        spaceId: string,
        params: { pi: number; ps: number; keywords?: string; sort_by?: string; sort_direction?: Direction }
    ): Observable<PaginationResponseData<PageInfo[]>> {
        return this.spaceApiService.fetchPageDetails(spaceId, params).pipe(
            tap((data: PaginationResponseData<PageInfo[], any>) => {
                this.initializeWithReferences(data.value, data.references, {
                    pageIndex: data.page_index,
                    pageSize: data.page_size,
                    pageCount: data.page_count,
                    count: data.count
                });
                this.globalUsersStore.addUsers(data.references.members);
            })
        );
    }

    @Action()
    pureUpdatePageById(id: Id, page: Partial<PageInfo>) {
        if (helpers.find(this.snapshot.entities, { _id: id })) {
            this.update(id, page);
        }
    }

    subscribeGlobalPage() {
        this.pageInfoSubscriber = this.globalPageStore
            .onPageInfoChange$()
            .pipe(
                tap((page: PageInfo) => {
                    if (page && page?.space_id === this.pilotStore.snapshot.detail._id) {
                        this.pureUpdatePageById(page._id, page);
                    }
                })
            )
            .subscribe();
    }
}
