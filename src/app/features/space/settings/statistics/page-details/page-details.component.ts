import { Component, HostBinding, inject, OnInit, ViewContainerRef } from '@angular/core';
import { PilotEntryStore, StyxSortMenuComponent } from '@atinc/ngx-styx';
import { css, Direction } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { getCachedPageSize, StyxTableEmitEvent } from '@atinc/ngx-styx/table';
import { asyncBehavior } from '@tethys/cdk/behaviors';
import { API_PREFIX } from '@wiki/app/constants';
import { PageService } from '@wiki/app/services/util/page.service';
import { ScopeTypeIsPersonalPipe } from '@wiki/common/pipe/scope-type.pipe';
import { ThyPopover } from 'ngx-tethys/popover';
import { PageDetailsStore } from './page-details.store';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-page-details',
    templateUrl: './page-details.component.html',
    providers: [PageDetailsStore],
    standalone: false
})
export class WikiPageDetailsComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @HostBinding('class') className = `${css.layout} wiki-page-details`;

    searchText = '';

    pageIndex = 0;

    pageSize = getCachedPageSize();

    sortConfig = {
        sortBy: 'updated_at',
        sortDirection: Direction.descending
    };

    sortOptions = [
        {
            key: 'name',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.page.name'),
            type: 1,
            from: 1,
            property_key: 'name',
            raw_key: 'name',
            value_path: 'name'
        },
        {
            key: 'created_by',
            name: this.translate.instant<I18nSourceDefinitionType>('styx.createdBy'),
            type: 9,
            from: 1,
            property_key: 'created_by',
            raw_key: 'created_by',
            value_path: 'created_by'
        },
        {
            key: 'updated_at',
            name: this.translate.instant<I18nSourceDefinitionType>('styx.updatedAt'),
            type: 4,
            from: 1,
            property_key: 'updated_at',
            raw_key: 'updated_at',
            value_path: 'updated_at'
        },
        {
            key: 'version_count',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.page.versions'),
            type: 28,
            from: 1,
            property_key: 'version_count',
            raw_key: 'version_count',
            value_path: 'version_count'
        },
        {
            key: 'word_count',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.page.words'),
            type: 28,
            from: 1,
            property_key: 'word_count',
            raw_key: 'word_count',
            value_path: 'word_count'
        },
        {
            key: 'reading',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.page.reads'),
            type: 28,
            from: 1,
            property_key: 'reading',
            raw_key: 'reading',
            value_path: 'reading'
        },
        {
            key: 'comment_count',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.page.comments'),
            type: 28,
            from: 1,
            property_key: 'comment_count',
            raw_key: 'comment_count',
            value_path: 'comment_count'
        }
    ];

    exportConfig = {};

    get pageCount() {
        return this.pageDetailsStore.snapshot?.pagination?.count ?? 0;
    }

    get space() {
        return this.pilotStore.snapshot.detail;
    }

    get pagination() {
        return this.pageDetailsStore.snapshot?.pagination;
    }

    public pilotStore = inject(PilotEntryStore);

    constructor(
        public pageDetailsStore: PageDetailsStore,
        private viewContainerRef: ViewContainerRef,
        private thyPopover: ThyPopover,
        private pageService: PageService,
        private scopeTypeIsPersonalPipe: ScopeTypeIsPersonalPipe
    ) {}

    ngOnInit() {
        if (this.scopeTypeIsPersonalPipe.transform(this.pilotStore.snapshot.detail.scope_type)) {
            this.sortOptions = this.sortOptions.filter(item => item.key !== 'created_by');
        }
        this.fetchPages();
    }

    detailsFetcher = asyncBehavior(() => {
        const params = {
            keywords: this.searchText,
            pi: this.pageIndex,
            ps: this.pageSize,
            sort_by: this.sortConfig.sortBy,
            sort_direction: this.sortConfig.sortDirection
        };
        return this.pageDetailsStore.fetchPages(this.space._id, params);
    });

    fetchPages() {
        this.generateExportConfig();
        this.detailsFetcher.execute();
    }

    onPageIndexChange(event: number) {
        this.pageIndex = event;
        this.fetchPages();
    }

    onSearch(keywords: string) {
        this.pageIndex = 0;
        this.searchText = keywords;
        this.fetchPages();
    }

    openViewSort(event: Event) {
        this.thyPopover.open(StyxSortMenuComponent, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            insideClosable: true,
            originActiveClass: 'active',
            initialState: {
                sortMenus: this.sortOptions,
                sortBy: this.sortConfig.sortBy,
                sortDirection: this.sortConfig.sortDirection,
                showDefaultSort: false,
                onSort: (e: Event, sortBy: string, sortDirection: Direction) => {
                    this.sortChange({
                        sortBy: sortBy,
                        sortDirection: sortDirection
                    });
                }
            }
        });
    }

    sortChange(event: { sortBy: string; sortDirection: Direction }) {
        this.sortConfig = {
            sortBy: event.sortDirection ? event.sortBy : '',
            sortDirection: event.sortDirection
        };
        this.fetchPages();
    }

    pageSizeChange(event: number) {
        this.pageSize = event;
        this.fetchPages();
    }

    rowClick(event: StyxTableEmitEvent) {
        this.pageService.openWikiPageDialog({
            pageId: event.row._id,
            viewContainerRef: this.viewContainerRef
        });
    }

    generateExportConfig() {
        this.exportConfig = {
            exportMethod: 'post',
            exportUrl: `${API_PREFIX}/spaces/${this.space._id}/statistics/pages/export?file_format=xlsx&date_format=normal`,
            exportParams: {
                columns: this.sortOptions.map(item => item.key),
                view: {
                    keywords: this.searchText,
                    sort_by: this.sortConfig.sortBy,
                    sort_direction: this.sortConfig.sortDirection
                }
            }
        };
    }
}
