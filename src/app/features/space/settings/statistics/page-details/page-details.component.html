<styx-content-main styxMode="entry">
  <list-filter-view
    class="d-flex align-items-center"
    mode="table"
    [listCount]="pageCount"
    [viewTemplateRef]="extraOperation"
    [isOperation]="space?.permissions | hasAnyPermission: ['space_export_statistics_data']"
    [operationTemplateRef]="operationTemplate"
    (keywordsChange)="onSearch($event)"
  >
  </list-filter-view>

  <styx-content-main-body class="pl-5 pb-0">
    <styx-table
      styxMode="lite"
      styxCachePrefix="wiki-space-setting-page-details-table-column"
      [styxLoadingDone]="detailsFetcher.loadingDone()"
      [styxData]="pageDetailsStore.entities$ | async"
      [styxShowSizeChanger]="true"
      [styxShowHeaderSetting]="false"
      [styxShowColumnAction]="false"
      [styxSortable]="false"
      [styxSelectable]="false"
      [styxShowIdentifier]="false"
      [styxSortConfig]="sortConfig"
      [styxPagination]="pagination"
      (styxSort)="sortChange($event)"
      (styxPageIndexChange)="onPageIndexChange($event)"
      (styxPageSizeChange)="pageSizeChange($event)"
      (styxClick)="rowClick($event)"
    >
      <styx-table-column
        styxProperty
        styxKey="name"
        styxI18nTracking
        [styxName]="'common.name' | translate"
        styxWidth="40%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          <div class="text-truncate">
            <styx-awesome-text
              class="ml-n1"
              [styxText]="row?.name"
              [styxCode]="row?.emoji_icon"
              [styxIcon]="row | pageIcon"
              [styxIconColor]="row | pageIconColor"
            ></styx-awesome-text>
          </div>
        </ng-template>
      </styx-table-column>

      @if (!(pilotStore.snapshot?.detail?.scope_type | scopeTypeIsPersonal)) {
        <styx-table-column
          styxProperty
          styxKey="created_by"
          styxI18nTracking
          [styxName]="'styx.createdBy' | translate"
          styxWidth="14%"
          [styxSortable]="true"
        >
          <ng-template #cell let-row="row">
            <thy-avatar
              class="text-truncate align-middle"
              thySize="xs"
              thyShowName="true"
              [thySrc]="(row.created_by | user)?.avatar"
              [thyName]="(row.created_by | user)?.display_name"
            ></thy-avatar>
          </ng-template>
        </styx-table-column>
      }

      <styx-table-column
        styxProperty
        styxKey="updated_at"
        styxI18nTracking
        [styxName]="'styx.updatedAt' | translate"
        styxWidth="14%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          <span>{{ row?.updated_at | dateTimeAutoFormat }}</span>
        </ng-template>
      </styx-table-column>

      <styx-table-column
        styxProperty
        styxKey="version_count"
        styxI18nTracking
        [styxName]="'wiki.statistics.page.versions' | translate"
        styxWidth="8%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          <span>{{ row?.version_count | number }}</span>
        </ng-template>
      </styx-table-column>

      <styx-table-column
        styxProperty
        styxKey="word_count"
        styxI18nTracking
        [styxName]="'wiki.statistics.page.words' | translate"
        styxWidth="8%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          @if ((row | isBoard) || (row | isTable)) {
            <span>-</span>
          }
          @if (row | isDocument) {
            <span>{{ row?.word_count | number }}</span>
          }
        </ng-template>
      </styx-table-column>

      <styx-table-column
        styxProperty
        styxKey="reading"
        styxI18nTracking
        [styxName]="'wiki.statistics.page.reads' | translate"
        styxWidth="8%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          <span>{{ row?.reading | number }}</span>
        </ng-template>
      </styx-table-column>

      <styx-table-column
        styxProperty
        styxKey="comment_count"
        styxI18nTracking
        [styxName]="'wiki.statistics.page.comments' | translate"
        styxWidth="8%"
        [styxSortable]="true"
      >
        <ng-template #cell let-row="row">
          <span>{{ row?.comment_count | number }}</span>
        </ng-template>
      </styx-table-column>
    </styx-table>
  </styx-content-main-body>
</styx-content-main>

<ng-template #extraOperation>
  <a class="mr-5" href="javascript:;" thyAction thyActionIcon="sort" (click)="openViewSort($event)" styxI18nTracking>{{
    'styx.sort' | translate
  }}</a>
</ng-template>

<ng-template #operationTemplate>
  <a
    href="javascript:;"
    thyAction
    thyActionIcon="file-export"
    [styxExport]="exportConfig.exportUrl"
    [styxExportMethod]="exportConfig.exportMethod"
    [styxExportParams]="exportConfig.exportParams"
    styxI18nTracking
    >{{ 'common.export' | translate }}</a
  >
</ng-template>
