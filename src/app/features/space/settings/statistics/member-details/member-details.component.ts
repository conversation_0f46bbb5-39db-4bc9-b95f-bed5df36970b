import { Component, HostBinding, inject, OnInit } from '@angular/core';
import { Direction, StyxSortMenuComponent, StyxTranslateService, css, getCachedPageSize, PilotEntryStore } from '@atinc/ngx-styx';
import { asyncBehavior } from '@tethys/cdk/behaviors';
import { API_PREFIX } from '@wiki/app/constants';
import { ThyPopover } from 'ngx-tethys/popover';
import { MemberDetailStore } from './member-details.store';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-member-details',
    templateUrl: './member-details.component.html',
    providers: [MemberDetailStore],
    standalone: false
})
export class WikiMemberDetailsComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @HostBinding('class') className = `${css.layout} wiki-member-details`;

    searchText = '';

    pageIndex = 0;

    pageSize = getCachedPageSize();

    sortConfig = {
        sortBy: 'created_page_count',
        sortDirection: Direction.descending
    };

    sortOptions = [
        {
            key: 'display_name',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.member.name'),
            type: 9,
            from: 1,
            property_key: 'display_name',
            raw_key: 'display_name',
            value_path: 'display_name',
            icon: 'font'
        },
        {
            key: 'created_page_count',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.member.createdPages'),
            type: 28,
            from: 1,
            property_key: 'created_page_count',
            raw_key: 'created_page_count',
            value_path: 'created_page_count',
            icon: 'version-circle'
        },
        {
            key: 'edited_page_count',
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.member.editedPages'),
            type: 28,
            from: 1,
            property_key: 'edited_page_count',
            raw_key: 'edited_page_count',
            value_path: 'edited_page_count',
            icon: 'user-bold'
        }
    ];

    exportConfig = {};

    get space() {
        return this.pilotStore.snapshot.detail;
    }

    get memberCount() {
        return this.memberDetailStore.snapshot?.pagination?.count ?? 0;
    }

    get pagination() {
        return this.memberDetailStore.snapshot?.pagination;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private thyPopover: ThyPopover,
        public memberDetailStore: MemberDetailStore
    ) {}

    ngOnInit() {
        this.fetchMembers();
    }

    membersFetcher = asyncBehavior(() => {
        const params = {
            keywords: this.searchText,
            pi: this.pageIndex,
            ps: this.pageSize,
            sortBy: this.sortConfig.sortBy,
            sortDirection: this.sortConfig.sortDirection
        };
        return this.memberDetailStore.fetchMembers(this.space._id, params);
    });

    fetchMembers() {
        this.generateExportConfig();
        this.membersFetcher().execute();
    }

    onPageChange(event: number) {
        this.pageIndex = event;
        this.fetchMembers();
    }

    onSearch(keywords: string) {
        this.pageIndex = 0;
        this.searchText = keywords;
        this.fetchMembers();
    }

    openViewSort(event: Event) {
        this.thyPopover.open(StyxSortMenuComponent, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            insideClosable: true,
            originActiveClass: 'active',
            initialState: {
                sortMenus: this.sortOptions,
                sortBy: this.sortConfig.sortBy,
                sortDirection: this.sortConfig.sortDirection,
                showDefaultSort: false,
                onSort: (e: Event, sortBy: string, sortDirection: Direction) => {
                    this.sortChange({
                        sortBy: sortBy,
                        sortDirection: sortDirection
                    });
                }
            }
        });
    }

    sortChange(event: { sortBy: string; sortDirection: Direction }) {
        this.sortConfig = {
            sortBy: event.sortDirection ? event.sortBy : '',
            sortDirection: event.sortDirection
        };
        this.fetchMembers();
    }

    pageSizeChange(event: number) {
        this.pageSize = event;
        this.fetchMembers();
    }

    generateExportConfig() {
        this.exportConfig = {
            exportMethod: 'post',
            exportUrl: `${API_PREFIX}/spaces/${this.space._id}/statistics/members/export?file_format=xlsx&date_format=normal`,
            exportParams: {
                columns: ['display_name', 'department', 'job', 'created_page_count', 'edited_page_count'],
                view: {
                    keywords: this.searchText,
                    sort_by: this.sortConfig.sortBy,
                    sort_direction: this.sortConfig.sortDirection
                }
            }
        };
    }
}
