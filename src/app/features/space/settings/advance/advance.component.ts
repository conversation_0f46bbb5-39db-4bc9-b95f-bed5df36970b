import { Component, HostBinding, OnInit, DestroyRef, inject, ViewContainerRef } from '@angular/core';
import { Router } from '@angular/router';
import { of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { UtilService, css, PublicPathPipe, Is, StyxTranslateService, PilotEntryStore } from '@atinc/ngx-styx';
import { SpaceApiService } from '@wiki/app/services';
import { SpaceService } from '@wiki/app/services/util/space.service';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-settings-advance',
    templateUrl: './advance.component.html',
    providers: [PublicPathPipe],
    standalone: false
})
export class SettingsAdvanceComponent implements OnInit {
    @HostBinding(`class`)
    className = `${css.displayFlexColumnFill}`;

    public pilotStore = inject(PilotEntryStore);

    currentSpace = this.pilotStore.select(state => state.detail);

    sharedPointKey = 'spaceShare';

    public get isConfiguration() {
        return this.router.url.includes('configuration');
    }

    private viewContainerRef = inject(ViewContainerRef);

    constructor(
        private router: Router,
        private spaceApiService: SpaceApiService,
        private util: UtilService,
        private spaceService: SpaceService,
        private destroyRef: DestroyRef,
        private translate: StyxTranslateService
    ) {}

    ngOnInit() {}

    moveSpace() {
        this.spaceService.moveSpace(this.currentSpace(), this.viewContainerRef);
    }

    archiveSpace() {
        this.spaceService.archiveSpace(this.currentSpace().name, () => {
            return this.pilotStore.archiveEntry(this.currentSpace()._id).pipe(
                tap(() => {
                    const space = {
                        ...this.currentSpace(),
                        is_favorite: Is.no,
                        is_archived: Is.yes
                    };
                    const pilot = this.pilotStore.getEntry(space._id);
                    pilot.is_favorite = Is.no;
                    this.pilotStore.updateEntryAction(space);
                    this.pilotStore.unfavoriteEntryAction(space._id);
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>(`common.archiveSuccess`));
                }),
                map(() => {
                    return true;
                }),
                catchError(error => {
                    this.util.defaultErrorHandler()(error);
                    return of(true);
                })
            );
        });
    }

    activateSpace() {
        this.spaceApiService.activeSpace(this.currentSpace()._id).subscribe({
            next: () => {
                this.pilotStore.updateEntryAction({ ...this.currentSpace(), is_archived: 0 });
                this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>(`common.activeSuccess`));
            },
            error: error => {
                this.util.defaultErrorHandler()(error);
            }
        });
    }

    deleteSpace() {
        this.spaceService.deleteSpace(this.currentSpace().name, () => {
            return this.pilotStore.deleteEntry(this.currentSpace()._id).pipe(
                tap(() => {
                    this.pilotStore.deleteEntry(this.currentSpace()._id);
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>(`common.deleteSuccess`));
                    // this.pilotStore.updateEntryAction({});
                    this._goWikiRootPage();
                }),
                map(() => {
                    return true;
                }),
                catchError(error => {
                    this.util.defaultErrorHandler()(error);
                    return of(true);
                })
            );
        });
    }

    private _goWikiRootPage() {
        this.router.navigate(['/wiki']);
    }
}
