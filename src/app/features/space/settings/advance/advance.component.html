<thy-header styxSecondaryHeader thyDivided styxI18nTracking [thyTitle]="'styx.advancedSettings' | translate"></thy-header>
<thy-content-main>
  @if (currentSpace().permissions | hasPermission: 'space_move') {
    <styx-content-setting-card
      styxI18nTracking
      [styxTitle]="'wiki.space.move' | translate"
      [styxDescription]="'wiki.space.settings.move.description' | translate"
    >
      <ng-template #operation>
        <button thyButton="primary" (click)="moveSpace()" translate="common.move"></button>
      </ng-template>
    </styx-content-setting-card>
  }
  @if (!currentSpace().is_archived && currentSpace().permissions | hasPermission: 'space_archive_setting') {
    <styx-content-setting-card
      styxI18nTracking
      [styxTitle]="'wiki.space.archive' | translate"
      [styxDescription]="'wiki.space.settings.archive.description' | translate"
    >
      <ng-template #operation>
        <button thyButton="primary" (click)="archiveSpace()" translate="common.archive"></button>
      </ng-template>
    </styx-content-setting-card>
  }
  @if (currentSpace().is_archived && currentSpace().permissions | hasPermission: 'space_archive_setting') {
    <styx-content-setting-card
      styxI18nTracking
      [styxTitle]="'wiki.space.activate' | translate"
      [styxDescription]="'wiki.space.settings.activate.description' | translate"
    >
      <ng-template #operation>
        <button thyButton="primary" (click)="activateSpace()" translate="common.active"></button>
      </ng-template>
    </styx-content-setting-card>
  }

  @if (currentSpace().permissions | hasPermission: 'space_delete_setting') {
    <styx-danger-operation-divider></styx-danger-operation-divider>
    <styx-content-setting-card
      styxI18nTracking
      [styxTitle]="'wiki.space.delete' | translate"
      [styxDescription]="'wiki.space.settings.delete.description' | translate"
    >
      <ng-template #operation>
        <button thyButton="danger" (click)="deleteSpace()" translate="common.delete"></button>
      </ng-template>
    </styx-content-setting-card>
  }
</thy-content-main>
