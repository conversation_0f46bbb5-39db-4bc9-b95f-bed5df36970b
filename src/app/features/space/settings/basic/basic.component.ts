import { Component, HostBinding, ViewChild, effect, inject } from '@angular/core';
import {
    css,
    ResponseData,
    UtilService,
    PilotScopeType,
    injectBizValidatorMaxLengths,
    StyxTranslateService,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { finalize } from 'rxjs/operators';
import { Router } from '@angular/router';
import { SpaceInfo } from '../../../../entities/space-info';
import { ThyFormDirective } from 'ngx-tethys/form';
import { SpaceApiService } from '../../../../services';
import { SpaceStatus } from '../../../../constants';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-space-settings-basic',
    templateUrl: './basic.component.html',
    standalone: false
})
export class SpaceSettingsBasicComponent {
    @HostBinding(`class`)
    className = `${css.displayFlexColumnFill} space-setting-basic`;

    currentSpace: SpaceInfo = {};

    styxName = this.translate.instant<I18nSourceDefinitionType>('styx.space', { isTitle: false, isPlural: false });

    userGroupId: string;

    scopeTypes = [PilotScopeType.organization, PilotScopeType.personal, PilotScopeType.team];

    descriptionMaxLength = injectBizValidatorMaxLengths().multiLineText;

    @ViewChild(ThyFormDirective, { static: true }) thyForm: ThyFormDirective;

    public saving: boolean;

    public validatorConfig = {
        validationMessages: {
            spaceName: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.space.nameRequired')
            },
            'styx-input-identifier': {
                pattern: '英文字母/下划线/数字/连接线（不超过15字符）',
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.space.identifierRequired')
            }
        }
    };

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private util: UtilService,
        private router: Router,
        private translate: StyxTranslateService
    ) {
        effect(() => {
            const pilot = this.pilotStore.state().detail;
            if (pilot) {
                this.currentSpace = { ...pilot };
                this.userGroupId =
                    this.currentSpace.user_group_ids && this.currentSpace.user_group_ids.length ? this.currentSpace.user_group_ids[0] : '';
            }
        });
    }

    private pagePrefixError() {
        this.thyForm.validator.setElementErrorMessage(
            'styx-input-identifier',
            this.translate.instant<I18nSourceDefinitionType>('wiki.space.identifierExists')
        );
    }

    private saveFail(error) {
        if (error.code === SpaceStatus.spaceIdentifierIsExisted) {
            this.pagePrefixError();
        }
    }

    colorSelect($event) {
        const _color = $event.color;
        this.currentSpace.color = _color;
    }

    editSpace() {
        if (this.saving) {
            return;
        }

        this.saving = true;
        const { _id, name, color, identifier, description, scope_type, visibility, category_id } = this.currentSpace;
        const newSpace = {
            _id,
            name,
            color,
            identifier,
            description,
            scope_type,
            visibility,
            category_id: category_id || null
        };

        if (this.currentSpace.scope_type !== PilotScopeType.personal) {
            newSpace['visibility'] = this.currentSpace.visibility;
        }

        this.pilotStore
            .updateEntry(newSpace)
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe({
                next: (data: ResponseData) => {
                    this.router.navigate(['/wiki/spaces', newSpace.identifier, 'settings', 'basic'], {
                        replaceUrl: true
                    });
                    this.util.notify.show({
                        type: 'success',
                        title: this.translate.instant<I18nSourceDefinitionType>('common.modifySuccess'),
                        pauseOnHover: false
                    });
                },
                error: error => {
                    this.saveFail(error);
                    this.util.defaultErrorHandler(error);
                }
            });
    }
}
