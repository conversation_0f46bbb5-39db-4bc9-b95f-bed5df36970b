<thy-header styxSecondaryHeader thyDivided styxI18nTracking [thyTitle]="'styx.baseInfo' | translate"></thy-header>
<thy-content-main>
  <styx-content-setting-form>
    <form
      thyForm
      name="addEditSpaceForm"
      thyLayout="vertical"
      [thyFormValidatorConfig]="validatorConfig"
      #demoVerticalThyForm="thyForm"
      #demoVerticalForm="ngForm"
    >
      <div thyRow [thyGutter]="20">
        <thy-form-group thyCol [thySpan]="24" styxI18nTracking [thyLabelText]="'styx.scopeType' | translate">
          <styx-pilot-scope-select
            name="scopeSelect"
            [styxValue]="userGroupId"
            [(ngModel)]="currentSpace.scope_type"
            [styxScopeTypes]="scopeTypes"
            [styxScopeDisabled]="true"
          ></styx-pilot-scope-select>
        </thy-form-group>
        <thy-form-group
          thyCol
          [thySpan]="15"
          thyLabelRequired
          styxI18nTracking
          [thyLabelText]="'styx.space' | translate: { isTitle: true, isPlural: false }"
        >
          <styx-pilot-input-name
            [(ngModel)]="currentSpace"
            [styxIconName]="'book-fill'"
            thyAutofocus
            styxName="spaceName"
            name="space"
            required
            styxI18nTracking
            [styxPlaceholder]="'wiki.space.namePlaceholder' | translate"
            [styxResultNgModel]="spaceIdentifier"
          ></styx-pilot-input-name>
        </thy-form-group>
        <thy-form-group thyCol [thySpan]="9" thyLabelRequired styxI18nTracking [thyLabelText]="'wiki.space.identifier' | translate">
          <styx-input-identifier
            #spaceIdentifier="ngModel"
            [(ngModel)]="currentSpace.identifier"
            name="identifier"
            styxI18nTracking
            [styxTooltip]="'wiki.space.identifierHint' | translate"
          ></styx-input-identifier>
        </thy-form-group>
        <thy-form-group thyCol [thySpan]="24" styxI18nTracking [thyLabelText]="'styx.visibility' | translate">
          <styx-pilot-visible-select
            name="visibleSelect"
            [(ngModel)]="currentSpace.visibility"
            [styxDisabled]="currentSpace.scope_type | pilotScopeIsPersonal"
            [styxScope]="currentSpace.scope_type"
            [styxName]="styxName"
          ></styx-pilot-visible-select>
        </thy-form-group>
        <thy-form-group thyCol [thySpan]="24" styxI18nTracking [thyLabelText]="'styx.category' | translate">
          <styx-pilot-category-select name="categorySelect" [(styxValue)]="currentSpace.category_id"></styx-pilot-category-select>
        </thy-form-group>
        <thy-form-group thyCol [thySpan]="24" styxI18nTracking [thyLabelText]="'styx.desc' | translate">
          <thy-input-group>
            <textarea
              thyInput
              name="description"
              class="form-control"
              rows="3"
              [(ngModel)]="currentSpace.description"
              styxI18nTracking
              [placeholder]="'wiki.space.description' | translate"
              [maxlength]="descriptionMaxLength"
            ></textarea>
            <ng-template #suffix>
              <thy-input-count></thy-input-count>
            </ng-template>
          </thy-input-group>
        </thy-form-group>
        <thy-form-group-footer class="space-setting-basic-footer">
          <button
            thyButton="primary"
            [thyLoading]="saving"
            [thyLoadingText]="'common.submitting' | translate"
            (thyFormSubmit)="editSpace()"
            translate="common.ok"
          ></button>
        </thy-form-group-footer>
      </div>
    </form>
  </styx-content-setting-form>
</thy-content-main>
