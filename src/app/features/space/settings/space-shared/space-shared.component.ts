import { Component, HostBinding, OnInit, inject, DestroyRef } from '@angular/core';
import { Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { UtilService, css, PublicPathPipe, StyxPricingService, StyxTranslateService, PilotEntryStore } from '@atinc/ngx-styx';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { SpaceShareSettingsComponent } from './share/share.component';
import { ThyDialogSizes } from 'ngx-tethys/dialog';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Component({
    selector: 'wiki-settings-shared',
    templateUrl: './space-shared.component.html',
    providers: [PublicPathPipe],
    standalone: false
})
export class SpaceSharedComponent implements OnInit {
    @HostBinding(`class`)
    className = `${css.displayFlexColumnFill}`;

    currentSpace?: SpaceInfo = {};

    sharedPointKey = 'spaceShare';

    translate = inject(StyxTranslateService);

    public get isConfiguration() {
        return this.router.url.includes('configuration');
    }

    private destroyRef = inject(DestroyRef);

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private util: UtilService,
        private pricingService: StyxPricingService
    ) {}

    ngOnInit() {
        this.pilotStore
            .select$(state => state.detail)
            .pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(space => !!space)
            )
            .subscribe(space => {
                this.currentSpace = { ...space };
            });
    }

    shareSetting() {
        if (!this.pricingService.ensureCheckpointEnabled(this.sharedPointKey)) {
            return;
        }

        const dialogRef = this.util.dialog.open(SpaceShareSettingsComponent, {
            initialState: {
                isShared: this.currentSpace.is_shared,
                spaceId: this.currentSpace._id,
                spaceName: this.currentSpace.name
            },
            canClose: () => {
                const { isSettingChange } = dialogRef.componentInstance;
                if (isSettingChange) {
                    this.util.dialog.confirm({
                        title: this.translate.instant<I18nSourceDefinitionType>('common.confirmExit'),
                        content: this.translate.instant<I18nSourceDefinitionType>('wiki.spaceSharing.exitConfirm'),
                        footerAlign: 'right',
                        okType: 'danger',
                        okText: this.translate.instant<I18nSourceDefinitionType>('common.ok'),
                        cancelText: this.translate.instant<I18nSourceDefinitionType>('common.cancel'),
                        onOk: () => {
                            dialogRef.close(null, true);
                        }
                    });
                    return false;
                }
                return true;
            },
            size: ThyDialogSizes.maxLg,
            height: '650px'
        });
    }
}
