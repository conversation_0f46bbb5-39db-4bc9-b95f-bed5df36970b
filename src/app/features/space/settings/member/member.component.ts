import { Component, HostBinding, OnInit, inject } from '@angular/core';
import { PilotEntryStore, Visibility, css, StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-space-settings-member',
    templateUrl: './member.component.html',
    standalone: false
})
export class SpaceSettingsMemberComponent implements OnInit {
    @HostBinding(`class`)
    className = `${css.layout}`;

    styxDescription: string;

    pilotStore = inject(PilotEntryStore);

    private translate = inject(StyxTranslateService);

    ngOnInit() {
        this.styxDescription =
            this.pilotStore.snapshot.detail?.visibility === Visibility.public
                ? this.translate.instant<I18nSourceDefinitionType>('wiki.space.removeMemberHint')
                : '';
    }
}
