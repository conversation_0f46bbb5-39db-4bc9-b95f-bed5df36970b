import { Component, HostBinding, inject } from '@angular/core';
import { PilotEntryStore, StyxTranslateService, WikiBroadObjectTypes, css } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-space-setting-tags',
    templateUrl: './tags.component.html',
    standalone: false
})
export class SpaceSettingTagsComponent {
    @HostBinding(`class`) className = `${css.layout}`;

    apiPrefix = '/api/wiki';

    private pilotStore = inject(PilotEntryStore);

    pilotId = this.pilotStore.select(state => state.detail?._id || '');

    broadObjectType = WikiBroadObjectTypes.page;

    creatable = true;

    editable = true;

    deletable = true;

    private translate = inject(StyxTranslateService);

    deleteDescription = this.translate.instant<I18nSourceDefinitionType>('wiki.configuration.tags.deleteDescription');
}
