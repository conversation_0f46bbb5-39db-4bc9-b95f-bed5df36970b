<thy-header styxSecondaryHeader thyDivided styxI18nTracking [thyTitle]="'wiki.space.directory' | translate">
  <ng-template #headerOperation>
    @if (homePage?.permissions | hasAnyPermission: ['page_create']) {
      <a thyButton="primary" thyIcon="plus-bold" thySize="md" thyPlacement="bottomRight" [thyDropdown]="menu" styxI18nTracking>
        {{ 'common.new' | translate }} <thy-icon thyIconName="caret-down"></thy-icon>
      </a>
    }
    <thy-dropdown-menu #menu>
      <wiki-add-page-menu
        [initPages]="initPages()"
        [homePage]="homePage"
        [spaceId]="spaceId"
        [createPageType]="createPageType.Child"
        [addGroupHandler]="addNewGroup"
        [isHome]="true"
      ></wiki-add-page-menu>
    </thy-dropdown-menu>
  </ng-template>
</thy-header>
<thy-content>
  <thy-content-main>
    <list-filter-view
      class="ml-5"
      mode="complex"
      [listCount]="pageCount"
      [viewTemplateRef]="extraOperation"
      (keywordsChange)="onSearch($event)"
    ></list-filter-view>
    <styx-table
      #table
      styxMode="lite"
      styxCachePrefix="wiki-space-setting-directory-table-column"
      [styxLoadingDone]="loadingDone"
      [styxData]="tablePages()"
      [styxShowExpand]="showExpand"
      [styxExpandResolve]="loadChildren"
      [styxHeaderOperationTemplate]="tableHeaderOperation"
      [styxRowClassName]="rowClassName"
      [styxFreezes]="freezeIds"
      [styxSelectable]="true"
      [styxShowSizeChanger]="false"
      [styxShowHeaderSetting]="false"
      [styxShowColumnAction]="false"
      [styxSortable]="false"
      [styxDraggable]="true"
      [styxVirtualScroll]="true"
      [styxRowHeight]="52"
      [styxBeforeDragStart]="beforeDragStart"
      [styxDropPredicate]="dropPredicate"
      (styxDragDropped)="dragDropped($event)"
      (styxSelectChange)="selectChange($event)"
      (styxSelectAllChange)="selectAllChange($event)"
      (styxClick)="rowClick($event)"
    >
      <styx-table-column
        styxProperty
        styxKey="name"
        styxI18nTracking
        [styxName]="'common.name' | translate"
        styxWidth="50%"
        styxFixed="left"
        [styxExpandable]="true"
      >
        <ng-template #cell let-row="row">
          <div class="page-title text-truncate" [ngClass]="{ 'page-title-edit': true }">
            <styx-awesome-text
              class="ml-n1"
              styxAutofocus="true"
              [styxText]="row?.name"
              [styxCode]="row?.emoji_icon"
              [styxIcon]="row | pageIcon"
              [styxIconColor]="row | pageIconColor"
              [styxPlaceholder]="placeholder()"
              [styxEdit]="row._id === editStateId()"
              [styxActive]="row._id === editStateId()"
              (styxBlur)="onRename($event, row)"
              (dblclick)="onEditName(row)"
              [styxMaxLength]="titleMaxLength"
            ></styx-awesome-text>
          </div>
        </ng-template>
        <ng-template #actions let-row="row">
          @if (row?.system !== 1 && row?.permissions | hasAnyPermission: ['page_create']) {
            <a
              href="javascript:;"
              thyAction
              thyActionIcon="plus"
              styxI18nTracking
              [thyTooltip]="'common.new' | translate"
              (click)="openCreatePageMenu($event, row)"
            ></a>
          }
          <a
            href="javascript:;"
            thyAction
            thyActionIcon="more-vertical"
            styxI18nTracking
            [thyTooltip]="'common.more' | translate"
            [class.active]="active"
            (click)="openMenu($event, row, moreMenu)"
          ></a>
        </ng-template>
      </styx-table-column>

      <styx-table-column styxProperty styxKey="created_by" styxI18nTracking [styxName]="'styx.createdBy' | translate" styxWidth="25%">
        <ng-template #cell let-row="row">
          <thy-avatar
            class="mr-1"
            thySize="xs"
            thyShowName="true"
            [thySrc]="(row.created_by | user)?.avatar"
            [thyName]="(row.created_by | user)?.display_name"
          ></thy-avatar>
        </ng-template>
      </styx-table-column>

      <styx-table-column styxProperty styxKey="created_at" styxI18nTracking [styxName]="'styx.createdAt' | translate" styxWidth="25%">
        <ng-template #cell let-row="row">
          <span>{{ row?.created_at | dateTimeAutoFormat }}</span>
        </ng-template>
      </styx-table-column>

      <styx-table-column styxProperty styxKey="updated_at" styxI18nTracking [styxName]="'styx.updatedAt' | translate" styxWidth="25%">
        <ng-template #cell let-row="row">
          <span>{{ row?.updated_at | dateTimeAutoFormat }}</span>
        </ng-template>
      </styx-table-column>
    </styx-table>
  </thy-content-main>
</thy-content>

<ng-template #extraOperation>
  <a
    href="javascript:;"
    thyAction
    thyActiveClass="active"
    styxI18nTracking
    [thyTooltip]="'wiki.space.settings.directory.levelTooltip' | translate"
    thyPanelClass="wiki-page-level-menu-panel"
    [thyDropdown]="levelMenu"
    (thyActiveChange)="pageLevelActiveChange($event)"
  >
    {{ 'wiki.space.settings.directory.level' | translate }}
    <thy-icon [thyIconName]="pageLevelActive ? 'angle-up' : 'angle-down'" class="font-size-sm ml-2"></thy-icon>
  </a>
  <thy-dropdown-menu #levelMenu>
    <a thyDropdownMenuItem href="javascript:;" [ngClass]="{ 'page-level-menu-active': !currentPageLevel }" (click)="expandAllLevel()">
      <span thyDropdownMenuItemName translate="wiki.space.settings.directory.showAll"></span>
      @if (!currentPageLevel) {
        <thy-icon thyDropdownMenuItemIcon thyIconName="check"></thy-icon>
      }
    </a>
    @for (level of [].constructor(maxPageLevel); track $index; let i = $index) {
      <a
        href="javascript:;"
        thyDropdownMenuItem
        [ngClass]="{ 'page-level-menu-active': i + 1 === currentPageLevel }"
        (click)="expandLevel(i + 1)"
      >
        <span thyDropdownMenuItemName translate="wiki.space.settings.directory.showLevel" [translateParams]="{ level: i + 1 }"></span>
        @if (i + 1 === currentPageLevel) {
          <thy-icon thyDropdownMenuItemIcon thyIconName="check"></thy-icon>
        }
      </a>
    }
  </thy-dropdown-menu>
</ng-template>

<ng-template #tableHeaderOperation let-selections="selections">
  <a
    href="javascript:;"
    thyAction
    thyTheme="lite"
    thyActionIcon="copy"
    [thyDisabled]="!(selections | everyPermission: 'page_copy' : initPages())"
    (click)="onCopyPage(selections)"
    styxI18nTracking
    >{{ 'common.copy' | translate }}</a
  >
  <a
    href="javascript:;"
    thyAction
    thyTheme="lite"
    thyActionIcon="move"
    [thyDisabled]="!(selections | everyPermission: 'page_move' : initPages())"
    (click)="onMovePage(selections)"
    >{{ 'common.move' | translate }}</a
  >
  <a
    href="javascript:;"
    thyAction
    thyTheme="lite"
    thyType="danger"
    thyActionIcon="trash"
    [thyDisabled]="!(selections | everyPermission: 'page_delete' : initPages())"
    (click)="onDeletePage(selections)"
    >{{ 'common.delete' | translate }}
  </a>
</ng-template>

<ng-template #moreMenu let-data>
  <div class="thy-dropdown-menu">
    <a
      thyDropdownMenuItem
      href="javascript:;"
      [thyDisabled]="!(data.page?.permissions | hasPermission: 'page_edit')"
      (click)="onEditName(data.page)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="rename"></thy-icon>
      <span thyDropdownMenuItemName translate="common.rename"></span>
    </a>
    @if (data.page | isPage) {
      <a thyDropdownMenuItem href="javascript:;" (click)="onOpenDetail(data.page)">
        <thy-icon thyDropdownMenuItemIcon thyIconName="preview"></thy-icon>
        <span thyDropdownMenuItemName translate="wiki.space.settings.directory.viewPage"></span>
      </a>
      <a thyDropdownMenuItem href="javascript:;" (click)="onWindowOpen(data.page)">
        <thy-icon thyDropdownMenuItemIcon thyIconName="publish"></thy-icon>
        <span thyDropdownMenuItemName translate="styx.openNew"></span>
      </a>
    }
    @if ((data.page?.permissions | hasPermission: 'page_edit') || (data.page | isPage)) {
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
    }
    <a
      thyDropdownMenuItem
      href="javascript:;"
      [thyDisabled]="!(data.page?.permissions | hasPermission: 'page_copy')"
      (click)="onCopyPage(data.page)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="copy"></thy-icon>
      <span thyDropdownMenuItemName translate="common.copy"></span>
    </a>
    <a
      thyDropdownMenuItem
      href="javascript:;"
      [thyDisabled]="!(data.page?.permissions | hasPermission: 'page_move')"
      (click)="onMovePage(data.page)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="move"></thy-icon>
      <span thyDropdownMenuItemName translate="common.move"></span>
    </a>
    <a
      href="javascript:;"
      thyDropdownMenuItem
      thyType="danger"
      [thyDisabled]="data.page?.system === 1 || !(data.page?.permissions | hasPermission: 'page_delete')"
      (click)="onDeletePage(data.page)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="trash"></thy-icon>
      <span thyDropdownMenuItemName translate="common.delete"></span>
    </a>
  </div>
</ng-template>
