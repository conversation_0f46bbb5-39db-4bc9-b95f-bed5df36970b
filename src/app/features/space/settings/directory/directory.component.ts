import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    computed,
    DestroyRef,
    effect,
    inject,
    OnDestroy,
    OnInit,
    signal,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { hasPermissionTransform, injectBizValidatorMaxLengths, PilotEntryStore } from '@atinc/ngx-styx';
import { helpers } from '@atinc/ngx-styx/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import {
    StyxTableBeforeDragStartEvent,
    StyxTableComponent,
    StyxTableDragDroppedEvent,
    StyxTableDropPosition,
    StyxTableEmitEvent,
    StyxTableRow,
    StyxTableSelectAllChangeEvent,
    StyxTableSelectChangeEvent
} from '@atinc/ngx-styx/table';
import { StyxAwesomeTextInputEvent } from '@atinc/ngx-styx/text';
import { WikiCreatePageMenuComponent } from '@wiki/app/components';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { CreatePageType, DRAFT_GROUP, PageSystems, PageTreeQueryScene } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PageService } from '@wiki/app/services/util/page.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { filterTreeData } from '@wiki/app/util/tree';
import { wikiPermissionDefinition, wikiPermissionPoints } from '@wiki/common/constants/permission';
import { IsPagePipe } from '@wiki/common/pipe/page.pipe';
import { CommonPageApiService } from '@wiki/common/services';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { finalize, of } from 'rxjs';

@Component({
    selector: 'wiki-space-directory',
    templateUrl: './directory.component.html',
    host: {
        class: 'wiki-space-directory d-flex-column-fill overflow-hidden'
    },
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiSettingDirectoryComponent implements OnInit, OnDestroy {
    private translate = inject(StyxTranslateService);

    loadingDone = false;

    pageCount = 0;

    initPages = computed(() => {
        return this.pagesStore.state().entities;
    });

    tablePages = signal<PageInfo[]>([]);

    searchText = '';

    pageLevelActive = false;

    maxPageLevel = 0;

    currentPageLevel: number = 2;

    createPageType = CreatePageType;

    menuPopover: ThyPopoverRef<any>;

    saving = false;

    editStateId = signal('');

    placeholder = signal('');

    freezeIds: string[] = [];

    titleMaxLength = injectBizValidatorMaxLengths().longTitle;

    get spaceId() {
        return this.pilotStore.snapshot.detail._id;
    }

    get space() {
        return this.pilotStore.snapshot.detail;
    }

    get homePage() {
        return this.pagesStore.snapshot.entities?.find(x => x?.system === PageSystems.home);
    }

    get isMenuPopoverOpen() {
        return this.menuPopover && this.menuPopover.getOverlayRef() && this.menuPopover.getOverlayRef().hasAttached();
    }

    @ViewChild(StyxTableComponent) table: StyxTableComponent;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private viewContainerRef: ViewContainerRef,
        private popover: ThyPopover,
        private pageService: PageService,
        private isPagePipe: IsPagePipe,
        private cdr: ChangeDetectorRef,
        private commonPageApiService: CommonPageApiService,
        private globalPageStore: PageEventBus,
        private errorService: WikiErrorService,
        private pagesStore: PagesStore,
        private destroyRef: DestroyRef
    ) {
        effect(() => {
            const data = this.initPages();
            this.setMaxHierarchyLevel(data);
            this.buildTablePages(data);
        });
    }

    ngOnInit() {
        this.fetchPages();
    }

    ngOnDestroy() {
        this.menuClose();
    }

    fetchPages() {
        this.loadingDone = false;
        this.pagesStore
            .fetchPages(this.spaceId, { scene: PageTreeQueryScene.table })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (pages: PageInfo[]) => {
                    if (!!pages.length) {
                        this.expandLevel(this.currentPageLevel);
                    }
                    this.loadingDone = true;
                }
            });
    }

    rowClick(event: StyxTableEmitEvent) {
        const pageInfo: PageInfo = event.row;
        if (pageInfo.children?.length > 0) {
            this.table?.expandChildren(event.event, event.row);
        }
    }

    showExpand(row: StyxTableRow) {
        return row.children && row.children.length > 0;
    }

    loadChildren(row: StyxTableRow) {
        return of(row.children);
    }

    rowClassName = (row: StyxTableRow) => {
        return this.editStateId() === row?._id ? 'table-column-page-title-edit' : '';
    };

    /**
     * 构建并筛选指定数据为树结构，同时计算当前所以层级页面数量
     * @param pages
     */
    buildTablePages(pages: PageInfo[]) {
        const { tree, flatTree } = filterTreeData<PageInfo>(pages, this.searchText);
        this.tablePages.set(tree);
        this.pageCount = flatTree.filter(page => this.isPagePipe.transform({ type: page.type })).length;
        this.menuClose();
    }

    onSearch(keywords: string) {
        this.searchText = keywords;
        this.buildTablePages(this.initPages());

        // 为空时基于当前层级展开，否则展开所有
        if (keywords === '') {
            this.expandLevel(this.currentPageLevel);
        } else {
            this.expandAll();
        }
    }

    /**
     * 设置最大层级与默认层级
     */
    setMaxHierarchyLevel(pages: PageInfo[]) {
        let maxLevel = 1;
        pages.forEach(item => {
            if (Array.isArray(item.parent_ids)) {
                // 计算当前元素的层级
                const level = item.parent_ids.length + 1;

                // 更新最大层级
                if (level > maxLevel) {
                    maxLevel = level;
                }
            }
        });
        this.currentPageLevel = maxLevel < 2 ? 1 : 2;
        this.maxPageLevel = maxLevel;
    }

    /**
     * 获取指定层级的节点集合
     */
    getNodesWithLevel(nodes: PageInfo[], level: number): { _id: string }[] {
        const result = [];
        const processNode = (node: PageInfo, currentLevel: number) => {
            if (currentLevel <= level) {
                result.push({ _id: node._id, children: node.children });
            }
            if (node.children && node.children.length > 0) {
                node.children.forEach(child => processNode(child, currentLevel + 1));
            }
        };
        nodes.forEach(node => processNode(node, 1));
        return result;
    }

    /**
     * 展示层级按钮选中状态
     */
    pageLevelActiveChange(state: boolean) {
        this.pageLevelActive = state;
    }

    expandAll() {
        if (!this.table?.allExpanded) {
            this.table?.expandAllRows();
            this.table?.markForCheck();
        }
    }

    expandPage(page: PageInfo) {
        const expand = this.table?.tableStateService.isExpanded(page);
        if (!expand) {
            this.table?.tableStateService.expandChildren(page);
            this.table?.markForCheck();
        }
    }

    clearSelect() {
        this.table?.tableSelectionService.clear();
        this.freezeIds = [];
    }

    /**
     * 展开所有层级
     */
    expandAllLevel() {
        this.currentPageLevel = null;
        this.expandAll();
    }

    /**
     * 展开指定层级
     * @param level
     */
    expandLevel(level: number) {
        this.currentPageLevel = level;
        this.table?.collapseAllRows();
        if (level > 1) {
            const rows = this.getNodesWithLevel(this.tablePages(), level - 1);
            this.table?.tableStateService.loadAllRowsChildren();
            this.table?.tableStateService.expandChildren(...rows);
        }
        this.table?.markForCheck();
    }

    /**
     * 弹出菜单，创建页面菜单与更多菜单共用
     */
    openMenu(e: Event, page: PageInfo, menu: TemplateRef<any>) {
        this.menuClose();
        this.menuPopover = this.popover.open(menu, {
            initialState: { page },
            origin: e.currentTarget as HTMLElement,
            hasBackdrop: false,
            outsideClosable: true,
            insideClosable: true,
            manualClosure: true,
            placement: 'bottomLeft',
            viewContainerRef: this.viewContainerRef
        });
    }

    menuClose() {
        if (this.isMenuPopoverOpen) {
            this.menuPopover?.close();
        }
    }

    openCreatePageMenu(e: Event, page: PageInfo) {
        if (this.isMenuPopoverOpen) {
            this.menuPopover.close();
        }
        this.menuPopover = this.popover.open(WikiCreatePageMenuComponent, {
            initialState: {
                page,
                initPages: this.initPages,
                spaceId: this.spaceId,
                addGroupHandler: (p, draftGroup) => this.addNewGroup(p, draftGroup)
            },
            origin: e.currentTarget as HTMLElement,
            hasBackdrop: false,
            outsideClosable: true,
            insideClosable: true,
            manualClosure: true,
            placement: 'bottomLeft',
            viewContainerRef: this.viewContainerRef
        });
    }

    addNewGroup = (page: PageInfo, draftGroup: DraftInfo) => {
        // 右上角创建分组，数据直接放到尾部，不用重新 build
        if (!page) {
            this.tablePages.set([...this.tablePages(), draftGroup]);
        } else {
            const newPages = [...this.initPages(), draftGroup];
            this.buildTablePages(newPages);

            // 插入子级分组时，如果父级未展开，则展开父级
            if (page?._id === draftGroup.parent_id) {
                this.expandPage(page);
            }
        }

        this.placeholder.set(this.translate.instant<I18nSourceDefinitionType>('styx.inputSectionName'));
        this.editStateId.set(draftGroup._id);
    };

    onNewPage(data: StyxAwesomeTextInputEvent, page: PageInfo) {
        // 数据不为空时创建分组，为空则清除临时数据
        if (data.value && !!data.value.trim()) {
            page.emoji_icon = data.emoji;
            page.name = data.value;
            this.pagesStore.pureAddGroup(this.spaceId, page);
        } else {
            this.buildTablePages(this.initPages());
        }
    }

    onCopyPage(pageOrIds: PageInfo | string[]) {
        const isArray = helpers.isArray(pageOrIds);
        let selectPageOrIds;
        if (isArray) {
            selectPageOrIds = pageOrIds.map(id => this.initPages().find(page => page._id === id));
        } else {
            selectPageOrIds = pageOrIds;
        }

        this.pageService.openCopyPageDialog({
            isMultiple: isArray,
            selectPageOrIds,
            viewContainerRef: this.viewContainerRef,
            copySuccess: () => {
                // 批量复制完成时，清空选择
                if (isArray) {
                    this.clearSelect();
                }
            }
        });
    }

    onMovePage(pageOrIds: PageInfo | string[]) {
        const isArray = helpers.isArray(pageOrIds);
        let selectPageOrIds;
        if (isArray) {
            selectPageOrIds = pageOrIds.map(id => this.initPages().find(page => page._id === id));
        } else {
            selectPageOrIds = pageOrIds;
        }

        this.pageService.openMovePageDialog({
            isMultiple: isArray,
            selectPageOrIds,
            viewContainerRef: this.viewContainerRef,
            moveSuccess: () => {
                // 批量移动完成时，清空选择
                if (isArray) {
                    this.clearSelect();
                }
            }
        });
    }

    onDeletePage(pageOrIds: PageInfo | string[]) {
        const isArray = helpers.isArray(pageOrIds);
        let target;
        if (isArray && pageOrIds.length > 1) {
            target = pageOrIds.map(id => this.initPages().find(page => page._id === id)).map(p => p._id);
        } else if (isArray && pageOrIds.length === 1) {
            target = this.initPages().find(page => page._id === pageOrIds[0]);
        } else {
            target = pageOrIds;
        }
        this.pageService.deletePages(this.spaceId, target);
    }

    /**
     * 进入标题编辑状态
     * @param page
     */
    onEditName(page: PageInfo) {
        if (hasPermissionTransform(page?.permissions, wikiPermissionPoints, wikiPermissionDefinition.page_edit)) {
            this.placeholder.set(this.translate.instant('wiki.page.name.placeholder'));
            this.editStateId.set(page._id);
        }
    }

    /**
     * 标题失焦时重命名或确认创建分组
     */
    onRename(data: StyxAwesomeTextInputEvent, page: PageInfo) {
        const isDraftGroup = page._id === DRAFT_GROUP;
        // 处理初始创建分组
        if (isDraftGroup) {
            this.onNewPage(data, page);
        } else {
            if (!this.saving && (data.emoji !== page?.emoji_icon || data.value !== page.name)) {
                page.emoji_icon = data.emoji;
                page.name = !!data.value.trim() ? data.value : page.name;
                this.updatePage(page);
            }
        }
        this.editStateId.set('');
        this.placeholder.set('');
    }

    updatePage(page: PageInfo) {
        const name = page.name.trim();
        if (!name) {
            return;
        }
        this.saving = true;
        this.commonPageApiService
            .updatePage(page._id, { name: page.name, emoji_icon: page.emoji_icon })
            .pipe(
                finalize(() => {
                    this.saving = false;
                    this.cdr.markForCheck();
                })
            )
            .subscribe({
                next: () => {
                    this.globalPageStore.emitPageInfo({
                        _id: page._id,
                        space_id: page.space_id,
                        name,
                        emoji_icon: page.emoji_icon
                    });
                },
                error: error => {
                    this.errorService.defaultErrorHandler(error);
                }
            });
    }

    onOpenDetail(page: PageInfo) {
        if (this.isPagePipe.transform({ type: page.type })) {
            const pageId = page._id;
            this.pageService.openWikiPageDialog({
                pageId,
                viewContainerRef: this.viewContainerRef
            });
        }
    }

    onWindowOpen(page: PageInfo) {
        const identifier = this.pilotStore.snapshot.detail.identifier;
        const id = page?.short_id ?? page?._id;
        this.pageService.newWindowOpenPage(identifier, id);
    }

    /**
     * 切换选择父级时，子级也切换选择，选中时，冻结子级，取消选中时，解冻子级
     * @param event
     */
    selectChange(event: StyxTableSelectChangeEvent) {
        if (event.selected) {
            this.freezeAndSelect(event.row, false);
        } else {
            this.unSelectSelection(event.row);
            this.unfreeze(event.row);
        }
    }

    selectAllChange(event: StyxTableSelectAllChangeEvent) {
        // 取消全选时，清除冻结页面
        if (event.operation === 'deselect') {
            this.freezeIds = [];
        }
    }

    /**
     * 解冻页面和所有子级
     * @param page
     */
    unfreeze(page: PageInfo) {
        let childrenIds = [page._id];
        childrenIds.push(...(helpers.flatTree(page.children) ?? []).map(node => node._id));
        this.freezeIds = this.freezeIds.filter(freezeId => !childrenIds.includes(freezeId));
    }

    /**
     * 取消选中页面和所有子级
     * @param page
     */
    unSelectSelection(page: PageInfo) {
        const children = helpers.flatTree(page.children) ?? [];
        this.table?.tableSelectionService.deselect([page, ...children]);
    }

    /**
     * 冻结并选中页面及所有子级
     * @param page
     */
    freezeAndSelect(page: PageInfo, isFreezeSelf = true) {
        const children = helpers.flatTree(page.children) ?? [];
        this.table?.tableSelectionService.select([page, ...children]);

        let childrenIds = isFreezeSelf ? [page._id] : [];
        childrenIds.push(...children.map(node => node._id));
        childrenIds.forEach(id => {
            if (!this.freezeIds.includes(id)) {
                this.freezeIds.push(id);
            }
        });
    }

    changeSelectState(from: PageInfo, to: PageInfo) {
        // 父级时勾选，则勾选并冻结当前元素及所有子级
        const isParentSelect = this.table?.tableSelectionService.isSelected(to);
        if (isParentSelect) {
            this.freezeAndSelect(from);
        } else {
            // 父级未勾选，那么目标元素及所有子级解冻
            this.unfreeze(from);
        }
    }

    beforeDragStart = (e: StyxTableBeforeDragStartEvent<PageInfo>) => {
        const pageId = e.item._id;
        return (
            hasPermissionTransform(e.item.permissions, wikiPermissionPoints, 'page_move') &&
            !e.item.is_lock &&
            pageId !== this.homePage?._id &&
            pageId !== this.editStateId()
        );
    };

    dropPredicate = (e: StyxTableDragDroppedEvent<PageInfo>) => {
        const toPage = e.to;
        if (toPage && !hasPermissionTransform(toPage.permissions, wikiPermissionPoints, 'page_create')) {
            return false;
        }
        return e.to._id !== this.homePage?._id;
    };

    dragDropped(e: StyxTableDragDroppedEvent<PageInfo>) {
        // 拖拽到子级
        if (e.position === StyxTableDropPosition.in) {
            this.dragPage(this.spaceId, e.from._id, e.to._id, '', () => {
                this.expandPage(e.to);
                this.changeSelectState(e.from, e.to);
            });
        } else {
            // 拖拽到同级
            if (e.to.parent_id === e.from.parent_id) {
                let afterPage;
                if (e.toIndex !== 0) {
                    afterPage = e.container.find((_, index) => index === e.toIndex - 1);
                }
                this.dragPage(this.spaceId, e.from._id, e.from.parent_id, afterPage?._id);
            } else {
                // 拖拽元素与目标元素不是同一父级，那么设置为目标的父级
                this.dragPage(this.spaceId, e.from._id, e.to.parent_id, e.to._id, () => {
                    const parentPage = this.initPages().find(page => page._id === e.to.parent_id);
                    this.changeSelectState(e.from, parentPage);
                });
            }
        }
    }

    dragPage(spaceId: string, pageId: string, parentId: string, afterId: string, callback?: () => void) {
        return this.pagesStore
            .dragPage(spaceId, pageId, {
                parentId,
                afterId
            })
            .subscribe({
                next: () => {
                    callback && callback();
                },
                error: error => this.errorService.defaultErrorHandler(error)
            });
    }
}
