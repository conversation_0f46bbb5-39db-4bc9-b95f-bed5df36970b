import { After<PERSON>iewInit, Component, HostBinding, OnInit, ViewContainerRef, inject } from '@angular/core';
import {
    css,
    UtilService,
    GlobalUsersStore,
    PaginationResponseData,
    PaginationResult,
    SortConfigInfo,
    helpers,
    Is,
    StyxTableSortEvent,
    Direction,
    hasAnyPermissionTransform,
    PilotScopeType,
    StyxTranslateService,
    getCachedPageSize,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { PagePermissionScopeType } from '@wiki/app/constants/page-sharing';
import { ListFilterLabel } from '@wiki/app/components/list-filter-view/types';
import { PagePermissionApiService } from '@wiki/app/services/page-permission-setting-api';
import { PagePermissionInfo, PagePermissionReference } from '@wiki/app/entities/page-permission.info';
import { SharePageStore } from '@wiki/app/stores/share-page.store';
import { PageService } from '@wiki/app/services/util/page.service';
import { PagePermissionParams } from '@wiki/app/entities/page-permission-setting';
import { SharePageParams } from '@wiki/app/entities/sharing.info';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

enum LabelType {
    spaceInside = 'space-inside',
    spaceOutside = 'space-outside',
    publicShared = 'share-publicly'
}

@Component({
    selector: 'wiki-space-setting-page-shared',
    templateUrl: './page-shared.component.html',
    providers: [SharePageStore],
    standalone: false
})
export class SpaceSettingsPageSharedComponent implements OnInit, AfterViewInit {
    @HostBinding('class') className = `${css.layout}`;

    loadingDone = false;

    labels: ListFilterLabel<LabelType>[] = [];

    translate = inject(StyxTranslateService);

    private readonly labelMap = {
        spaceInsideLabel: { name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.member'), value: LabelType.spaceInside },
        spaceOutsideLabel: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.settings.pageShared.nonSpaceMember'),
            value: LabelType.spaceOutside
        },
        publiclyShared: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.settings.pageShared.publicShared'),
            value: LabelType.publicShared
        }
    };

    showProperties = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.name'),
            key: 'name',
            type: 1,
            fixed: 'left',
            sortable: true,
            width: '50%'
        },
        { name: this.translate.instant<I18nSourceDefinitionType>('common.operator'), key: 'updated_by', type: 9, width: '25%' },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.operateAt'),
            key: 'updated_at',
            type: 4,
            sortable: true,
            width: '25%'
        }
    ];

    sortConfig: SortConfigInfo = {
        sortBy: 'updated_at',
        sortDirection: Direction.default
    };

    pagination: PaginationResult = {};

    pageIndex = 0;

    pageSize = getCachedPageSize();

    label: LabelType;

    keywords: string = '';

    pagesData: { _id: string; name: string; updated_at: number; updated_by: string; permissions: string }[] = [];

    get spaceId() {
        return this.pilotStore.snapshot.detail._id;
    }

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    private pilotStore = inject(PilotEntryStore);

    initLabels() {
        if (hasAnyPermissionTransform(this.currentSpace.permissions, wikiPermissionPoints, ['space_page_permission_setting'])) {
            if (this.currentSpace.scope_type !== PilotScopeType.personal) {
                this.labels = this.labels.concat(this.labelMap.spaceInsideLabel);
            }
            this.labels = this.labels.concat(this.labelMap.spaceOutsideLabel);
        }
        if (hasAnyPermissionTransform(this.currentSpace.permissions, wikiPermissionPoints, ['page_share_publicly'])) {
            this.labels = this.labels.concat(this.labelMap.publiclyShared);
        }
    }

    constructor(
        private util: UtilService,
        private pagePermissionApiService: PagePermissionApiService,
        private usersStore: GlobalUsersStore,
        private sharePageStore: SharePageStore,
        private pageService: PageService,
        private viewContainerRef: ViewContainerRef
    ) {}

    ngOnInit() {
        this.initLabels();
    }

    ngAfterViewInit(): void {
        this.fetchPagesData();
    }

    fetchPagesData(allowLoading: boolean = true) {
        if ([LabelType.spaceInside, LabelType.spaceOutside].includes(this.label)) {
            this.fetchPermissions();
        }
        if (LabelType.publicShared === this.label) {
            this.fetchPublicShared(allowLoading);
        }
    }

    private fetchPublicShared(allowLoading: boolean) {
        if (allowLoading) {
            this.loadingDone = false;
        }
        const params: SharePageParams = {
            keyWords: this.keywords,
            pageIndex: this.pageIndex,
            pageSize: this.pageSize,
            sortBy: this.sortConfig.sortBy,
            sortDirection: this.sortConfig.sortDirection
        };
        this.sharePageStore.fetchSharePages(params).subscribe(() => {
            this.loadingDone = true;

            const pageMap = helpers.keyBy(this.sharePageStore.snapshot.references.principals, x => x._id);
            this.pagesData = this.sharePageStore.snapshot.entities.map(x => {
                return {
                    _id: x.principal_id,
                    name: x.name,
                    emoji_icon: x['emoji_icon'],
                    updated_by: x.updated_by,
                    updated_at: x.updated_at,
                    permissions: x.permissions,
                    refs: {
                        page: pageMap[x.principal_id]
                    }
                };
            });
            this.pagination = this.sharePageStore.snapshot.pagination;
        });
    }

    private fetchPermissions() {
        this.loadingDone = false;
        this.pagePermissionApiService
            .fetchPagePermissions(
                this.spaceId,
                this.label as unknown as PagePermissionScopeType,
                this.keywords,
                this.pageIndex,
                this.pageSize,
                this.sortConfig.sortBy,
                this.sortConfig.sortDirection
            )
            .subscribe((data: PaginationResponseData<PagePermissionInfo[], PagePermissionReference>) => {
                this.loadingDone = true;
                const pageMap = helpers.keyBy(data.references.pages, x => x._id);
                this.pagesData = data.value.map(x => {
                    return {
                        _id: x.page_id,
                        name: x['page_name'],
                        emoji_icon: x['emoji_icon'],
                        updated_by: x.updated_by,
                        updated_at: x.updated_at,
                        permissions: pageMap[x.page_id]?.permissions,
                        refs: {
                            page: pageMap[x.page_id]
                        }
                    };
                });
                this.usersStore.initializeUsers(data.references.members);
                this.pagination = {
                    count: data.count,
                    pageCount: data.page_count,
                    pageIndex: data.page_index,
                    pageSize: data.page_size
                };
            });
    }

    pageIndexChange(pi: number) {
        this.pageIndex = pi;
        this.fetchPagesData();
    }

    pageSizeChange(ps: number) {
        this.pageSize = ps;
        this.fetchPagesData();
    }

    labelChange(label: ListFilterLabel<LabelType>) {
        if (!this.label) {
            this.label = label.value;
        } else {
            this.label = label.value;
            this.fetchPagesData();
        }
    }

    keywordsChange(keywords: string) {
        this.keywords = keywords;
        this.fetchPagesData();
    }

    sortChange({ sortBy, sortDirection }: StyxTableSortEvent) {
        this.sortConfig = {
            sortBy,
            sortDirection: sortDirection
        };
        this.fetchPagesData();
    }

    openPageSharing(row: { _id: string; refs: { page: { short_id: string } } }) {
        this.pageService.openPageSharing({
            isShared: Is.yes,
            spaceId: this.spaceId,
            pageId: row._id,
            shortId: row.refs.page.short_id,
            isConfiguration: false,
            viewContainerRef: this.viewContainerRef,
            selectType: this.label as unknown as PagePermissionScopeType,
            spaceScopeType: this.pilotStore.snapshot.detail.scope_type,
            confirmCallback: (data: PagePermissionParams) => {
                this.fetchPagesData();
            },
            sharedCallback: (isShared: Is) => {
                if (!isShared) {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.closeSuccess'));
                } else {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.enableSuccess'));
                }
                this.fetchPagesData(false);
            }
        });
    }

    openPageDetail(row: PagePermissionInfo) {
        const pageId = row.page_id ? row.page_id : row._id;
        this.pageService.openWikiPageDialog({
            pageId,
            hasPageTreeSidebar: true,
            viewContainerRef: this.viewContainerRef
        });
    }
}
