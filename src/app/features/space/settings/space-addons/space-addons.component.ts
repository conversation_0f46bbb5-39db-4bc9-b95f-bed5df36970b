import { Component, inject } from '@angular/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { PilotEntryStore, StyxTranslateService } from '@atinc/ngx-styx';
import { ResponseData, hasPermissionTransform, helpers } from '@atinc/ngx-styx';
import { StyxAddonColumnsComponent, StyxAddonInfo, StyxAddonPermissionComponent } from '@atinc/ngx-styx/addon';
import { API_PREFIX } from '@wiki/app/constants';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';

@Component({
    selector: 'wiki-space-addons',
    templateUrl: './space-addons.component.html',
    standalone: false
})
export class WikiSpaceAddonsComponent {
    private translate = inject(StyxTranslateService);

    private pilotStore = inject(PilotEntryStore);

    public get apiPrefix() {
        return `${API_PREFIX}/spaces/${this.pilotStore.snapshot?.detail?._id}`;
    }

    addonSettingsConfigs = {
        columns: {
            title: this.translate.instant<I18nSourceDefinitionType>('styx.viewManagement'),
            component: StyxAddonColumnsComponent,
            lockMax: 3,
            unremovableColumns: ['name']
        },
        permission: {
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.configuration.space.componentPermission'),
            component: StyxAddonPermissionComponent
        }
    };

    addonDefinitions = {
        pages: {
            key: 'pages',
            settings: ['permission']
        },
        baseline: {
            key: 'baseline',
            settings: ['columns', 'permission']
        },
        reviews: {
            key: 'reviews',
            settings: ['permission']
        }
    };

    get hasPermission() {
        return hasPermissionTransform(this.pilotStore.snapshot.detail?.permissions, wikiPermissionPoints, 'space_addon_setting');
    }

    addonsChange(addons: StyxAddonInfo[]) {
        const identifier = this.pilotStore.snapshot.detail.identifier;
        this.pilotStore.fetchEntry(identifier).subscribe({
            next: (data: ResponseData) => {
                if (data && data.value) {
                    this.pilotStore.updateAddons(data.value.addons);
                }
            },
            error: () => {
                if (addons.length) {
                    const enabledAddons: StyxAddonInfo[] = helpers
                        .filter(addons, (item: StyxAddonInfo) => {
                            return item.is_enabled;
                        })
                        .map(item => {
                            return { ...item, key: item.addon_type };
                        });
                    this.pilotStore.updateAddons(enabledAddons);
                }
            }
        });
    }
}
