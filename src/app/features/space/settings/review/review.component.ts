import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { API_PREFIX } from '@wiki/app/constants';
import { PilotEntryStore, StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-review',
    templateUrl: './review.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SpaceSettingsReviewComponent {
    private pilotStore = inject(PilotEntryStore);

    private translate = inject(StyxTranslateService);

    public tips = this.translate.instant<I18nSourceDefinitionType>('wiki.configuration.review.configHint');

    public get apiPrefix() {
        return `${API_PREFIX}/admin/spaces/${this.pilotStore.snapshot?.detail?._id}/review`;
    }
}
