import { Component, HostBinding, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    ApplicationContext,
    ApplicationType,
    GlobalUsersStore,
    PilotEntryStore,
    StyxHeadService,
    StyxTranslateService,
    css,
    hasAnyPermissionTransform
} from '@atinc/ngx-styx';
import { getWikiTitles } from '@wiki/app/constants';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';

interface RouteItem {
    name: string;
    path?: string;
    point?: string;
    permissions?: string[];
    config?: () => boolean;
}

type RouteListType = {
    [key: string]: RouteItem;
};

@Component({
    selector: 'wiki-space-settings',
    templateUrl: './settings.component.html',
    styleUrls: ['./settings.component.scss'],
    standalone: false
})
export class SpaceSettingsComponent implements OnInit {
    private translate = inject(StyxTranslateService);
    @HostBinding(`class`)
    className = `${css.layout} ${css.displayFlexColumnFill}`;

    public loadingDone = true;

    groupList = {
        share: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.shared'),
            permissions: ['space_shared_setting', 'space_page_permission_setting', 'page_share_publicly']
        },
        space: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.space', { isTitle: true, isPlural: false }),
            permissions: ['space_addon_setting', 'space_directory_setting', 'space_review_setting']
        },
        setting: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.configuration'),
            permissions: ['space_basic_setting', 'space_move', 'space_archive_setting', 'space_delete_setting']
        }
    };

    routeList: RouteListType = {
        members: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.member'),
            path: 'members',
            point: 'space_member_setting'
        },
        flow: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.flowRule'),
            path: 'flow',
            config: () => {
                return this.manageAutomateRulePermission;
            }
        },
        stencils: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.stencils'),
            path: 'stencils',
            permissions: ['space_stencil_setting']
        },
        tags: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.tagManage'),
            path: 'tags',
            permissions: ['tag_manage']
        },
        trash: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.recycleBin'),
            path: 'trash',
            permissions: ['space_recycle_bin_setting']
        },
        pageShared: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.page.shared.title'),
            path: 'page-shared',
            permissions: ['space_page_permission_setting', 'page_share_publicly']
        },
        spaceShared: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.sharing'),
            path: 'space-shared',
            permissions: ['space_shared_setting']
        },
        component: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.component'),
            path: 'component',
            permissions: ['space_addon_setting']
        },
        review: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.reviewConfig'),
            path: 'review',
            permissions: ['space_review_setting']
        },
        directory: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.directory'),
            path: 'directory',
            permissions: ['space_directory_setting']
        },
        statistics: {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.space.statistics'),
            path: 'statistics',
            config: () => {
                return true;
            }
        },
        basic: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.baseInfo'),
            path: 'basic',
            permissions: ['space_basic_setting']
        },
        advance: {
            name: this.translate.instant<I18nSourceDefinitionType>('styx.advancedSettings'),
            path: 'advance',
            permissions: ['space_move', 'space_archive_setting', 'space_delete_setting']
        }
    };

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    get manageAutomateRulePermission() {
        return this.applicationContext.isAvailableApp(ApplicationType.flow);
    }

    public pilotStore = inject(PilotEntryStore);

    constructor(
        public userStore: GlobalUsersStore,
        private headService: StyxHeadService,
        private applicationContext: ApplicationContext,
        private router: Router,
        private route: ActivatedRoute
    ) {}

    ngOnInit() {
        const { default: defaultTitle } = getWikiTitles(this.translate);
        this.headService.setTitle(defaultTitle);

        const activatedChildrenPath = (this.route.children[0] && this.route.children[0].routeConfig.path) || '';
        const currentRoute = this.routeList[activatedChildrenPath];
        const hasPermission =
            currentRoute?.permissions &&
            hasAnyPermissionTransform(this.currentSpace?.permissions, wikiPermissionPoints, currentRoute?.permissions);
        const hasEnable = currentRoute?.config && currentRoute.config();
        const path = currentRoute && (hasPermission || hasEnable) ? currentRoute?.path : 'members';

        if (activatedChildrenPath !== path) {
            this.router.navigate(['./', path], {
                relativeTo: this.route,
                replaceUrl: true
            });
        }
    }
}
