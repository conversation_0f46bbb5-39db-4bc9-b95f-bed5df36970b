@if (loadingDone) {
  <thy-layout>
    <thy-sidebar thyTheme="light">
      <wiki-return-to-space></wiki-return-to-space>
      <thy-sidebar-content>
        <thy-menu thyTheme="loose">
          <thy-divider></thy-divider>
          <thy-menu-group styxI18nTracking [thyTitle]="'styx.general' | translate" [thyCollapsible]="false">
            @if (currentSpace | spaceCheckpoints: routeList.members.point) {
              <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.members.path]" routerLinkActive="active">
                <span thyMenuItemName> {{ routeList.members.name }} </span>
              </a>
            }
            @if (currentSpace?.permissions | hasAnyPermission: routeList.stencils.permissions) {
              <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.stencils.path]" routerLinkActive="active">
                <span thyMenuItemName> {{ routeList.stencils.name }} </span>
              </a>
            }
            @if (currentSpace?.permissions | hasAnyPermission: routeList.tags.permissions) {
              <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.tags.path]" routerLinkActive="active">
                <span thyMenuItemName> {{ routeList.tags.name }} </span>
              </a>
            }
            @if (manageAutomateRulePermission) {
              <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.flow.path]" routerLinkActive="active">
                <span thyMenuItemName> {{ routeList.flow.name }} </span>
              </a>
            }
            @if (currentSpace?.permissions | hasAnyPermission: routeList.trash.permissions) {
              <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.trash.path]" routerLinkActive="active">
                <span thyMenuItemName> {{ routeList.trash.name }} </span>
              </a>
            }
          </thy-menu-group>
          @if (currentSpace?.permissions | hasAnyPermission: groupList.share.permissions) {
            <thy-divider></thy-divider>
            <thy-menu-group [thyTitle]="groupList.share.name" [thyCollapsible]="false">
              @if (currentSpace?.permissions | hasAnyPermission: routeList.pageShared.permissions) {
                <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.pageShared.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.pageShared.name }} </span>
                </a>
              }
              @if (pilotStore.snapshot.pilot?.permissions | hasAnyPermission: routeList.spaceShared.permissions) {
                <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.spaceShared.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.spaceShared.name }} </span>
                </a>
              }
            </thy-menu-group>
          }
          <thy-divider></thy-divider>
          <thy-menu-group [thyTitle]="groupList.space.name" [thyCollapsible]="false">
            @if (currentSpace?.permissions | hasAnyPermission: groupList.space.permissions) {
              @if (currentSpace?.permissions | hasAnyPermission: routeList.component.permissions) {
                <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.component.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.component.name }} </span>
                </a>
              }
              @if (currentSpace?.permissions | hasAnyPermission: routeList.review.permissions) {
                <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.review.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.review.name }} </span>
                </a>
              }
              @if (currentSpace?.permissions | hasAnyPermission: routeList.directory.permissions) {
                <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.directory.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.directory.name }} </span>
                </a>
              }
            }
            <a href="javascript:;" thyMenuItem [routerLink]="['./', routeList.statistics.path]" routerLinkActive="active">
              <span thyMenuItemName> {{ routeList.statistics.name }} </span>
            </a>
          </thy-menu-group>
          @if (currentSpace?.permissions | hasAnyPermission: groupList.setting.permissions) {
            <thy-divider></thy-divider>
            <thy-menu-group [thyTitle]="groupList.setting.name" [thyCollapsible]="false">
              @if (currentSpace?.permissions | hasAnyPermission: routeList.basic.permissions) {
                <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.basic.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.basic.name }} </span>
                </a>
              }
              @if (currentSpace?.permissions | hasAnyPermission: routeList.advance.permissions) {
                <a thyMenuItem href="javascript:;" [routerLink]="['./', routeList.advance.path]" routerLinkActive="active">
                  <span thyMenuItemName> {{ routeList.advance.name }} </span>
                </a>
              }
            </thy-menu-group>
          }
        </thy-menu>
      </thy-sidebar-content>
    </thy-sidebar>
    <thy-layout>
      <router-outlet></router-outlet>
    </thy-layout>
  </thy-layout>
} @else {
  <thy-loading [thyDone]="loadingDone"></thy-loading>
}
