import { Component, DestroyRef, HostBinding, OnInit, ViewChild, ViewContainerRef, inject } from '@angular/core';
import { PilotEntryStore, StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Direction, StyxTableComponent, StyxTableEmitEvent, StyxTableRow, UtilService } from '@atinc/ngx-styx';
import { DeletePageInfo, PageInfo } from '@wiki/app/entities/page-info';
import { PageApiService } from '@wiki/app/services/page-api.service';
import { PageService } from '@wiki/app/services/util/page.service';
import { filterAndSortPageData } from '@wiki/app/util/tree';
import { IsPageGroupPipe, IsPagePipe } from '@wiki/common/pipe/page.pipe';
import { ThyTableSize } from 'ngx-tethys/table';
import { of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { DeletePageStore } from './delete-pages.store';

@Component({
    selector: 'wiki-space-trash',
    templateUrl: './trash.component.html',
    providers: [DeletePageStore],
    standalone: false
})
export class SpaceSettingsTrashComponent implements OnInit {
    private translate = inject(StyxTranslateService);
    @HostBinding('class') classNames = 'wiki-space-trash d-flex-column-fill overflow-hidden';

    public loadingDone = false;

    protected pageCount = 0;

    deletePages: DeletePageInfo[] = [];

    initPages: DeletePageInfo[] = [];

    sortConfig = {
        sortBy: 'deleted_at',
        sortDirection: Direction.default
    };

    tableSize: ThyTableSize = 'md';

    searchText = '';

    columns = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('common.name'),
            raw_key: 'name',
            property_key: 'name',
            key: 'name',
            type: 1,
            fixed: 'left',
            width: '50%',
            sortable: true,
            expandable: true
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.deleteBy'),
            raw_key: 'deleted_by',
            property_key: 'deleted_by',
            key: 'deleted_by',
            type: 9,
            lookup: 'members',
            width: '25%'
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.statistics.deleteTime'),
            raw_key: 'deleted_at',
            property_key: 'deleted_at',
            key: 'deleted_at',
            sortable: true,
            type: 4,
            width: '25%'
        }
    ];

    @ViewChild(StyxTableComponent) table: StyxTableComponent;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private pageApiService: PageApiService,
        private util: UtilService,
        private deletePageStore: DeletePageStore,
        private pageService: PageService,
        private viewContainerRef: ViewContainerRef,
        private isPageGroupPipe: IsPageGroupPipe,
        private isPagePipe: IsPagePipe,
        private destroyRef: DestroyRef
    ) {}

    ngOnInit() {
        this.fetchDeletePages();
        this.deletePageStore
            .select$(state => state.entities)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe(data => {
                this.initPages = data;
                this.onSearch(this.searchText);
            });
    }

    fetchDeletePages() {
        const fetchDeletePages$ = this.pageApiService.fetchDeletePages(this.pilotStore.snapshot.detail._id);
        this.loadingDone = false;
        const pages = [];
        const pageMap = new Map<string, PageInfo>();
        fetchDeletePages$.subscribe(currentPages => {
            currentPages.forEach(page => {
                if (!pageMap.has(page._id)) {
                    pageMap.set(page._id, page);
                    pages.push(page);
                    return;
                }
            });
            this.loadingDone = true;
            this.deletePageStore.initialize(pages.filter(page => page.is_published) as any);
            // 现在递归函数没有返回成员需要额外处理
            // this.globalUsersStore.addUsers(data.references.members);
            this.sortChange(this.sortConfig);
        });
    }

    rowClick(event: StyxTableEmitEvent) {
        const pageInfo: PageInfo = event.row;
        if (this.isPageGroupPipe.transform({ type: pageInfo.type })) {
            this.table.expandChildren(event.event, event.row);
        }
    }

    onTrashRestore(event, selections: string[]) {
        this.pageApiService.restorePages(this.pilotStore.snapshot.detail._id, selections).subscribe(
            () => {
                this.util.notify.success(null, this.translate.instant<I18nSourceDefinitionType>(`wiki.statistics.restoreSuccess`));
                this.deletePageStore.remove(selections);
            },
            catchError(error => {
                this.util.defaultErrorHandler()(error);
                return of(true);
            })
        );
    }

    showExpand(row: StyxTableRow) {
        return row.children && row.children.length > 0;
    }

    loadChildren(row: StyxTableRow) {
        return of(row.children);
    }

    openPageDialog(row: DeletePageInfo) {
        if (this.isPagePipe.transform({ type: row.type })) {
            this.pageService.openWikiPageDialog({
                pageId: row._id,
                viewContainerRef: this.viewContainerRef
            });
        }
    }

    getPageCount() {
        const pages = !this.searchText ? this.initPages : this.deletePages;
        return pages.filter(page => this.isPagePipe.transform({ type: page.type })).length;
    }

    onSearch(keywords: string) {
        this.searchText = keywords;
        this.columns[0].expandable = !this.searchText;
        if (!this.searchText) {
            this.sortConfig = {
                sortBy: '',
                sortDirection: Direction.default
            };
        }
        this.deletePages = filterAndSortPageData<DeletePageInfo>(this.initPages, this.searchText, this.sortConfig);
        this.pageCount = this.getPageCount();
    }

    sortChange(event: { sortBy: string; sortDirection: Direction }) {
        this.sortConfig = {
            sortBy: event.sortDirection ? event.sortBy : '',
            sortDirection: event.sortDirection
        };
        this.deletePages = filterAndSortPageData<DeletePageInfo>(this.initPages, this.searchText, this.sortConfig);
    }
}
