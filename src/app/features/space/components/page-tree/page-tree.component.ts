import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    Input,
    OnInit,
    QueryList,
    Signal,
    ViewChild,
    ViewChildren,
    ViewContainerRef,
    WritableSignal,
    computed,
    effect,
    inject,
    input,
    signal
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import {
    GlobalUsersStore,
    Is,
    PilotEntryStore,
    PilotScopeType,
    ResponseData,
    StyxAwesomeTextInputEvent,
    StyxTranslateService,
    UtilService,
    hasPermissionTransform,
    injectBizValidatorMaxLengths
} from '@atinc/ngx-styx';
import { DRAFT_GROUP, PAGE_TREE_NODE_HEIGHT, PageTypes } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo } from '@wiki/app/entities/page-info';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PageTreeService } from '@wiki/app/services/page-tree.service';
import { PageService } from '@wiki/app/services/util/page.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { getTreeIndex } from '@wiki/app/util/tree';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { ThyDragDropEvent, ThyDragStartEvent, ThyDropPosition } from 'ngx-tethys/drag-drop';
import { ThyPopoverDirective } from 'ngx-tethys/popover';
import { ThyTree, ThyTreeDragDropEvent, ThyTreeEmitEvent, ThyTreeNode, ThyTreeNodeData } from 'ngx-tethys/tree';
import { finalize } from 'rxjs/operators';
import { PagePreviewComponent } from '../../../page/preview/preview.component';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { CommonPageApiService } from '@wiki/common/services';
import { PagePermissionScopeType } from '@wiki/app/constants';

@Component({
    selector: 'space-page-tree',
    templateUrl: './page-tree.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SpacePageTreeComponent implements OnInit {
    @Input() spaceId: string;

    @Input() pageNodes: ThyTreeNodeData[];

    isVisible = input(true);

    @ViewChild(ThyTree, { static: false }) tree: ThyTree;

    @ViewChildren(ThyPopoverDirective)
    previewTriggers: QueryList<ThyPopoverDirective>;

    selectedIdOrShortId: WritableSignal<string> = signal('');

    selectedPageId: Signal<string> = computed(() => {
        const id = this.selectedIdOrShortId();
        const page = this.pagesStore.snapshot.entities.find(item => item._id === id || item.short_id === id);
        return id && page ? page?._id : '';
    });

    public editStateId = '';

    public editTitle = '';

    public editEmoji = '';

    titleMaxLength = injectBizValidatorMaxLengths().longTitle;

    protected placeholder: string = '';

    public thyIcons = { expand: 'angle-down', collapse: 'angle-right' };

    public saving = false;

    previewComponent = PagePreviewComponent;

    private get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    get isDisablePreview() {
        return (
            this.elementRef.nativeElement.querySelector('.more-active') ||
            (this.pageTreeService.searchKeywords && this.pageTreeService.isSearchMode)
        );
    }

    get spaceScopeType() {
        return this.pilotStore.snapshot.detail.scope_type;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private route: ActivatedRoute,
        private util: UtilService,
        public userStore: GlobalUsersStore,
        private pagesStore: PagesStore,
        private commonPageApiService: CommonPageApiService,
        public pageTreeService: PageTreeService,
        private pageService: PageService,
        private globalPageStore: PageEventBus,
        private elementRef: ElementRef,
        private cdr: ChangeDetectorRef,
        private errorService: WikiErrorService,
        private translate: StyxTranslateService,
        private viewContainerRef: ViewContainerRef
    ) {
        effect(() => {
            this.revertPosition();
        });
    }

    ngOnInit() {
        if (this.route.snapshot.firstChild) {
            const isDraftList = this.route.snapshot.firstChild.routeConfig.path.includes('draft-pages');
            const pageIdOrShortId = !isDraftList && this.route.snapshot.firstChild.paramMap.get('pageIdOrShortId');
            this.selectedIdOrShortId.set(pageIdOrShortId);
        }
    }

    revertPosition() {
        const isVisible = this.isVisible();
        if (isVisible) {
            setTimeout(() => {
                this.tree.viewport.checkViewportSize();
            });
        }
    }

    setSelectedPage(pageIdOrShortId: string) {
        const node = this.getTreeNode(this.selectedPageId());
        if (this.tree.isSelected(node)) {
            this.tree.toggleTreeNode(node);
        }
        this.selectedIdOrShortId.set(pageIdOrShortId);
    }

    onClick(event: ThyTreeEmitEvent) {
        const pageInfo: PageInfo = event.node.origin;
        if (pageInfo.type === PageTypes.group) return;
        const pageIdOrShortId = pageInfo?.short_id || pageInfo._id;
        this.selectedIdOrShortId.set(pageIdOrShortId);
        this.router.navigate(['/wiki/spaces', this.currentSpace.identifier, 'pages', pageIdOrShortId]);
        this.previewTriggers.forEach(item => {
            if (item.elementRef.nativeElement.contains(event.event.target)) {
                item.hide();
            }
        });
    }

    beforeDragStart() {
        return (event: ThyDragStartEvent<ThyTreeNode>) => {
            return this.canDrag(event);
        };
    }

    beforeDragDrop() {
        return (event: ThyDragDropEvent<ThyTreeNode>) => {
            const page = event.position === ThyDropPosition.in ? event.item.origin : event.item.parentNode?.origin;
            if (page && !hasPermissionTransform(page.permissions, wikiPermissionPoints, 'page_create')) {
                this.util.notify.error(
                    this.translate.instant<I18nSourceDefinitionType>('wiki.error.moveError'),
                    this.translate.instant<I18nSourceDefinitionType>('wiki.error.moveNoPermission')
                );
                return false;
            }
            return event.item.origin._id !== this.homePage?._id && event.previousItem.origin._id !== this.homePage?._id;
        };
    }

    canDrag(event: ThyDragStartEvent<ThyTreeNode>) {
        const pageId = event.item.origin._id;
        return (
            hasPermissionTransform(event.item.origin.permissions, wikiPermissionPoints, 'page_move') &&
            !event.item.origin.is_lock &&
            pageId !== this.homePage?._id &&
            pageId !== this.editStateId
        );
    }

    onDragDrop(event: ThyTreeDragDropEvent) {
        const { afterNode, targetNode, dragNode } = event;
        let parentId: string = targetNode ? targetNode.origin._id : '';
        parentId = parentId === this.homePage?._id ? '' : parentId;
        let afterId: string = afterNode ? afterNode.origin._id : '';
        afterId = afterId === parentId ? '' : afterId;
        this.pagesStore.dragPage(this.spaceId, dragNode.origin._id, { parentId, afterId }).subscribe({
            next: (data: ResponseData<PageInfo[]>) => {
                const page = data.value.find(item => item._id === dragNode.origin._id);
                const { position, parent_id, parent_ids } = page;
                this.globalPageStore.emitPageInfo({ ...dragNode.origin, position, parent_id, parent_ids });
                this.pageTreeService.updatePageTreeNodes(this.pagesStore.snapshot.entities);
            },
            error: error => {
                this.errorService.defaultErrorHandler(error);
            }
        });
        this.selectPage(this.selectedIdOrShortId());
    }

    onPageInputEdit = (treeNode: ThyTreeNode<PageInfo>) => {
        this.placeholder = this.translate.instant('wiki.page.name.placeholder');
        this.editStateId = treeNode.key.toString();
        this.editTitle = treeNode.title;
        this.editEmoji = (treeNode as PageInfo).emoji_icon;
        this.cdr.detectChanges();
    };

    onSavePage(data: StyxAwesomeTextInputEvent, treeNode: ThyTreeNode<PageInfo>) {
        ((data.event.currentTarget as HTMLElement).closest('.thy-tree-node') as HTMLElement).classList.remove('rename-mode');
        if (this.saving) {
            return;
        }
        (treeNode as PageInfo).emoji_icon = data.emoji;
        treeNode.title = data.value;
        this.editStateId = null;
        if (treeNode.key === DRAFT_GROUP) {
            const name = (treeNode.title || '').trim();
            const newGroup: DraftInfo = {
                name,
                type: PageTypes.group,
                after_id: (treeNode as DraftInfo)?.after_id,
                parent_id: (treeNode as PageInfo).parent_id,
                is_published: Is.yes,
                emoji_icon: (treeNode as PageInfo).emoji_icon
            };
            if (name) {
                this.pageService.addGroup(this.spaceId, newGroup).subscribe(() => {
                    this.pagesStore.pureRemovePageById(DRAFT_GROUP);
                });
            } else {
                this.pagesStore.pureRemovePageById(DRAFT_GROUP);
            }
            this.cdr.detectChanges();
            return;
        }
        if ((!treeNode.title.trim() || treeNode.title === this.editTitle) && data.emoji === this.editEmoji) {
            treeNode.title = this.editTitle;
        } else {
            this.updatePage(treeNode);
        }
    }

    updateTreeNodeTitle(pageInfo: PageInfo) {
        const node = this.tree.thyTreeService.getTreeNode(pageInfo._id);
        if (node) {
            node.origin.title = pageInfo.name;
            node.origin.emoji_icon = pageInfo.emoji_icon;
            this.cdr.markForCheck();
        }
    }

    public updatePage(treeNode: ThyTreeNode<PageInfo>) {
        const name = treeNode.title.trim();
        if (!name) {
            return;
        }
        const page = treeNode as PageInfo;
        this.saving = true;
        (treeNode as PageInfo).name = name;
        this.commonPageApiService
            .updatePage((treeNode as PageInfo)._id, { name: page.name, emoji_icon: page.emoji_icon })
            .pipe(
                finalize(() => {
                    this.saving = false;
                    this.cdr.markForCheck();
                })
            )
            .subscribe({
                next: (data: ResponseData<PageInfo>) => {
                    this.globalPageStore.emitPageInfo({
                        ...page,
                        ...data.value
                    });
                },
                error: error => {
                    this.errorService.defaultErrorHandler(error);
                }
            });
    }

    getTreeNode(pidOrShortId: string) {
        const page = this.pagesStore.snapshot.entities.find(item => item._id === pidOrShortId || item.short_id === pidOrShortId);
        return this.tree.thyTreeService.getTreeNode(page?._id);
    }

    selectPage(pidOrShortId: string) {
        const node = this.getTreeNode(pidOrShortId);
        if (node) {
            this.selectedIdOrShortId.set(pidOrShortId);
            this.tree.selectTreeNode(node);
            this.expandedTree(node);
        }
    }

    onDblClick(event: ThyTreeEmitEvent) {
        const isExpanded = event.node.isExpanded;
        event.node.setExpanded(!isExpanded);
    }

    expandedTree(node: ThyTreeNode) {
        // Expand the parent node when the selected parent node is not expanded
        if (node) {
            let currentNode = node;
            while (currentNode.parentNode) {
                if (!currentNode.parentNode.isExpanded) {
                    currentNode.parentNode.setExpanded(true);
                }
                currentNode = currentNode.parentNode;
            }
            this.cdr.detectChanges();
        }
    }

    getScrollIndex(pageId?: string) {
        const id = pageId || this.selectedPageId();
        return this.pageTreeService.viewConfig?.sortBy || (this.pageTreeService.isSearchMode && this.pageTreeService.searchKeywords)
            ? this.pageNodes.findIndex(it => it.key === id)
            : getTreeIndex(this.pageNodes, id);
    }

    scrollToCurrentNode() {
        const node = this.getTreeNode(this.selectedPageId());

        // 可视区域内不处理
        if (!node || this.scrollNodeIsVisible(this.elementRef.nativeElement.querySelector('.thy-tree-node-wrapper.active'))) {
            return;
        }
        const scrollTop = this.elementRef.nativeElement.querySelector('.cdk-virtual-scroll-viewport')?.scrollTop;
        if (scrollTop > 0) {
            return;
        }

        let scrollIndex = this.getScrollIndex();
        // 滚动到中间偏上区域
        if (scrollIndex < 4) {
            scrollIndex = 0;
        } else {
            scrollIndex = scrollIndex - 4;
        }
        this.tree.viewport.scrollToIndex(scrollIndex);
    }

    scrollToDraftGroupNode() {
        // 仅向上滑动至子分组显示出来（可视容器最下方）
        const scrollIndex = this.getScrollIndex(DRAFT_GROUP);
        const pageTreeHeight = this.elementRef.nativeElement.getBoundingClientRect().height;
        const index = scrollIndex - (~~(pageTreeHeight / PAGE_TREE_NODE_HEIGHT) - 1);
        this.tree.viewport.scrollToIndex(index);
    }

    unselectPage() {
        this.tree.toggleTreeNode(this.tree.getSelectedNode());
    }

    getDraftGroup() {
        let draftGroup: HTMLElement;
        const inputElement = (this.elementRef.nativeElement as HTMLElement).querySelector('.styx-awesome-text-input');
        if (inputElement) {
            draftGroup = inputElement.closest('.thy-tree-node') as HTMLElement;
        }
        return draftGroup;
    }

    scrollNodeIsVisible(target: HTMLElement) {
        const buffer = PAGE_TREE_NODE_HEIGHT / 2; // 一个节点的高度
        if (target) {
            const containerRect = this.elementRef.nativeElement.getBoundingClientRect();
            const targetRect = target.getBoundingClientRect();
            if (targetRect.top > containerRect.top - buffer && targetRect.bottom < containerRect.bottom + buffer) {
                return true;
            }
        }
        return false;
    }

    previewNavigationCallback(pageId: string) {
        return () => {
            this.selectPage(pageId);
        };
    }

    openPageSharing(page: PageInfo) {
        this.pageService.openPageSharing({
            isShared: page.is_shared,
            spaceId: page.space_id,
            pageId: page._id,
            shortId: page.short_id,
            isConfiguration: false,
            viewContainerRef: this.viewContainerRef,
            spaceScopeType: this.spaceScopeType,
            selectType:
                this.spaceScopeType === PilotScopeType.personal ? PagePermissionScopeType.spaceOutside : PagePermissionScopeType.spaceInside
        });
    }
}
