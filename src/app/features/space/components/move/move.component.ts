import { Component, inject, Input, OnInit } from '@angular/core';
import {
    GlobalApplicationPermissionPipe,
    PilotBaseEntity,
    PilotEntryStore,
    PilotScopeType,
    StyxTranslateService,
    SubAppRootContext,
    UtilService
} from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { of, throwError } from 'rxjs';
import { catchError, mergeMap } from 'rxjs/operators';

@Component({
    selector: 'space-move',
    templateUrl: './move.component.html',
    providers: [GlobalApplicationPermissionPipe],
    standalone: false
})
export class SpaceMoveComponent implements OnInit {
    @Input()
    set spaceInfo(value: SpaceInfo) {
        this.pilotEntity = value;
    }

    private pilotStore = inject(PilotEntryStore);

    pilotEntity: PilotBaseEntity;

    scopeTypes = [PilotScopeType.organization, PilotScopeType.personal, PilotScopeType.team];

    translate = inject(StyxTranslateService);

    constructor(
        private subAppRootContext: SubAppRootContext,
        private globalApplicationPermission: GlobalApplicationPermissionPipe,
        private util: UtilService
    ) {}

    ngOnInit(): void {
        const hasCreateGlobalSpacePermission = this.globalApplicationPermission.transform(
            this.subAppRootContext.snapshot.globalPermissions,
            'create_space'
        );
        if (!hasCreateGlobalSpacePermission) {
            this.scopeTypes = this.scopeTypes.filter(item => item !== PilotScopeType.organization);
        }
    }

    change(event: PilotBaseEntity) {
        this.pilotEntity = { ...event };
        this.pilotStore.updateEntryAction(this.pilotEntity);
    }

    styxMoveEntryItem$ = () => {
        const params = {
            ...this.pilotEntity,
            ...{
                user_group_ids: this.pilotEntity?.scope_type === PilotScopeType.team ? this.pilotEntity.user_group_ids : []
            }
        };
        return this.pilotStore.moveEntry(params).pipe(
            catchError(error => {
                return throwError(error);
            }),
            mergeMap((res: any) => {
                if (res) {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.moveSuccess'));
                    return of(res);
                } else {
                    return throwError(res);
                }
            })
        );
    };
}
