import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    HostBinding,
    inject,
    Input,
    OnInit,
    ViewChild
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GlobalUsersStore, PilotEntryStore } from '@atinc/ngx-styx';
import { ScrollToService } from 'ngx-tethys/core';
import { ThyTree, ThyTreeEmitEvent, ThyTreeNodeData } from 'ngx-tethys/tree';

@Component({
    selector: 'space-draft-tree',
    templateUrl: './draft-tree.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class SpaceDraftTreeComponent implements OnInit, AfterViewInit {
    @Input() spaceId: string;

    @Input()
    set draftPages(value) {
        this.draftTrees = value.map(i => ({ key: i._id, title: i.name, ...i }));
    }

    @Input() draftId: string;

    @ViewChild(ThyTree, { static: false }) tree: ThyTree;

    @HostBinding('class') hostClass = `space-draft-tree-container`;

    draftTrees: ThyTreeNodeData[] = [];

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        public userStore: GlobalUsersStore,
        private route: ActivatedRoute,
        private elementRef: ElementRef
    ) {}

    ngOnInit() {}

    ngAfterViewInit(): void {
        if (this.route.snapshot.firstChild) {
            const draftIdOrShortId = this.route.snapshot.firstChild.paramMap.get('draftIdOrShortId');
            this.draftId = draftIdOrShortId;
        }
    }

    onClick(event: ThyTreeEmitEvent) {
        this.draftId = event.node.origin?.short_id ?? event.node.origin._id;
        this.router.navigate(['/wiki/spaces', this.pilotStore.snapshot.detail?.identifier, 'draft-pages', this.draftId]);
    }

    selectDraft(draftIdOrShortId: string) {
        if (!this.tree) {
            return;
        }
        const page = this.draftTrees.find(i => i?.short_id === draftIdOrShortId || i._id === draftIdOrShortId);
        const node = this.tree.thyTreeService.getTreeNode(page._id);
        if (node) {
            this.tree.selectTreeNode(node);
            setTimeout(() => {
                ScrollToService.scrollToElement(
                    this.elementRef.nativeElement.querySelector('.active'),
                    this.elementRef.nativeElement.parentElement
                );
            });
        }
    }

    unselectDraft() {
        if (!this.tree) {
            return;
        }
        this.tree.toggleTreeNode(this.tree.getSelectedNode());
    }
}
