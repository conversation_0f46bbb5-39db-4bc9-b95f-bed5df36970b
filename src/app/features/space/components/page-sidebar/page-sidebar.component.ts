import {
    AfterViewInit,
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    HostBinding,
    inject,
    Input,
    model,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { Direction, Is, PilotEntryStore, SortConfigInfo, UtilService, cache } from '@atinc/ngx-styx';
import { WikiPageTreeOperationComponent } from '@wiki/app/components/page-tree-operation/page-tree-operation.component';
import { PageSortBy, RecentOperationType, WIKI_PAGE_LIST_SORT_MENU } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { PageApiService } from '@wiki/app/services';
import { PageTreeService } from '@wiki/app/services/page-tree.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { Subscription } from 'rxjs';
import { map } from 'rxjs/operators';
import { ActiveType } from '../../detail/detail.component';
import { SpacePageTreeComponent } from '../page-tree/page-tree.component';

@Component({
    selector: 'space-page-sidebar',
    templateUrl: './page-sidebar.component.html',
    standalone: false
})
export class SpacePageSidebarComponent implements OnInit, OnChanges, AfterViewInit {
    @HostBinding('class') class = 'wiki-space-page-sidebar';

    @Input() isCollapsed: boolean;

    @Input() spaceId: string;

    pageId = model(null);

    // 一定有值，pageIdOrShortId 值来源于路由参数，pageId 在初期可能没有值，它的值来源元 signal 的计算属性
    pageIdOrShortId = model(null);

    @Input() isHome: boolean;

    @Input() isVisible: boolean;

    @Output() changeType: EventEmitter<ActiveType> = new EventEmitter();

    @ViewChild(SpacePageTreeComponent)
    pageTreeComponent: SpacePageTreeComponent;

    loadingDone = false;

    recentlyBrowseSubscription: Subscription;

    sortPagesSubscription: Subscription;

    treePagesSubscription: Subscription;

    private get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    get currentSpace(): SpaceInfo {
        return this.pilotStore.snapshot.detail;
    }

    get spaceMembersCount() {
        return this.currentSpace?.members.length + this.currentSpace?.members_be_user_group?.length;
    }

    get nativeElement() {
        return this.elementRef.nativeElement;
    }

    get sortBy() {
        return this.pageTreeService.viewConfig?.sortBy;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public pageTreeService: PageTreeService,
        private elementRef: ElementRef<HTMLElement>,
        private util: UtilService,
        private router: Router,
        private route: ActivatedRoute,
        private pagesStore: PagesStore,
        private pageApiService: PageApiService,
        private destroyRef: DestroyRef
    ) {}

    ngOnInit(): void {
        this.initializeViewConfig();
        this.fetchPages();
    }

    ngAfterViewInit(): void {
        this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            if (params.idOrIdentifier && this.spaceId !== this.currentSpace?._id) {
                this.spaceId = this.currentSpace?._id;
                this.pageIdOrShortId = null;
                this.initializeViewConfig();
                this.fetchPages();
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        const isVisible = changes['isVisible'];
        if (isVisible) {
            if (this.isVisible) {
                this.elementRef.nativeElement.classList.remove('d-none');
            } else {
                this.elementRef.nativeElement.classList.add('d-none');
            }
        }
    }

    keywordsChanges(keywords: string) {
        let pages: PageInfo[] = this.pagesStore.snapshot.entities;
        if (keywords !== '') {
            pages = this.pagesStore.snapshot.entities.filter(
                item => item.name.toLowerCase().includes(keywords.toLowerCase()) && item.is_visibility === Is.yes
            );
        }
        this.pageTreeService.updatePageNodes(pages);
    }

    openSortMenu(event: MouseEvent) {
        this.util.popover.open(WikiPageTreeOperationComponent, {
            origin: event.currentTarget as HTMLElement,
            placement: 'bottomLeft',
            insideClosable: false,
            originActiveClass: 'active',
            offset: 2,
            initialState: {
                space: this.currentSpace,
                sortBy: this.pageTreeService.viewConfig.sortBy,
                sortDirection: this.pageTreeService.viewConfig.sortDirection,
                onSort: (sortBy: string, sortDirection: Direction) => {
                    const viewConfig: SortConfigInfo = {
                        sortBy: sortDirection ? sortBy : '',
                        sortDirection
                    };
                    this.updateViewConfig(viewConfig);
                    this.fetchPages();
                    this.util.popover.close();
                }
            }
        });
    }

    selectPage(pageId: string) {
        setTimeout(() => {
            if (this.pageTreeComponent) {
                this.pageTreeComponent.selectPage(pageId);
            }
        });
    }

    fetchPages() {
        if (!this.spaceId) {
            return;
        }
        const sortBy = this.pageTreeService.viewConfig?.sortBy;

        this.loadingDone = false;
        switch (sortBy) {
            case PageSortBy.recentlyBrowse:
                this.recentlyBrowseSubscription = this.fetchRecentlyOperatedPages();
                break;
            case PageSortBy.createdAt:
            case PageSortBy.publishedAt:
            case PageSortBy.name:
                this.sortPagesSubscription = this.fetchSortPages();
                break;
            default:
                this.treePagesSubscription = this.fetchPageTree();
                break;
        }
    }

    fetchPageTree() {
        this.recentlyBrowseSubscription?.unsubscribe();
        this.sortPagesSubscription?.unsubscribe();

        return this.pageApiService
            .fetchPageTree(this.spaceId)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (pages: PageInfo[]) => {
                    const isPageTreeMode = !this.pageTreeService.viewConfig?.sortBy;
                    if (isPageTreeMode) {
                        this.loadingDone = true;
                        this.pagesStore.initializePages(pages);
                        this.gotoPage(pages);
                    }
                }
            });
    }

    fetchSortPages(recentlyBrowsePages?: PageInfo[]) {
        this.treePagesSubscription?.unsubscribe();
        this.recentlyBrowseSubscription?.unsubscribe();
        this.sortPagesSubscription?.unsubscribe();

        return this.pageApiService
            .fetchPages(this.spaceId, recentlyBrowsePages ? null : this.pageTreeService.viewConfig)
            .pipe(map((pages: PageInfo[]) => pages.filter(x => x.is_visibility)))
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe({
                next: (pages: PageInfo[]) => {
                    const isSortMode = this.pageTreeService.viewConfig?.sortBy !== PageSortBy.recentlyBrowse;
                    if (isSortMode) {
                        let sortPages = pages;
                        if (recentlyBrowsePages) {
                            const recentlyBrowsePageIds = recentlyBrowsePages.map(x => x._id);
                            sortPages = pages
                                .sort((a, b) => b.created_at - a.created_at)
                                .filter(x => !recentlyBrowsePageIds.includes(x._id));
                            sortPages.unshift(...recentlyBrowsePages);
                            this.pagesStore.initializePages(sortPages);
                        } else {
                            this.loadingDone = true;
                            this.pagesStore.initializePages(sortPages);
                            this.gotoPage(sortPages);
                        }
                    }
                },
                error: error => {
                    this.util.defaultErrorHandler()(error);
                }
            });
    }

    fetchRecentlyOperatedPages() {
        this.treePagesSubscription?.unsubscribe();
        this.sortPagesSubscription?.unsubscribe();
        this.recentlyBrowseSubscription?.unsubscribe();

        return this.pageApiService
            .fetchRecentlyOperatedPages(this.spaceId, RecentOperationType.browse)
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((recentlyBrowsePages: PageInfo[]) => {
                const isRecentlyMode = this.pageTreeService.viewConfig?.sortBy === PageSortBy.recentlyBrowse;
                if (isRecentlyMode) {
                    this.loadingDone = true;
                    const recentlyBrowsePage = recentlyBrowsePages.filter(x => !!x.is_published && x.is_visibility);
                    this.pagesStore.initializePages(recentlyBrowsePage);
                    this.gotoPage(recentlyBrowsePage);
                    this.sortPagesSubscription = this.fetchSortPages(recentlyBrowsePage);
                }
            });
    }

    gotoPage(pages: PageInfo[]) {
        const isDraft = this.router.routerState.snapshot.url.includes('draft');
        if (isDraft || !this.homePage) {
            return;
        }
        if (
            !this.pageIdOrShortId() ||
            (this.pageTreeService.viewConfig?.sortBy &&
                !pages.some(x => x._id === this.pageIdOrShortId() || x.short_id === this.pageIdOrShortId()))
        ) {
            this.pageId.set(this.homePage._id);
            const id = this.homePage.short_id ?? this.homePage._id;
            this.router.navigate(['pages', id], { relativeTo: this.route, replaceUrl: true });
        }
    }

    initializeViewConfig() {
        let viewConfig: SortConfigInfo = {
            sortBy: '',
            sortDirection: Direction.ascending
        };
        const defaultSort = cache.get<SortConfigInfo>(`${WIKI_PAGE_LIST_SORT_MENU}_${this.spaceId}`);
        if (defaultSort && !Object.is(defaultSort, viewConfig)) {
            viewConfig = defaultSort;
        }
        this.pageTreeService.updateViewConfig(viewConfig);
    }

    updateViewConfig(viewConfig: SortConfigInfo) {
        if (viewConfig.sortBy) {
            cache.set<SortConfigInfo>(`${WIKI_PAGE_LIST_SORT_MENU}_${this.spaceId}`, {
                sortBy: viewConfig.sortBy,
                sortDirection: viewConfig.sortDirection
            });
        } else {
            cache.remove(`${WIKI_PAGE_LIST_SORT_MENU}_${this.spaceId}`);
        }
        this.pageTreeService.updateViewConfig(viewConfig);
    }

    changeSidebar = () => {
        this.changeType.emit('draft');
    };

    goSettings(type: 'members' | 'trash') {
        this.router.navigate([`./settings/${type}`], { relativeTo: this.route });
    }
}
