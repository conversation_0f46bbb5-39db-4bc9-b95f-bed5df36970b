import {
    AfterViewInit,
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    HostBinding,
    inject,
    Input,
    OnChanges,
    OnInit,
    Output,
    SimpleChanges,
    ViewChild
} from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { PageDraftStore } from '@wiki/app/stores/draft-page.store';
import { finalize } from 'rxjs/operators';
import { ActiveType } from '../../detail/detail.component';
import { SpaceDraftTreeComponent } from '../draft-tree/draft-tree.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { PilotEntryStore } from '@atinc/ngx-styx';

@Component({
    selector: 'space-draft-sidebar',
    templateUrl: './draft-sidebar.component.html',
    standalone: false
})
export class SpaceDraftSidebarComponent implements OnInit, OnChanges, AfterViewInit {
    @HostBinding('class') class = 'wiki-space-draft-sidebar';

    @Input() spaceId: string;

    @Input() draftId: string;

    @Input() isVisible: boolean;

    @Output() changeType: EventEmitter<ActiveType> = new EventEmitter();

    @ViewChild(SpaceDraftTreeComponent)
    draftTreeComponent: SpaceDraftTreeComponent;

    loadingDone = false;

    spaceIdentifier: string;

    get nativeElement() {
        return this.elementRef.nativeElement;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private elementRef: ElementRef<HTMLElement>,
        private route: ActivatedRoute,
        private destroyRef: DestroyRef,
        public pageDraftStore: PageDraftStore
    ) {}

    ngOnInit(): void {
        this.pageDraftStore
            .getPageDraftList(this.spaceId)
            .pipe(
                finalize(() => {
                    this.loadingDone = true;
                })
            )
            .subscribe(() => {
                setTimeout(() => {
                    this.draftTreeComponent?.selectDraft(this.draftId);
                }, 0);
            });
    }

    ngAfterViewInit(): void {
        this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            const currentSpace = this.pilotStore.snapshot.detail;
            if (params.idOrIdentifier && this.spaceId !== currentSpace?._id) {
                this.spaceId = currentSpace?._id;
                this.pageDraftStore.getPageDraftList(this.spaceId);
            }
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        const isVisible = changes['isVisible'];
        if (isVisible) {
            if (this.isVisible) {
                this.elementRef.nativeElement.classList.remove('d-none');
            } else {
                this.elementRef.nativeElement.classList.add('d-none');
            }
        }
    }

    changeSidebar = () => {
        this.changeType.emit('page');
    };
}
