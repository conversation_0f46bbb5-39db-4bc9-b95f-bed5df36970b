import { Component, DestroyRef, HostB<PERSON>ing, OnInit, ViewContainerRef, inject } from '@angular/core';
import { PilotEntryStore, PropertyType, StyxPropertyService, StyxTagApiService, StyxTagScene, StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import {
    CodeStatus,
    PilotScopeType,
    StyxAdditionsInfo,
    StyxBaselineConfig,
    StyxBaselineService,
    StyxPermissionService,
    StyxReviewConfigService,
    WikiBroadObjectTypes,
    cache,
    css
} from '@atinc/ngx-styx';
import { API_PREFIX, WIKI_SPACE_ROUTER_KEY } from '@wiki/app/constants';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { SpaceService } from '@wiki/app/services/util/space.service';
import { getAddons } from '@wiki/app/util/addons';
import { ScopeTypeIsPersonalPipe } from '@wiki/common/pipe/scope-type.pipe';
import { Observable } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
    selector: 'wiki-space',
    templateUrl: './space.component.html',
    host: {
        class: 'wiki-space wiki-fullscreen-container'
    },
    providers: [StyxBaselineService],
    standalone: false
})
export class SpaceComponent implements OnInit {
    private styxPropertyService = inject(StyxPropertyService);

    private styxTagApiService = inject(StyxTagApiService);

    private translate = inject(StyxTranslateService);

    @HostBinding(`class`) className = `${css.layout} common-space-container`;

    isPersonalSpace = false;

    public error: { code: CodeStatus; additions?: StyxAdditionsInfo & { scope_type: PilotScopeType } };

    get space() {
        return this.pilotStore.snapshot.detail;
    }

    private viewContainerRef = inject(ViewContainerRef);

    public pilotStore = inject(PilotEntryStore);

    constructor(
        private route: ActivatedRoute,
        private baselineService: StyxBaselineService,
        private permissionService: StyxPermissionService,
        private router: Router,
        private spaceService: SpaceService,
        private scopeTypeIsPersonalPipe: ScopeTypeIsPersonalPipe,
        private reviewConfigService: StyxReviewConfigService,
        private destroyRef: DestroyRef
    ) {
        this.registerProperties();
    }

    ngOnInit() {
        if (!this.space) {
            this.error = this.route.snapshot.data.pilot;
            this.isPersonalSpace = this.scopeTypeIsPersonalPipe.transform(this.error.additions?.scope_type);
            return;
        }
        this.initializeBaselineConfig();
        this.initializeReviewConfig();

        this.route.params.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            if (params?.idOrIdentifier) {
                this.initializeBaselineConfig();
                this.initializeReviewConfig();
            }
        });

        (
            this.router.events.pipe(
                takeUntilDestroyed(this.destroyRef),
                filter(event => event instanceof NavigationEnd)
            ) as Observable<NavigationEnd>
        ).subscribe(router => {
            const urlAddonKey = this.getUrlAddonKey(router.url);
            const regex = /pages\/|reviews\//;
            if (urlAddonKey && !regex.test(router.url)) {
                cache.set(`${WIKI_SPACE_ROUTER_KEY}${this.route.snapshot.params.idOrIdentifier}`, { key: urlAddonKey, url: router.url });
            }
        });
    }

    private registerProperties() {
        (this.styxPropertyService as any).componentsKeyMap[PropertyType.tag].config = {
            broadObjectType: WikiBroadObjectTypes.page,
            showPilot: false,
            optionsResolver: () => {
                return this.styxTagApiService.fetchSceneTags(API_PREFIX, WikiBroadObjectTypes.page, {
                    scene: StyxTagScene.pilot_with_related_global,
                    scope_id: this.pilotStore.snapshot.detail._id
                });
            }
        };
    }

    private initializeBaselineConfig() {
        this.baselineService.setPrincipalBaselineConfig({
            apiPrefix: `/api/wiki/spaces/${this.space?._id}`,
            fetchSelectablePilotsApi: `${API_PREFIX}/spaces`,
            pilotBroadObjectType: WikiBroadObjectTypes.space,
            editable: this.permissionService.hasPermission(this.space?.permissions, 'manage_baseline'),
            principalTitle: this.translate.instant<I18nSourceDefinitionType>('styx.page'),
            completeBaselineDescription: this.translate.instant<I18nSourceDefinitionType>(`wiki.space.baselineHint`)
        } as StyxBaselineConfig);
    }

    private initializeReviewConfig() {
        this.reviewConfigService.initialize({ pilotId: this.space?._id, permissions: this.space.permissions });
    }

    openSettingsMenu($event: { activeItem: SpaceInfo; origin: HTMLElement }) {
        this.spaceService.openSpaceSettingPopover($event.activeItem, { origin: $event.origin, viewContainerRef: this.viewContainerRef });
    }

    private getUrlAddonKey(url: string) {
        const addons = getAddons(this.pilotStore.snapshot.detail.addons);
        const addonKeys = addons.map(item => item.key);
        const addonKey = addonKeys.find(item => url.indexOf(item) > -1);
        return addonKey;
    }
}
