import { AfterViewInit, Component, DestroyRef, HostBinding, OnInit, ViewChild, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { PilotEntryStore, TagInfo, UtilService, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { TagAggregationTableComponent } from '@wiki/app/components/tag-aggregation-table/tag-aggregation-table.component';
import { API_PREFIX } from '@wiki/app/constants';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { ThyTableColumnSkeletonType } from 'ngx-tethys/table';

@Component({
    selector: 'space-tags',
    templateUrl: './tags.component.html',
    host: {
        class: 'd-block h-100'
    },
    standalone: false
})
export class SpaceTagsComponent implements OnInit, AfterViewInit {
    @HostBinding(`class`) className = `space-tags`;

    loadingDone = true;

    keywords = '';

    apiPrefix = API_PREFIX;

    spaceIds = [];

    broadObjectType = WikiBroadObjectTypes.page;

    data: PageInfo[] = [];

    skeletonTypes: ThyTableColumnSkeletonType[] = [
        ThyTableColumnSkeletonType.default,
        ThyTableColumnSkeletonType.default,
        ThyTableColumnSkeletonType.member
    ];

    selectedTagIds: string[] = [];

    spaces: SpaceInfo[] = [];

    selectedSpaceItem: SpaceInfo;

    private pilotStore = inject(PilotEntryStore);

    private util = inject(UtilService);

    private activatedRoute = inject(ActivatedRoute);

    private router = inject(Router);

    private destroyRef = inject(DestroyRef);

    @ViewChild('tagTable', { static: false }) tagTable: TagAggregationTableComponent;

    get pilotId() {
        return this.pilotStore.snapshot.detail._id;
    }

    ngOnInit() {
        this.spaceIds = [this.pilotId];
        this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            let tags = params['tags'];
            if (tags) {
                this.selectedTagIds = tags.split(',');
            }
        });
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            this.tagTable.fetchPages();
        });
    }

    selectedTagsChange(tags: TagInfo[]) {
        this.selectedTagIds = tags.map(tag => tag._id);
        this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: { tags: this.selectedTagIds.join(',') },
            queryParamsHandling: 'merge'
        });
    }

    searchKeywords() {
        this.tagTable.onSearch(this.keywords);
    }

    clearSearchText() {
        this.tagTable.onSearch('');
    }

    clickSidebarFooter(e: Event) {
        const url = this.util.generateTeamUrl(`/wiki/tags`);
        window.open(url, '_blank');
    }
}
