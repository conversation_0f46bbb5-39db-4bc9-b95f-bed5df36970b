import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { cache, PilotEntryStore } from '@atinc/ngx-styx';
import { WIKI_SPACE_ROUTER_KEY } from '@wiki/app/constants';
import { getAddons } from '@wiki/app/util/addons';

@Component({
    selector: 'wiki-addon-empty',
    template: '',
    standalone: false
})
export class WikiAddonEmptyComponent implements OnInit {
    private pilotStore = inject(PilotEntryStore);

    constructor(
        private router: Router,
        private route: ActivatedRoute
    ) {}

    ngOnInit() {
        this.goAddon();
    }

    private goAddon() {
        if (!this.pilotStore.snapshot.detail) {
            return;
        } else {
            const idOrIdentifier = this.route.snapshot.params.idOrIdentifier;
            const addons = getAddons(this.pilotStore.snapshot.detail.addons);
            const addonKeys = addons.map(item => item.key);
            const cacheRouteInfo: { key: string; url: string } = cache.get(`${WIKI_SPACE_ROUTER_KEY}${idOrIdentifier}`);

            if (/\/baseline(?!s)/.test(cacheRouteInfo?.url)) {
                cacheRouteInfo.url = cacheRouteInfo.url.split('/baseline')[0] + '/baselines';
            }

            const enabledAddon = addons.filter(item => item.is_enabled)[0]?.key;
            const enabledAddonOrEmptyUrl = enabledAddon
                ? `/wiki/spaces/${idOrIdentifier}/${enabledAddon}`
                : `/wiki/spaces/${idOrIdentifier}`;

            const addonKey = addonKeys.filter(item => item !== 'pages').find(item => cacheRouteInfo?.url?.indexOf(item) > -1);
            if (addonKey && addons.find(addon => addon.key === addonKey).is_enabled) {
                this.router.navigateByUrl(cacheRouteInfo.url, { replaceUrl: true });
            } else {
                this.router.navigateByUrl(enabledAddonOrEmptyUrl, {
                    replaceUrl: true
                });
            }
        }
    }
}
