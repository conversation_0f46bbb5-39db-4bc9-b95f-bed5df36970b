<thy-layout>
  <thy-sidebar thyTheme="light">
    <thy-sidebar-header>
      <ng-template #headerTitle>
        <a href="javascript:;" class="link-muted" (click)="goBack()" styxI18nTracking>
          <thy-icon thyIconName="arrow-left" class="mr-2"></thy-icon> {{ 'common.back' | translate }}
        </a>
      </ng-template>
    </thy-sidebar-header>
    <thy-sidebar-content>
      <thy-menu thyTheme="loose">
        <thy-divider></thy-divider>
        <thy-menu-group styxI18nTracking [thyTitle]="'styx.general' | translate">
          <a thyMenuItem href="javascript:;" [routerLink]="['spaces']" routerLinkActive="active">
            <span thyMenuItemName translate="wiki.configuration.manage.title"></span>
          </a>
          <a thyMenuItem href="javascript:;" [routerLink]="['tags']" routerLinkActive="active">
            <span thyMenuItemName translate="styx.tagManage"></span>
          </a>
          <a thyMenuItem href="javascript:;" [routerLink]="['categories']" routerLinkActive="active">
            <span thyMenuItemName translate="styx.categoryManage"></span>
          </a>
        </thy-menu-group>
        <thy-divider></thy-divider>
        <thy-menu-group styxI18nTracking [thyTitle]="'styx.page' | translate: { isTitle: true, isPlural: false }">
          <a thyMenuItem href="javascript:;" [routerLink]="['stencils']" routerLinkActive="active">
            <span thyMenuItemName translate="wiki.space.stencils"></span>
          </a>
          <a thyMenuItem href="javascript:;" [routerLink]="['sharing']" routerLinkActive="active">
            <span thyMenuItemName translate="wiki.page.shared.title"></span>
          </a>
        </thy-menu-group>
        <thy-divider></thy-divider>
        <thy-menu-group styxI18nTracking [thyTitle]="'styx.config' | translate">
          <a thyMenuItem href="javascript:;" [routerLink]="['space']" routerLinkActive="active">
            <span thyMenuItemName translate="wiki.configuration.title"></span>
          </a>
          <a thyMenuItem href="javascript:;" [routerLink]="['review']" routerLinkActive="active">
            <span thyMenuItemName translate="styx.reviewConfig"></span>
          </a>
        </thy-menu-group>
        <thy-divider></thy-divider>
        <thy-menu-group styxI18nTracking [thyTitle]="'styx.permission' | translate: { isTitle: true, isPlural: false }">
          <a thyMenuItem href="javascript:;" [routerLink]="['security']" routerLinkActive="active">
            <span thyMenuItemName translate="styx.permissionConfig"></span>
          </a>
        </thy-menu-group>
      </thy-menu>
    </thy-sidebar-content>
  </thy-sidebar>
  <thy-layout>
    <router-outlet></router-outlet>
  </thy-layout>
</thy-layout>
