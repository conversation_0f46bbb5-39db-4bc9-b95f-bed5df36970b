import { Component, inject } from '@angular/core';
import { providePilotEntryStore, StyxTranslateService, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { StyxAddonColumnsComponent, StyxAddonPermissionComponent } from '@atinc/ngx-styx/addon';
import { API_PREFIX } from '@wiki/app/constants';

@Component({
    selector: 'wiki-configuration-settings-space-config',
    templateUrl: './space.component.html',
    standalone: false
})
export class ConfigurationSpaceComponent {
    private translate = inject(StyxTranslateService);
    public get apiPrefix() {
        return `${API_PREFIX}/configuration/addon`;
    }

    addonSettingsConfigs = {
        columns: {
            title: this.translate.instant<I18nSourceDefinitionType>('styx.viewManagement'),
            component: StyxAddonColumnsComponent,
            lockMax: 3,
            unremovableColumns: ['name']
        },
        permission: {
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.configuration.space.componentPermission'),
            component: StyxAddonPermissionComponent
        }
    };

    addonDefinitions = {
        pages: {
            key: 'pages',
            settings: ['permission']
        },
        baseline: {
            key: 'baseline',
            settings: ['columns', 'permission']
        },
        reviews: {
            key: 'reviews',
            settings: ['permission']
        }
    };
}
