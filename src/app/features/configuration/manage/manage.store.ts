import { inject, Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { EntityStore, EntityState, Action } from '@tethys/store';
import { PaginationResponseData, helpers, GlobalUsersStore, PilotEntryStore } from '@atinc/ngx-styx';
import { API_PREFIX } from '@wiki/app/constants';
import { SpaceInfo } from '@wiki/app/entities/space-info';

export interface SpacesState extends EntityState<SpaceInfo> {}

@Injectable()
export class ConfigurationSpaceStore extends EntityStore<SpacesState, SpaceInfo> {
    private pilotStore = inject(PilotEntryStore);

    constructor(
        private http: HttpClient,
        private userStore: GlobalUsersStore
    ) {
        super();
    }

    @Action()
    fetchSpaces(type?: number, keyWords?: string, pageIndex?: number): Observable<PaginationResponseData<SpaceInfo[]>> {
        const params = { type, pi: pageIndex, keywords: keyWords };
        return this.http.get(`${API_PREFIX}/configuration/spaces${helpers.buildUrlParams(params)}`).pipe(
            tap((data: PaginationResponseData<SpaceInfo[]>) => {
                this.initialize(data.value, {
                    pageIndex: data.page_index,
                    pageSize: data.page_size,
                    pageCount: data.page_count,
                    count: data.count
                });

                this.userStore.initializeUsers(data.references.members);
            })
        );
    }

    @Action()
    deleteSpace(spaceId: string) {
        return this.http.delete(`${API_PREFIX}/configuration/space/${spaceId}`).pipe(
            tap(() => {
                this.remove(spaceId);
            })
        );
    }

    @Action()
    archiveSpace(spaceId: string) {
        return this.http.put(`${API_PREFIX}/configuration/space/${spaceId}/archive`, {}).pipe(
            tap(() => {
                this.remove(spaceId);
                if (this.pilotStore.getEntry(spaceId)) {
                    this.pilotStore.removeEntryAction(spaceId);
                }
            })
        );
    }

    @Action()
    activateSpace(space: SpaceInfo) {
        return this.http.put(`${API_PREFIX}/configuration/space/${space._id}/activate`, {}).pipe(
            tap(() => {
                this.remove(space._id);
                this.pilotStore.addEntryAction(space);
            })
        );
    }

    @Action()
    restoreSpace(space: SpaceInfo) {
        return this.http.put(`${API_PREFIX}/configuration/space/${space._id}/restore`, {}).pipe(
            tap(() => {
                this.remove(space._id);
                this.pilotStore.addEntryAction(space);
            })
        );
    }

    @Action()
    setAdmins(spaceId: string, uids: string[]) {
        return this.http.post(`${API_PREFIX}/spaces/${spaceId}/admins`, {
            uids
        });
    }
}
