import { Component, HostBinding, inject } from '@angular/core';
import { Router } from '@angular/router';
import { PilotEntryStore, StyxPermissionService, css } from '@atinc/ngx-styx';

@Component({
    selector: 'wiki-configuration',
    templateUrl: './configuration.component.html',
    host: {
        class: 'configuration-space-manage'
    },
    standalone: false
})
export class ConfigurationComponent {
    @HostBinding(`class`) className = `${css.layout}`;

    private permissionService = inject(StyxPermissionService);

    private router = inject(Router);

    private pilotStore = inject(PilotEntryStore);

    get reviewManagePermission() {
        return this.permissionService.hasPermission(this.pilotStore.snapshot.detail?.permissions, 'review_manage');
    }

    goBack() {
        this.router.navigate(['/admin/product']);
    }
}
