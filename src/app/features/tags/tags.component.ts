import { Component, DestroyRef, HostB<PERSON>ing, OnInit, ViewChild, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute, Router } from '@angular/router';
import { PilotEntryStore, ResponseData, TagInfo, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { TagAggregationTableComponent } from '@wiki/app/components/tag-aggregation-table/tag-aggregation-table.component';
import { API_PREFIX } from '@wiki/app/constants';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { wikiPermissionDefinition } from '@wiki/common/constants/permission';
import { ThyTableColumnSkeletonType } from 'ngx-tethys/table';

@Component({
    selector: 'wiki-tags',
    templateUrl: './tags.component.html',
    host: {
        class: 'd-block'
    },
    standalone: false
})
export class WikiTagsComponent implements OnInit {
    @HostBinding(`class`) className = `wiki-tag`;

    loadingDone = true;

    keywords = '';

    apiPrefix = API_PREFIX;

    broadObjectType = WikiBroadObjectTypes.page;

    spaceIds = [];

    data: PageInfo[] = [];

    skeletonTypes: ThyTableColumnSkeletonType[] = [
        ThyTableColumnSkeletonType.default,
        ThyTableColumnSkeletonType.default,
        ThyTableColumnSkeletonType.member
    ];

    selectedTagIds: string[] = [];

    spaces: SpaceInfo[] = [];

    selectedSpaceItem: SpaceInfo;

    private activatedRoute = inject(ActivatedRoute);

    private router = inject(Router);

    private destroyRef = inject(DestroyRef);

    private pilotStore = inject(PilotEntryStore);

    @ViewChild('tagTable', { static: false }) tagTable: TagAggregationTableComponent;

    ngOnInit() {
        this.watchRouteParams();
        this.fetchSpaces();
    }

    watchRouteParams() {
        this.activatedRoute.queryParams.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(params => {
            if (params?.tags) {
                this.selectedTagIds = params.tags.split(',');
            }
        });
    }

    fetchSpaces() {
        return this.pilotStore
            .fetchSelectableAuthorizedEntries({
                permission_keys: [wikiPermissionDefinition.page_create.key]
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: ResponseData<SpaceInfo[]>) => {
                this.spaces = data.value;
            });
    }

    selectedTagsChange(tags: TagInfo[]) {
        this.selectedTagIds = tags.map(tag => tag._id);
        this.router.navigate([], {
            relativeTo: this.activatedRoute,
            queryParams: { tags: this.selectedTagIds.join(',') },
            queryParamsHandling: 'merge'
        });
    }

    changeFilter(infos: SpaceInfo[]) {
        this.spaceIds = infos.map(info => info._id);
    }

    searchKeywords() {
        this.tagTable.onSearch(this.keywords);
    }

    clearSearchText() {
        this.tagTable.onSearch('');
    }
}
