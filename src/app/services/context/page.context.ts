import { Injectable, Optional, ViewContainerRef } from '@angular/core';
import { AppRootContext, AttachmentEntity, GlobalUsersStore, Id, ResponseData, UtilService, helpers } from '@atinc/ngx-styx';
import { Discussion } from '@wiki/common/custom-types';
import { AttachmentScope, TheExtensionMode } from '@wiki/common/interface';
import { WikiCommonEditorAttachmentComponent } from '@wiki/common/plugins/attachment';
import { RelationWorkItemInfo, RelationWorkItems } from '@wiki/common/plugins/relation-work-item';
import { AttachmentApiService, AttachmentContext } from '@wiki/common/services';
import { PageStore } from '@wiki/common/stores/page.store';
import { RelationOpenType } from '@wiki/common/types';
import { ThyDialogSizes } from 'ngx-tethys/dialog';
import { Observable, of } from 'rxjs';
import { filter, map, scan } from 'rxjs/operators';
import { Range } from 'slate';
import { WikiAttachmentsDialogComponent, WikiDiscussionListComponent } from '../../components';
import { DiscussionInfo, PageExtensionData, PageExtensionInfo, PageExtensionType, PageInfo, SpaceInfo } from '../../entities';
import { PagesStore } from '../../stores/pages.store';
import { getDomainUrl } from '../../util/common';
import { PageApiService } from '../page-api.service';
import { PageService } from '../util/page.service';
import { SpaceService } from '../util/space.service';
import { WorkItemApiService } from '../work-item-api.service';
import { BasePluginContext } from './base-plugin.context';

@Injectable()
export class PageContext extends BasePluginContext implements AttachmentContext {
    readonly: boolean;

    workItemReferences: RelationWorkItems;

    // key 是 pageId
    workItemListIdsMap = new Map<string, RelationWorkItemInfo[]>();

    pagePermission: string;

    get permissions() {
        return this.pageStore.snapshot.page?.permissions;
    }

    constructor(
        @Optional()
        public pageApiService: PageApiService,
        public workItemApiService: WorkItemApiService,
        public spaceService: SpaceService,
        public pageService: PageService,
        private pageStore: PageStore,
        private pagesStore: PagesStore,
        private util: UtilService,
        public viewContainerRef: ViewContainerRef,
        public globalUsersStore: GlobalUsersStore,
        public appRootContext: AppRootContext,
        public attachmentApiService: AttachmentApiService
    ) {
        super();
    }

    attachmentChange(attachmentId: string) {
        return this.pageStore.select$(state => state.page?.attachments?.find(attachment => attachment._id === attachmentId));
    }

    getPageId(): string {
        return this.pageStore.snapshot.page._id;
    }

    getSpaceId(): string {
        return this.pageStore.snapshot.page.space_id;
    }

    getPages() {
        return this.pagesStore.snapshot.entities;
    }

    fetchPageExtension(
        pageId: string,
        extensionId: string
    ): Observable<
        ResponseData<
            PageExtensionInfo,
            {
                pages?: PageInfo[];
                space?: SpaceInfo;
            }
        >
    > {
        return this.pageApiService.fetchPageExtension(pageId, extensionId);
    }

    savePageExtension(
        pageId: string,
        value: { key: string; data: PageExtensionData; type: PageExtensionType },
        mode?: TheExtensionMode
    ): Observable<any> {
        return this.pageApiService.savePageExtension(pageId, value, mode);
    }

    initializePageStore(pageStore: PageStore) {
        this.pageStore = pageStore;
    }

    attachmentOriginId() {
        return this.pageStore.snapshot.page._id;
    }

    getAttachmentComponent() {
        return WikiCommonEditorAttachmentComponent;
    }

    openAttachmentList(editor) {
        this.util.dialog.open(WikiAttachmentsDialogComponent, {
            initialState: {
                editor,
                pageStatus: 'edit'
            },
            size: ThyDialogSizes.maxLg,
            viewContainerRef: this.viewContainerRef,
            restoreFocus: false
        });
    }

    getPageAttachments() {
        const pageId = this.pageStore.snapshot.page._id;
        return this.attachmentApiService.fetchAttachments(pageId).pipe(
            map((data: ResponseData<any[]>) => {
                return data.value;
            })
        );
    }

    addAttachment(attachment: AttachmentEntity, isPure = false, attachmentScope?: AttachmentScope): Observable<AttachmentEntity> {
        // pure insert store
        if (isPure) {
            this.pageStore.pureAddAttachment(attachment);
            return of(null);
        }
        return this.pageStore.addAttachment(this.pageStore.snapshot.page._id, attachment, attachmentScope);
    }

    getAttachmentById(_id: Id): AttachmentEntity {
        return helpers.find(this.pageStore.snapshot.page.attachments, { _id });
    }

    getAttachments(): AttachmentEntity[] {
        return this.pageStore.snapshot.page.attachments;
    }

    modifyAttachmentName(id: Id, title: string): Observable<any> {
        return this.pageStore.modifyAttachmentName(this.pageStore.snapshot.page._id, id, title);
    }

    getDiscussions() {
        if (!this.pageStore) {
            return of([]);
        }
        return this.pageStore.getOngoingDiscussions$().pipe(
            map((discussions: DiscussionInfo[]) => {
                if (this.pageStore.snapshot.isFullScreen) {
                    return [];
                } else {
                    return discussions;
                }
            }),
            map((discussions: DiscussionInfo[]) => {
                const retDiscussions = [];
                discussions.forEach((item: DiscussionInfo) => {
                    const isExist = retDiscussions.some(
                        (ele: DiscussionInfo) =>
                            item.lowest_node_key === ele.lowest_node_key &&
                            item.node_key === ele.node_key &&
                            Range.equals(ele.range, item.range)
                    );
                    if (!isExist) {
                        retDiscussions.push(item);
                    }
                });
                return retDiscussions;
            }),
            scan<DiscussionInfo[], { prev: DiscussionInfo[]; current: DiscussionInfo[] }>(
                (acc, current) => {
                    return { prev: acc.current, current };
                },
                { prev: [], current: [] }
            ),
            filter(discussions => {
                return (
                    discussions.prev.length !== discussions.current.length ||
                    discussions.prev.some((item, index) => {
                        return (
                            item.lowest_node_key !== discussions.current[index].lowest_node_key &&
                            item.node_key !== discussions.current[index].node_key &&
                            Range.equals(item.range, discussions.current[index].range)
                        );
                    })
                );
            }),
            map(discussions => {
                return discussions.current;
            })
        );
    }

    openDiscussionList(target: HTMLElement, discussionData: Discussion) {
        this.pageService.openDiscussionPopover(
            WikiDiscussionListComponent,
            {
                target,
                showDiscussionList: true,
                pageId: this.pageStore.snapshot.page._id,
                discussionOrigin: discussionData
            },
            this.viewContainerRef
        );
    }

    getMentionReferenceData() {
        if (this.pageStore && this.pageStore.snapshot.members) {
            return this.pageStore.snapshot.members;
        } else {
            return this.globalUsersStore.snapshot.teamUsers;
        }
    }

    getMentionData() {
        return this.globalUsersStore.snapshot.teamUsers;
    }

    getSearchedData(searchText: string) {
        if (searchText) {
            return this.getMentionData().filter(item => {
                return (
                    item.name.toLowerCase().includes(searchText.toLowerCase()) ||
                    item.display_name.includes(searchText) ||
                    (item.display_name_pinyin || '').toLowerCase().includes(searchText.toLowerCase())
                );
            });
        } else {
            const spaceMembers = this.pageStore.snapshot.spaceMembers || [];
            return spaceMembers.length > 0 ? spaceMembers.slice(0, 20) : this.globalUsersStore.snapshot.teamUsers.slice(0, 20);
        }
    }

    // workItem
    fetchRecentWorkItems(): Observable<any[]> {
        return this.workItemApiService.fetchRecentBrowsedWorkItems().pipe(
            map((data: ResponseData<any[]>) => {
                return data.value;
            })
        );
    }

    searchWorkItems(keywords: string): Observable<RelationWorkItemInfo[]> {
        return this.workItemApiService.fetchSearchWorkItems(keywords).pipe(
            map((data: ResponseData<any[]>) => {
                return data.value;
            })
        );
    }

    getWorkItemRoute(workItemId: string) {
        const domain = getDomainUrl(this.appRootContext.globalInfo.config.baseUrlFormat, this.appRootContext.team.domain);
        return {
            url: `${domain}/agile/items/${workItemId}`,
            open_type: RelationOpenType.SELF
        };
    }

    selectWorkItem(selectedWorkItemIds: string[], handle: (workItemInfo: RelationWorkItems) => void) {}

    getPagePermissions() {
        return this.pageStore.snapshot.page.permissions;
    }
}
