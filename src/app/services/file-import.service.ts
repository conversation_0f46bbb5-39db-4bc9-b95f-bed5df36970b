import { Injectable, OnDestroy, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
    DriveFileIconUrlPipe,
    DriveTypes,
    FileSizeDisplayPipe,
    PilotEntryStore,
    ProcessRef,
    StyxFeedReceiver,
    StyxFileProcessManager,
    StyxTranslateService,
    UtilService
} from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { ThyUploadFileProgress, ThyUploadStatus } from 'ngx-tethys/upload';
import { Subject } from 'rxjs';
import { concatMap, map, takeUntil, tap } from 'rxjs/operators';
import { FeedEventKeys } from '../constants/feed';
import { PageTreeQueryScene, WIKI_SPACE_TYPE } from '../constants/page';
import { ImportFeedData, ImportPageErrorStatus, UploadPageOption } from '../entities/file-import';
import { PageInfo } from '../entities/page-info';
import { PagesStore } from '../stores/pages.store';
import { SpaceImportPageService } from './import-page.service';

@Injectable()
export class FileImportService implements OnDestroy {
    private translate = inject(StyxTranslateService);

    private isOpenDialog = false;

    private updatePageTree$ = new Subject<void>();

    get spaceId() {
        return this.pilotStore.snapshot.detail._id;
    }

    get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    get scopeId() {
        return `wiki-page-${this.homePage?._id}`;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private spaceImportPageService: SpaceImportPageService,
        private pagesStore: PagesStore,
        private util: UtilService,
        private router: Router,
        private feedReceiver: StyxFeedReceiver,
        private fileSizeDisplayPipe: FileSizeDisplayPipe,
        public driveFileIconUrlPipe: DriveFileIconUrlPipe,
        public fileProcessManager: StyxFileProcessManager
    ) {}

    ngOnDestroy(): void {
        this.updatePageTree$?.complete();
    }

    public initialize() {
        this.registerUpdatePageTreeSubscribe();
    }

    public uploadPageFile(
        spaceIdentifier: string,
        parentId: string,
        files: File[],
        uploadPageInfo: UploadPageOption,
        selectImportPage: (pageId: string, page: PageInfo[]) => void,
        afterId?: string | null
    ) {
        let completedCount = 0;
        let completedTotal = 0;
        let firstCompletedId;

        for (const file of files) {
            if (this.maxFileSize(file)) {
                const cancelUpload$ = new Subject<boolean>();
                let processRef: ProcessRef;
                let ext = uploadPageInfo.suffix[0].slice(1);
                uploadPageInfo.suffix.forEach(suffixItem => {
                    if (file.name.includes(suffixItem)) {
                        ext = suffixItem.slice(1);
                    }
                });
                this.spaceImportPageService
                    .importPages(this.spaceId, this.scopeId, uploadPageInfo.type, file, parentId || '', afterId)
                    .pipe(takeUntil(cancelUpload$))
                    .subscribe({
                        next: uploadResponse => {
                            if (uploadResponse.status === ThyUploadStatus.started) {
                                processRef = this.fileProcessManager.addProcess(
                                    {
                                        name: uploadResponse.uploadFile.fileName,
                                        icon: this.driveFileIconUrlPipe.transform({
                                            type: DriveTypes.file,
                                            addition: {
                                                ext
                                            }
                                        })
                                    },
                                    {
                                        cancelTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.import.cancel'),
                                        preview: (url: string) => {
                                            if (url) {
                                                this.previewToUpdatePageTree(url);
                                                this.router.navigateByUrl(url);
                                            }
                                        }
                                    }
                                );
                            }
                            // 更新进程管理器进度条
                            processRef?.setProgress(this.setProgress(uploadResponse.uploadFile.progress));

                            // 取消上传
                            processRef?.cancel$.pipe(takeUntil(cancelUpload$)).subscribe(() => {
                                if (uploadResponse.status !== ThyUploadStatus.done && cancelUpload$) {
                                    completedCount += 1;
                                    this.destroy(cancelUpload$);
                                }
                            });

                            if (uploadResponse.status === ThyUploadStatus.done) {
                                const jobId = uploadResponse.uploadFile.response.oid;
                                this.registerFeedEvents(jobId, processRef, (feedData: ImportFeedData, isImportEmpty: boolean) => {
                                    // 更新已处理的文件数量
                                    completedCount += 1;

                                    if (!feedData.importResult.error && !isImportEmpty) {
                                        const previewUrl = this.getPreviewUrl(spaceIdentifier, feedData.pageId);

                                        // 设置进程管理器预览地址
                                        processRef.complete(previewUrl);

                                        // 更新导入成功总数量
                                        completedTotal += feedData.importResult.succeeded;

                                        // 设置第一个导入成功的页面id
                                        if (!firstCompletedId) {
                                            const idOrIds = feedData.importResult.short_id ?? feedData.pageId;
                                            firstCompletedId = Array.isArray(idOrIds) ? idOrIds[0] : idOrIds;
                                        }
                                    }

                                    const currentIdentifier = this.pilotStore.snapshot.detail.identifier;
                                    if (spaceIdentifier === currentIdentifier && completedCount === files.length) {
                                        // 每个批次完成时更新页面树
                                        this.updatePageTree$.next();

                                        // 处理完成弹出提示
                                        if (!this.isOpenDialog && completedTotal > 0) {
                                            this.completeConfirm(spaceIdentifier, completedTotal, firstCompletedId, selectImportPage);
                                        }
                                    }
                                });
                            }
                        },
                        error: error => {
                            this.util.defaultErrorHandler()(error);
                        }
                    });
            } else {
                this.util.notify.show({
                    type: 'warning',
                    title: this.translate.instant<I18nSourceDefinitionType>('common.tip'),
                    content: '文件大小不能超过 1 G',
                    duration: 3000
                });
            }
        }
    }

    private previewToUpdatePageTree(url: string) {
        const urlTree = this.router.parseUrl(url);
        const segments = urlTree.root.children['primary'].segments;
        const lastSegment = segments[segments.length - 1].path;
        const pages = this.pagesStore.snapshot.entities;
        const hasPage = pages.some((page: PageInfo) => page._id === lastSegment);

        if (!hasPage) {
            this.updatePageTree$.next();
        }
    }

    private setProgress(progress: ThyUploadFileProgress) {
        if (progress && progress.percentage > 0) {
            return progress.percentage - (progress.percentage === 100 ? 1 : 0);
        } else {
            return 0;
        }
    }

    private maxFileSize(file: File) {
        const maxSize = 1024 * 1024 * 1024;
        return file.size < maxSize;
    }

    private registerFeedEvents(
        jobId: number,
        processRef: ProcessRef,
        completedCallback: (feedData: ImportFeedData, isImportEmpty: boolean) => void
    ) {
        let destroy$ = new Subject<boolean>();
        this.feedReceiver
            .received()
            .pipe(takeUntil(destroy$))
            .subscribe(feed => {
                if (feed.type === WIKI_SPACE_TYPE && feed.event_key === FeedEventKeys.finishImport) {
                    const feedFileName = feed.data.fileName + jobId;
                    const processFileName = processRef.customEntity.name + jobId;
                    if (feedFileName !== processFileName) {
                        return;
                    }
                    const data = feed.data as ImportFeedData;
                    if (data?.importResult) {
                        const isImportEmpty = data.importResult?.succeeded === 0 && data.importResult?.total === 0;

                        // 导入失败处理
                        if (data.importResult.error || isImportEmpty) {
                            processRef.error(this.translate.instant<I18nSourceDefinitionType>('wiki.import.failed'));
                        }
                        if (data.importResult.error) {
                            this.errorNotifyError(data.importResult.error, data.importResult?.expect_value);
                        }

                        completedCallback(data, isImportEmpty);
                        this.destroy(destroy$);
                    }
                }
            });
    }

    private getPreviewUrl(spaceIdentifier: string, pageId: string) {
        const urlTree = this.router.createUrlTree(['/wiki/spaces', spaceIdentifier, 'pages', pageId]);
        return this.router.serializeUrl(urlTree);
    }

    private registerUpdatePageTreeSubscribe() {
        this.updatePageTree$
            .pipe(concatMap(() => this.pagesStore.fetchPages(this.spaceId, { scene: PageTreeQueryScene.simple })))
            .subscribe();
    }

    private completeConfirm(
        spaceIdentifier: string,
        completedCount: number,
        firstPageId: string,
        selectImportPage: (pageId: string, page: PageInfo[]) => void
    ) {
        this.isOpenDialog = true;
        const completeConfirmRef = this.util.dialog.confirm({
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.import.complete'),
            content: this.translate.instant<I18nSourceDefinitionType>('wiki.page.import.success', { count: completedCount }),
            footerAlign: 'right',
            okType: 'primary',
            okText: this.translate.instant<I18nSourceDefinitionType>('common.lookAt'),
            cancelText: this.translate.instant<I18nSourceDefinitionType>('common.cancel'),
            onOk: () => {
                return this.pagesStore.fetchPages(this.spaceId, { scene: PageTreeQueryScene.simple }).pipe(
                    tap(data => {
                        this.router.navigate(['/wiki/spaces', spaceIdentifier, 'pages', firstPageId]);
                        selectImportPage(firstPageId, data);
                    }),
                    map(() => true)
                );
            }
        });
        completeConfirmRef?.afterClosed().subscribe(() => {
            this.isOpenDialog = false;
        });
    }

    private errorNotifyError(errorCode: number, expectValue?: number) {
        let errorContent;
        switch (errorCode) {
            case ImportPageErrorStatus.FILE_NOT_FOUND:
                errorContent = this.translate.instant<I18nSourceDefinitionType>('wiki.import.fileNotExist');
                break;
            case ImportPageErrorStatus.FILE_FORMAT_FAULT:
                errorContent = this.translate.instant<I18nSourceDefinitionType>('wiki.import.invalidFormat');
                break;
            case ImportPageErrorStatus.IMPORT_FILE_ATTACHMENT_SIZE_LIMIT:
                const size = this.fileSizeDisplayPipe.transform(expectValue);
                errorContent = this.translate.instant<I18nSourceDefinitionType>('wiki.page.import.attachmentSizeLimit', { size });
                break;
            default:
                errorContent = this.translate.instant<I18nSourceDefinitionType>('wiki.error.unknown');
                break;
        }
        this.util.notify.show({
            type: 'error',
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.import.failed'),
            content: errorContent,
            duration: 3000
        });
    }

    private destroy(destroy$: Subject<Boolean>) {
        destroy$.next(true);
        destroy$.unsubscribe();
    }
}
