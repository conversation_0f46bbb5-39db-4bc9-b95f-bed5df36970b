import { Injectable } from '@angular/core';
import { ThyUploadService } from 'ngx-tethys/upload';
import { API_PREFIX } from '../constants';

@Injectable({
    providedIn: 'root'
})
export class SpaceImportPageService {
    constructor(private thyUploaderService: ThyUploadService) {}

    importPages(spaceId: string, scopeId: string, type: string, file: File, parentId: string, afterId?: string | null) {
        const data: Record<string, any> = {
            parent_id: parentId
        };
        if (afterId) {
            data.after_id = afterId;
            data.append_when_nil_after_id = true;
        } else if (afterId === null) {
            data.after_id = null;
            data.append_when_nil_after_id = false;
        }
        return this.thyUploaderService.upload({
            nativeFile: file,
            fileField: 'uploadFile',
            url: `${API_PREFIX}/importer/spaces/${spaceId}/import-pages/${type}`,
            method: 'POST',
            fileName: file.name,
            identifier: `${scopeId}_${Math.floor(Math.random() * 1000000)}`,
            data
        });
    }
}
