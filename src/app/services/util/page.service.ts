import { ConnectedPosition, Overlay, OverlayRef } from '@angular/cdk/overlay';
import { ComponentType } from '@angular/cdk/portal';
import { Inject, Injectable, TemplateRef, ViewContainerRef } from '@angular/core';
import { StyxTranslateService } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '../../constants/i18n-source';
import { Router } from '@angular/router';
import {
    StyxConfirmService,
    StyxPricingService,
    UtilService,
    WikiBroadObjectTypes,
    hasPermissionTransform,
    helpers
} from '@atinc/ngx-styx';
import { PAGE_COPY_COMPONENT_TOKEN } from '@wiki/app/components/copy/copy.component.token';
import { PAGE_MOVE_COMPONENT_TOKEN } from '@wiki/app/components/move/move.component.token';
import { PAGE_DIALOG_COMPONENT_TOKEN } from '@wiki/app/components/page-dialog/page-dialog.component.token';
import { PAGE_EDIT_COMPONENT_TOKEN } from '@wiki/app/components/page-edit/edit/edit.component.token';
import { PageSharingComponent } from '@wiki/app/components/page-sharing/page-sharing.component';
import { WikiRelationPageSelectionComponent } from '@wiki/app/components/relation-page-selection/relation-page-selection.component';
import { SelectPageComponent } from '@wiki/app/components/select-page/select-page.component';
import { StencilDetailComponent } from '@wiki/app/components/stencil/detail/stencil-detail.component';
import { PAGE_STENCIL_CONTENT_DETAIL_COMPONENT } from '@wiki/app/components/stencil/detail/stencil-detail.component.token';
import { PAGE_STENCIL_CONTENT_EDIT_COMPONENT } from '@wiki/app/components/stencil/edit/stencil-edit.component.token';
import { EditingPageFrom, PageTypes } from '@wiki/app/constants/page';
import { PageDialogOptions } from '@wiki/app/entities/page-dialot';
import { DiscussionInfo } from '@wiki/app/entities/page-discussion-info';
import { PageExtensionItemInfo } from '@wiki/app/entities/page-extendsion';
import { CopyPageOptions, DraftInfo, MovePageOptions, PageInfo, PageSelectionOption } from '@wiki/app/entities/page-info';
import { PageSharingPopoverOptions } from '@wiki/app/entities/page-sharing';
import { PAGE_INFO_COMPONENT_TOKEN } from '@wiki/app/features/page/components/page-info/page-info.component.token';
import { EditingPageResult } from '@wiki/app/info/editing-page';
import { PageDraftStore } from '@wiki/app/stores/draft-page.store';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { convertToBroadObject } from '@wiki/app/util/common';
import { wikiPermissionDefinition } from '@wiki/common/constants/permission';
import { RelationApiService } from '@wiki/common/services/relation-api.service';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { getDefaultPageTitle } from '@wiki/common/util/get-default-page-title';
import { getPreviousNodeByKey } from '@wiki/common/util/tree';
import { updatePopoverPosition } from '@worktile/theia';
import { ScrollToService, ThyPlacement } from 'ngx-tethys/core';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThySlideService } from 'ngx-tethys/slide';
import { of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { Element } from 'slate';
import { WikiErrorService } from '../error.service';
import { PageApiService } from '../page-api.service';
import { PageTreeService } from '../page-tree.service';
import { StencilEditOptions } from '@wiki/app/entities/stencil-info';
import scrollIntoView from 'scroll-into-view-if-needed';

@Injectable({ providedIn: 'root' })
export class PageService {
    constructor(
        private thyDialog: ThyDialog,
        private util: UtilService,
        private pagesStore: PagesStore, // Store 数据源
        private router: Router,
        private confirm: StyxConfirmService,
        private translate: StyxTranslateService,
        @Inject(PAGE_DIALOG_COMPONENT_TOKEN)
        private pageDialogComponent: ComponentType<any>,
        @Inject(PAGE_EDIT_COMPONENT_TOKEN)
        private pageEditComponent: any,
        @Inject(PAGE_STENCIL_CONTENT_EDIT_COMPONENT)
        private pageStencilEditComponent: any,
        @Inject(PAGE_STENCIL_CONTENT_DETAIL_COMPONENT)
        private pageStencilDetailComponent: TemplateRef<StencilDetailComponent>,
        @Inject(PAGE_MOVE_COMPONENT_TOKEN)
        private pageMoveComponent: any,
        @Inject(PAGE_COPY_COMPONENT_TOKEN)
        private pageCopyComponent: any,
        @Inject(PAGE_INFO_COMPONENT_TOKEN)
        private pageInfoComponent: any,
        private relationApiService: RelationApiService,
        private pageDraftStore: PageDraftStore,
        private errorService: WikiErrorService,
        private pricingService: StyxPricingService,
        public pageTreeService: PageTreeService,
        private overlay: Overlay,
        private editingPageStore: CommonEditingPageStore,
        private pageApiService: PageApiService,
        private thySlideService: ThySlideService
    ) {}

    openPageSelection(option: PageSelectionOption) {
        const { confirmHandle, ...arg } = option;
        const dialogRef = this.thyDialog.open(WikiRelationPageSelectionComponent, {
            initialState: {
                ...arg
            },
            size: ThyDialogSizes.superLg
        });

        if (dialogRef?.componentInstance) {
            dialogRef.componentInstance.selectionConfirm.subscribe((selectPage: PageInfo[]) => confirmHandle(selectPage));
        }
    }

    openPageInfo(drawerContainer: HTMLElement, page: PageInfo, node: Element) {
        const pageInfoSlide = this.thySlideService.open(this.pageInfoComponent, {
            initialState: {
                page,
                node,
                close: () => {
                    pageInfoSlide.close();
                }
            },
            drawerContainer: drawerContainer,
            hasBackdrop: true,
            backdropClosable: true,
            width: '360px',
            mode: 'over',
            from: 'right'
        });
    }

    openWikiPageDialog(options: PageDialogOptions) {
        const { viewContainerRef, ...arg } = options;
        return this.util.dialog.open(this.pageDialogComponent, {
            initialState: {
                ...arg
            },
            size: ThyDialogSizes.superLg,
            viewContainerRef
        });
    }

    openDiscussionPopover(
        discussionComponent: ComponentType<unknown>,
        initialState: {
            target: HTMLElement;
            contentContainer?: string;
            showDiscussionList?: Boolean;
            pageId?: string;
            discussionOrigin?: DiscussionInfo;
            isSolved?: boolean;
            resolvedAt?: number | string;
            isAlwaysRight?: boolean;
            isScroll?: boolean;
        },
        viewContainerRef?: ViewContainerRef,
        options: {
            offset?: number;
            placement?: ThyPlacement;
            panelClass?: string;
        } = {}
    ) {
        const { target, isAlwaysRight, isScroll, contentContainer } = initialState;
        const containerElement = contentContainer && document.getElementsByClassName(contentContainer)[0];

        if (target?.tagName === 'THY-ICON') {
            options.placement = window.innerWidth - target.getBoundingClientRect().right < 320 ? 'leftTop' : 'rightTop';
        }

        if (isAlwaysRight && containerElement) {
            containerElement.scrollLeft = 710;
        }

        if (isScroll && containerElement) {
            scrollIntoView(target, {
                scrollMode: 'if-needed'
            });
        }

        const { placement, offset, panelClass } = options ?? {};
        const popoverRef = this.util.popover.open(discussionComponent, {
            origin: target,
            panelClass: panelClass || 'start-discussion',
            hasBackdrop: false,
            outsideClosable: false,
            insideClosable: false,
            manualClosure: true,
            initialState,
            viewContainerRef,
            autoAdaptive: true,
            scrollStrategy: this.overlay.scrollStrategies.reposition()
        });

        const overlayRef = popoverRef?.getOverlayRef();
        this.updatePositionStrategy(overlayRef, target, placement, offset);

        return popoverRef;
    }

    updatePositionStrategy(overlayRef: OverlayRef, origin: HTMLElement, placement: ThyPlacement = null, offset = 12) {
        const bottomPosition: ConnectedPosition = {
            originX: 'center',
            originY: 'bottom',
            overlayX: 'center',
            overlayY: 'top',
            offsetY: offset
        };
        const rightTopPosition: ConnectedPosition = {
            originX: 'end',
            originY: 'top',
            overlayX: 'start',
            overlayY: 'top',
            offsetX: offset
        };
        const rightBottomPosition: ConnectedPosition = {
            ...rightTopPosition,
            overlayY: 'bottom',
            offsetY: offset
        };
        const leftTopPosition: ConnectedPosition = {
            ...rightTopPosition,
            originX: 'start',
            overlayX: 'end',
            offsetX: -offset
        };
        const topPosition: ConnectedPosition = {
            ...bottomPosition,
            originY: 'top',
            overlayY: 'bottom',
            offsetY: -offset
        };
        let position: ConnectedPosition[];
        switch (placement) {
            case 'rightTop':
                position = [rightTopPosition, rightBottomPosition];
                break;
            case 'leftTop':
                position = [leftTopPosition];
                break;
            default:
                position = [bottomPosition, topPosition];
                break;
        }

        updatePopoverPosition(overlayRef, origin, position);
    }

    getDeleteConfirmOptions(pageOrIds: string[] | PageInfo) {
        const definitionName = this.translate.instant<I18nSourceDefinitionType>('styx.page');
        const description = this.translate.instant('wiki.page.delete.description');
        return {
            definitionName,
            description
        };
    }

    deletePage(spaceIdentifier: string, page: PageInfo, isToPage = false) {
        let previousPageId = null;
        if (isToPage) {
            const previousNode = getPreviousNodeByKey(this.pageTreeService.pageNodes, '_id', page._id);
            if (previousNode) {
                previousPageId = previousNode.short_id ?? previousNode._id;
            }
        }
        this.deletePages(page.space_id, page, () => {
            this.pageDraftStore.remove(page._id);
            if (previousPageId) {
                this.router.navigate(['/wiki/spaces/', spaceIdentifier, 'pages', previousPageId]);
            }
        });
    }

    deletePages(spaceId: string, pageOrIds: string[] | PageInfo, callback?: () => void) {
        const isArray = helpers.isArray(pageOrIds);
        const ids = isArray ? pageOrIds : [pageOrIds._id];
        const target = isArray ? pageOrIds : convertToBroadObject(pageOrIds as PageInfo, WikiBroadObjectTypes.page);
        const { definitionName, description } = this.getDeleteConfirmOptions(pageOrIds);

        let mainContent = '';
        if (isArray) {
            mainContent = this.translate.instant('wiki.page.delete.confirmMultiple', {
                count: pageOrIds.length
            });
        } else {
            mainContent = this.translate.instant('wiki.page.delete.confirmSingle', {
                name: pageOrIds.name
            });
        }
        this.confirm.confirm({
            title: this.translate.instant<I18nSourceDefinitionType>('common.deleteConfirm'),
            mainContent,
            operationName: this.translate.instant<I18nSourceDefinitionType>('common.delete'),
            operationType: 'danger',
            definitionName,
            target,
            description,
            okLoadingText: this.translate.instant<I18nSourceDefinitionType>('common.deleting'),
            action: () => {
                return this.pageApiService.deletePages(spaceId, ids).pipe(
                    map(() => {
                        this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.deleteSuccess'));
                        this.pagesStore.pureRemovePageById(ids);
                        if (callback) {
                            callback();
                        }
                        return true;
                    }),
                    catchError(error => {
                        this.errorService.defaultErrorHandler(error);
                        return of(true);
                    })
                );
            }
        });
    }

    /**
     * 打开页面编辑方法
     * @param editId 页面 Id
     * @param viewContainerRef
     * @param isPublished 页面是否发布过:已发布是 true, 草稿是 false
     * @param from 枚举值 EditingPageFrom, 区分从模版新建还是普通新建, null 代表编辑
     * @param pageType 页面类型，table 类型打开弹窗编辑
     * @returns
     */
    openPageEdit(editId: string, isPublished: boolean, from?: EditingPageFrom, viewContainerRef?: ViewContainerRef, pageType?: PageTypes) {
        if (pageType === PageTypes.table) {
            return this.openWikiPageDialog({
                pageId: editId,
                viewContainerRef,
                isCreatePage: true
            });
        } else {
            return this.util.dialog.open(this.pageEditComponent, {
                size: ThyDialogSizes.full,
                backdropClosable: false,
                restoreFocus: false,
                closeOnNavigation: false,
                viewContainerRef,
                initialState: {
                    editId,
                    from,
                    isPublished
                }
            });
        }
    }

    editStencilPage(options: StencilEditOptions) {
        const { editId, pageType, isGlobal, viewContainerRef, syncStencilList } = options;
        return this.util.dialog.open(this.pageStencilEditComponent, {
            size: ThyDialogSizes.full,
            backdropClosable: false,
            restoreFocus: false,
            viewContainerRef,
            initialState: {
                editId,
                pageType,
                isGlobal,
                syncStencilList
            }
        });
    }

    openStencilDetail(
        stencilId?: string,
        mode?: 'page-create' | 'space-setting' | 'configuration',
        viewContainerRef?: ViewContainerRef,
        parentId?: string,
        afterId?: string
    ) {
        return this.util.dialog.open(this.pageStencilDetailComponent, {
            size: ThyDialogSizes.superLg,
            viewContainerRef,
            initialState: { stencilId, mode, parentId, afterId }
        });
    }

    addRelationResource(page: PageInfo, resource: PageExtensionItemInfo, type: string) {
        const hasCreatePermission =
            hasPermissionTransform(page.permissions, helpers.getStoragePermissionPoints(wikiPermissionDefinition), `relation_${type}`) ||
            false;
        if (hasCreatePermission) {
            return this.relationApiService.addRelation(page._id, resource._id, WikiBroadObjectTypes.page, type);
        }
        return of(null);
    }

    openPageSharing(options: PageSharingPopoverOptions) {
        if (!this.pricingService.ensureCheckpointEnabled('pageShare')) {
            return;
        }
        const { viewContainerRef, ...rest } = options;
        const pagePermissionDialog = this.util.dialog.open(PageSharingComponent, {
            initialState: rest,
            closeOnNavigation: false,
            viewContainerRef: viewContainerRef,
            size: ThyDialogSizes.maxLg,
            height: '640px',
            canClose: () => {
                const { isSpaceSettingChange, isNoSpaceSettingChange } = pagePermissionDialog.componentInstance;
                if (isSpaceSettingChange || isNoSpaceSettingChange) {
                    this.util.dialog.confirm({
                        title: this.translate.instant<I18nSourceDefinitionType>('common.confirmExit'),
                        content: this.translate.instant<I18nSourceDefinitionType>('wiki.page.sharing.exitConfirm'),
                        footerAlign: 'right',
                        okType: 'danger',
                        okText: this.translate.instant<I18nSourceDefinitionType>('common.ok'),
                        cancelText: this.translate.instant<I18nSourceDefinitionType>('common.cancel'),
                        onOk: () => {
                            pagePermissionDialog.close(null, true);
                        }
                    });
                    return false;
                }
                return true;
            }
        });
        return pagePermissionDialog;
    }

    createPage(completeHandle: (pageInfo: EditingPageResult | null) => void, spaceId?: string, viewContainerRef?: ViewContainerRef) {
        this.thyDialog
            .open(SelectPageComponent, {
                initialState: {
                    title: this.translate.instant<I18nSourceDefinitionType>('wiki.common.action.newPage'),
                    componentMode: 'create',
                    spaceId
                }
            })
            .afterClosed()
            .subscribe((result: { pageInfo: PageInfo; createType: PageTypes }) => {
                if (!result && !result?.pageInfo) return;
                const { pageInfo, createType } = result;
                const newPage: DraftInfo = {
                    name: getDefaultPageTitle(createType, this.translate),
                    parent_id: pageInfo._id,
                    type: createType
                };

                this.editingPageStore.createPage(pageInfo.space_id, newPage, EditingPageFrom.create, this.router.url).subscribe({
                    next: data => {
                        this.openPageEdit(data.value._id, false, EditingPageFrom.create, viewContainerRef, createType)
                            .afterClosed()
                            .subscribe((editingPageResult: EditingPageResult) => {
                                completeHandle(editingPageResult);

                                if (editingPageResult && editingPageResult.is_published) {
                                    const action = () => {
                                        this.openWikiPageDialog({
                                            pageId: editingPageResult._id,
                                            hasPageTreeSidebar: true,
                                            viewContainerRef
                                        });
                                    };

                                    this.util.notify.success(
                                        this.translate.instant<I18nSourceDefinitionType>(`common.success`),
                                        this.translate.instant<I18nSourceDefinitionType>('common.createSuccess'),
                                        {
                                            detail: {
                                                link: this.translate.instant<I18nSourceDefinitionType>('common.lookAt'),
                                                action: action
                                            }
                                        }
                                    );
                                }
                            });
                    },
                    error: error => {
                        this.errorService.defaultErrorHandler(error);
                    }
                });
            });
    }

    addGroup(spaceId: string, newGroup: DraftInfo) {
        return this.pagesStore.pureAddGroup(spaceId, newGroup).pipe(
            tap(() => {
                this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.newSuccess'));
            }),
            catchError(error => {
                this.errorService.defaultErrorHandler(error);
                return of(true);
            })
        );
    }

    newWindowOpenPage(spaceIdentifier: string, pageId: string) {
        const url = this.util.generateTeamUrl(`/wiki/spaces/${spaceIdentifier}/pages/${pageId}`);
        window.open(url);
    }

    /**
     * 打开复制页面的弹窗
     * @param selectPage 要复制的页面
     * @param viewContainerRef
     * @param copySuccess 复制完成后的回调
     */
    openCopyPageDialog(options: CopyPageOptions) {
        const { selectPageOrIds, isMultiple = false, viewContainerRef, copySuccess } = options;
        const currentSpaceId = helpers.isArray(selectPageOrIds) ? selectPageOrIds[0].space_id : selectPageOrIds.space_id;
        this.thyDialog.open(this.pageCopyComponent, {
            initialState: {
                isMultiple,
                operationPage: selectPageOrIds,
                currentSpaceId,
                showIncludeChildren: !isMultiple,
                copySuccess
            },
            viewContainerRef
        });
    }

    /**
     * 打开移动页面的弹窗
     * @param selectPage 要移动的页面
     * @param viewContainerRef
     * @param moveSuccess 移动完成后的回调
     */
    openMovePageDialog(options: MovePageOptions) {
        const { selectPageOrIds, isMultiple = false, viewContainerRef, moveSuccess } = options;
        const currentSpaceId = helpers.isArray(selectPageOrIds) ? selectPageOrIds[0].space_id : selectPageOrIds.space_id;
        this.thyDialog.open(this.pageMoveComponent, {
            initialState: {
                isMultiple,
                operationPage: selectPageOrIds,
                currentSpaceId,
                moveSuccess
            },
            viewContainerRef
        });
    }
}
