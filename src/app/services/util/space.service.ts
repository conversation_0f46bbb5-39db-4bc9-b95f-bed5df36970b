import { Injectable, ViewContainerRef } from '@angular/core';
import { StyxConfirmService, UtilService, StyxTranslateService } from '@atinc/ngx-styx';
import { SpaceSettingMenuComponent } from '@wiki/app/components/space-setting-menu/space-setting-menu.component';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { SpaceMoveComponent } from '@wiki/app/features/space/components/move/move.component';
import { ThyDialog } from 'ngx-tethys/dialog';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Injectable()
export class SpaceService {
    constructor(
        private thyDialog: ThyDialog,
        private confirm: StyxConfirmService,
        public util: UtilService,
        private translate: StyxTranslateService
    ) {}

    moveSpace(spaceInfo: SpaceInfo, viewContainerRef: ViewContainerRef) {
        this.util.dialog.open(SpaceMoveComponent, {
            viewContainerRef,
            initialState: { spaceInfo }
        });
    }

    archiveSpace(spaceName: string, callback: Function) {
        this.confirm['archive']({
            definitionName: this.translate.instant<I18nSourceDefinitionType>('styx.space', { isPlural: false }),
            target: { name: spaceName, type: 'space' },
            description: this.translate.instant<I18nSourceDefinitionType>('wiki.space.archiveHint'),
            action: () => {
                return callback();
            }
        });
    }

    deleteSpace(spaceName: string, callback: Function) {
        this.confirm['delete']({
            definitionName: this.translate.instant<I18nSourceDefinitionType>('styx.space', { isPlural: false }),
            target: { name: spaceName, type: 'space' },
            description: this.translate.instant<I18nSourceDefinitionType>('wiki.space.deleteHint'),
            action: () => {
                return callback();
            }
        });
    }

    openSpaceSettingPopover(space: SpaceInfo, options: { origin: HTMLElement; viewContainerRef?: ViewContainerRef }) {
        this.util.popover.open(SpaceSettingMenuComponent, {
            origin: options.origin,
            viewContainerRef: options.viewContainerRef,
            originActiveClass: 'active',
            initialState: {
                space: space
            },
            insideClosable: true,
            manualClosure: true
        });
    }

    newWindowOpenSpace(spaceIdentifier: string) {
        const url = this.util.generateTeamUrl(`/wiki/spaces/${spaceIdentifier}`);
        window.open(url);
    }
}
