import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Direction, Id, PaginationResponseData, ResponseData, helpers } from '@atinc/ngx-styx';
import { Observable } from 'rxjs';
import { API_PREFIX, ExportFileType } from '../constants';
import { PageTypes } from '../constants/page';
import { SpaceInfo, SpaceSettingInfo } from '../entities/space-info';
import { StencilInfo } from '../entities/stencil-info';
import { fetchPagesRecursion } from './util/fetch-pages';

@Injectable({
    providedIn: 'root'
})
export class SpaceApiService {
    constructor(private http: HttpClient) {}

    fetchSpaces() {
        return this.http.get(`${API_PREFIX}/pilot`);
    }

    favoriteSpace(spaceId: string) {
        return this.http.put(`${API_PREFIX}/pilot/items/${spaceId}/favorite`, null);
    }

    unFavoriteSpace(spaceId: string) {
        return this.http.delete(`${API_PREFIX}/pilot/items/${spaceId}/favorite`, {});
    }

    fetchArchivedSpaces(keywords: string) {
        return this.http.get(`${API_PREFIX}/spaces/archived?pi=0&ps=1000&keywords=${keywords}`);
    }

    fetchSpacesByPermission(points: string[] = []) {
        return this.http.get(`${API_PREFIX}/spaces${points.length > 0 ? `?${points.join(',')}` : ''}`);
    }

    fetchSpace(spaceId: string) {
        return this.http.get(`${API_PREFIX}/spaces/${spaceId}`);
    }

    activateSpace(spaceId: string) {
        return this.http.put(`${API_PREFIX}/spaces/${spaceId}/activate`, null);
    }

    checkPrefixIsExist(identifier: string) {
        return this.http.get(`${API_PREFIX}/spaces/identifier/check-is-exist?identifier=${identifier}`) as Observable<
            ResponseData<boolean, void>
        >;
    }

    fetchStencils(spaceId: Id, pageType?: PageTypes) {
        const params = { page_type: pageType, pz: 500 };
        return this.http.get<ResponseData<StencilInfo[], void>>(
            `${API_PREFIX}/spaces/${spaceId}/stencils${helpers.buildUrlParams(params)}`
        );
    }

    fetchSpaceSharedPages(spaceId: Id, pageIndex: number, isConfiguration = false) {
        const params = { pi: pageIndex };
        const api = isConfiguration ? `/configuration/shared/spaces/${spaceId}` : `/spaces/${spaceId}/shared-setting`;
        const pagesApi = `${API_PREFIX}${api}/pages${helpers.buildUrlParams(params)}`;
        return fetchPagesRecursion(
            this.http,
            pagesApi,
            (data?: PaginationResponseData) => {
                const pi = data?.page_index !== undefined ? data?.page_index + 1 : 0;
                return { pi };
            },
            (data?: PaginationResponseData) => {
                return data?.value?.length;
            }
        );
    }

    fetchSpaceSharedSetting(spaceId: Id, isConfiguration = false) {
        const configurationApi = isConfiguration ? `/configuration/shared` : '';
        return this.http.get(`${API_PREFIX}${configurationApi}/spaces/${spaceId}/shared-setting/additions`);
    }

    setSpaceSharedSetting(spaceId: Id, options: SpaceSettingInfo, isConfiguration = false) {
        const configurationApi = isConfiguration ? `/configuration/shared` : '';
        return this.http.put(`${API_PREFIX}${configurationApi}/spaces/${spaceId}/shared-setting/additions`, options);
    }

    fetchSpaceMembers(spaceId: string, payload: { pi?: number; ps?: number; keywords?: string } = {}) {
        return this.http.get(`${API_PREFIX}/spaces/${spaceId}/members${helpers.buildUrlParams({ ...payload, v: 2 })}`);
    }

    exportPage(data: { type: ExportFileType; space_id: string; page_ids?: string[]; page_id?: string }) {
        return this.http.post(`${API_PREFIX}/exporter/jobs`, data);
    }

    cancelExportPage(jobId: string) {
        return this.http.put(`${API_PREFIX}/exporter/jobs/${jobId}`, null);
    }

    getOverviewData(spaceId: string) {
        return this.http.get(`${API_PREFIX}/spaces/${spaceId}/statistics/overview`);
    }

    fetchPageDetails(spaceId: Id, params: { pi: number; ps: number; keywords?: string; sort_by?: string; sort_direction?: Direction }) {
        return this.http.get(`${API_PREFIX}/spaces/${spaceId}/statistics/pages${helpers.buildUrlParams(params)}`);
    }

    fetchStatisticsMembers(
        spaceId: Id,
        params: { pi: number; ps: number; keywords?: string; sort_by?: string; sort_direction?: Direction }
    ) {
        return this.http.get(`${API_PREFIX}/spaces/${spaceId}/statistics/members${helpers.buildUrlParams(params)}`);
    }

    /**
     * 激活已归档空间
     * @param spaceId 空间id
     * @returns
     */
    activeSpace(spaceId: string) {
        return this.http.put(`${API_PREFIX}/spaces/${spaceId}/active`, null);
    }
}
