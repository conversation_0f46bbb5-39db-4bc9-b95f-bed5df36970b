import { Injectable, inject } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { PilotEntryStore } from '@atinc/ngx-styx';
import { helpers } from '@atinc/ngx-styx/core';
import { ROUTE_PREFIX } from '@wiki/app/constants';
import { getAddons } from '@wiki/app/util/addons';
import { of } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class AddonResolver {
    private router = inject(Router);

    private pilotStore = inject(PilotEntryStore);

    resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
        const pilot = this.pilotStore.snapshot.detail;
        if (pilot) {
            const addonKey = route.routeConfig.path;
            const addons = getAddons(pilot.addons);
            const addon = addons.find(item => item.key === route.routeConfig.path);
            if (addon?.is_enabled) {
                this.pilotStore.switchAddon(addonKey);
                return of(true);
            } else {
                const enableAddonKey = helpers.find(addons, _addon => _addon.is_enabled)?.key;
                const path = enableAddonKey ? '/' + (enableAddonKey === 'baseline' ? 'baselines' : enableAddonKey) : '';
                const url = `${ROUTE_PREFIX}/spaces/${pilot.identifier}${path}`;
                this.router.navigateByUrl(url, { replaceUrl: true });
                return of(true);
            }
        } else {
            return of(null);
        }
    }
}
