import { NgModule } from '@angular/core';
import { ɵEmptyOutletComponent as EmptyOutletComponent, RouterModule, Routes } from '@angular/router';
import {
    createReviewRoutes,
    flowTokenResolveData,
    pilotResolveData,
    StyxReviewDetailWrapComponent,
    StyxSecurityPermissionsComponent,
    subAppInitialResolveData
} from '@atinc/ngx-styx';
import { ScopeTypeIsPersonalPipe } from '@wiki/common/pipe/scope-type.pipe';
import { redirectToRoute } from '@worktile/planet';
import {
    baselineRoutes,
    ConfigurationComponent,
    ConfigurationReviewComponent,
    ConfigurationSecurityComponent,
    ConfigurationSharingComponent,
    ConfigurationSpaceComponent,
    ConfigurationSpaceManageComponent,
    ConfigurationStencilsComponent,
    ConfigurationTagsComponent,
    ConfigurationCategoriesComponent,
    DeskCollectionsComponent,
    DeskComponent,
    DeskRecentComponent,
    DeskShareComponent,
    DeskSpacesComponent,
    PageDetailComponent,
    PageDetailWrapComponent,
    PageDraftDetailComponent,
    SettingsAdvanceComponent,
    SpaceComponent,
    SpaceDetailComponent,
    SpaceSettingsBasicComponent,
    SpaceSettingsComponent,
    SpaceSettingsFlowComponent,
    SpaceSettingsMemberComponent,
    SpaceSettingsPageSharedComponent,
    SpaceSettingsReviewComponent,
    SpaceSettingsStencilsComponent,
    SpaceSettingsTrashComponent,
    SpaceSettingTagsComponent,
    SpaceSharedComponent,
    SpaceTagsComponent,
    WikiAddonEmptyComponent,
    WikiPagesEmptyComponent,
    WikiSettingDirectoryComponent,
    WikiSpaceAddonsComponent,
    WikiStatisticsComponent,
    WikiTagsComponent
} from './features/index';
import { ActualRootComponent } from './root/root.component';
import { SharingGuard } from './services/guard/sharing-guard';
import { AddonResolver } from './services/resolver/addon.resolver';
import { AITableGridView } from '@ai-table/grid-view/grid.component';

const routes: Routes = [
    {
        path: 'wiki',
        component: ActualRootComponent,
        resolve: subAppInitialResolveData,
        children: [
            {
                path: '',
                component: DeskComponent,
                children: [
                    redirectToRoute('spaces'),
                    { path: 'pages/:pageIdOrShortId', component: PageDetailWrapComponent },
                    {
                        path: 'spaces',
                        component: DeskSpacesComponent
                    },
                    {
                        path: 'spaces/organization',
                        component: DeskSpacesComponent
                    },
                    {
                        path: 'spaces/team',
                        component: DeskSpacesComponent
                    },
                    {
                        path: 'spaces/archived',
                        component: DeskSpacesComponent
                    },
                    {
                        path: 'spaces/personal',
                        component: DeskSpacesComponent
                    },
                    {
                        path: 'recent',
                        component: DeskRecentComponent
                    },
                    {
                        path: 'shared',
                        component: DeskShareComponent
                    },
                    {
                        path: 'favorites',
                        component: DeskCollectionsComponent
                    },
                    {
                        path: 'reviews',
                        component: DeskSpacesComponent,
                        children: [
                            {
                                path: ':reviewId',
                                component: StyxReviewDetailWrapComponent
                            }
                        ]
                    }
                ]
            },
            {
                path: 'spaces/:idOrIdentifier',
                component: SpaceComponent,
                resolve: pilotResolveData,
                children: [
                    {
                        path: '',
                        pathMatch: 'full',
                        component: WikiAddonEmptyComponent
                    },
                    {
                        path: '',
                        component: SpaceDetailComponent,
                        children: [
                            {
                                path: 'pages',
                                resolve: {
                                    addon: AddonResolver
                                },
                                children: [
                                    {
                                        path: '',
                                        pathMatch: 'full',
                                        component: WikiPagesEmptyComponent
                                    },
                                    {
                                        path: ':pageIdOrShortId',
                                        component: PageDetailComponent,
                                        children: [
                                            {
                                                path: 'edit',
                                                component: PageDetailWrapComponent
                                            },
                                            {
                                                path: 'versions',
                                                component: PageDetailWrapComponent
                                            },
                                            {
                                                path: ':viewShortId',
                                                component: AITableGridView
                                            }
                                        ]
                                    }
                                ]
                            },
                            {
                                path: 'draft-pages/:draftIdOrShortId',
                                component: PageDraftDetailComponent,
                                children: [
                                    {
                                        path: 'edit',
                                        component: PageDetailWrapComponent
                                    }
                                ]
                            }
                        ]
                    },
                    createReviewRoutes({ reviewsResolver: AddonResolver as any }),
                    ...baselineRoutes,
                    {
                        path: 'settings',
                        component: SpaceSettingsComponent,
                        children: [
                            redirectToRoute('members'),
                            {
                                path: 'basic',
                                component: SpaceSettingsBasicComponent
                            },
                            {
                                path: 'members',
                                component: SpaceSettingsMemberComponent
                            },
                            {
                                path: 'stencils',
                                component: SpaceSettingsStencilsComponent
                            },
                            {
                                path: 'page-shared',
                                component: SpaceSettingsPageSharedComponent,
                                canActivate: [SharingGuard]
                            },
                            {
                                path: 'flow',
                                component: SpaceSettingsFlowComponent,
                                canDeactivate: [
                                    (component: SpaceSettingsFlowComponent) => {
                                        return component.componentRef?.componentInstance.openExitDialog();
                                    }
                                ],
                                resolve: flowTokenResolveData,
                                children: [
                                    {
                                        path: '**',
                                        component: SpaceSettingsFlowComponent
                                    }
                                ]
                            },
                            {
                                path: 'advance',
                                component: SettingsAdvanceComponent
                            },
                            {
                                path: 'space-shared',
                                component: SpaceSharedComponent
                            },
                            {
                                path: 'trash',
                                component: SpaceSettingsTrashComponent
                            },
                            {
                                path: 'component',
                                component: WikiSpaceAddonsComponent
                            },
                            {
                                path: 'directory',
                                component: WikiSettingDirectoryComponent
                            },
                            {
                                path: 'statistics',
                                component: WikiStatisticsComponent
                            },
                            {
                                path: 'tags',
                                component: SpaceSettingTagsComponent
                            },
                            {
                                path: 'review',
                                component: SpaceSettingsReviewComponent
                            }
                        ]
                    },
                    {
                        path: 'tags',
                        component: SpaceTagsComponent
                    }
                ]
            },
            {
                path: 'tags',
                component: WikiTagsComponent
            }
        ]
    },
    {
        path: 'admin/product/wiki/configuration',
        component: ConfigurationComponent,
        children: [
            redirectToRoute('spaces'),
            {
                path: 'spaces',
                component: ConfigurationSpaceManageComponent
            },
            {
                path: 'stencils',
                resolve: subAppInitialResolveData,
                component: ConfigurationStencilsComponent
            },
            {
                path: 'sharing',
                component: ConfigurationSharingComponent,
                canActivate: [SharingGuard]
            },
            {
                path: 'security',
                component: ConfigurationSecurityComponent,
                children: [redirectToRoute('permissions'), { path: 'permissions', component: StyxSecurityPermissionsComponent }]
            },
            {
                path: 'space',
                component: ConfigurationSpaceComponent
            },
            {
                path: 'tags',
                component: ConfigurationTagsComponent
            },
            {
                path: 'categories',
                component: ConfigurationCategoriesComponent
            },
            {
                path: 'review',
                component: ConfigurationReviewComponent
            }
        ]
    },
    {
        path: '**',
        component: EmptyOutletComponent
    }
];

@NgModule({
    imports: [RouterModule.forRoot(routes, {})],
    exports: [RouterModule],
    providers: [ScopeTypeIsPersonalPipe]
})
export class AppRoutingModule {}
