<thy-layout>
  <thy-header class="pl-4" styxSecondaryHeader thyDivided>
    <ng-template #headerTitle>
      <styx-awesome-text
        class="wiki-related-space-title"
        [styxText]="space?.name"
        [styxIcon]="space?.type | spaceTemplateIcon"
        [styxIconColor]="space?.color | mapColor"
        (click)="clickToWiki()"
      ></styx-awesome-text>
    </ng-template>
    <ng-template #headerOperation>
      @if (homePage?.permissions | hasAnyPermission: ['page_create']) {
        <a thyButton="primary" thyIcon="plus-bold" thySize="md" thyPlacement="bottomRight" [thyDropdown]="menu" styxI18nTracking>
          {{ 'wiki.common.action.newPage' | translate }} <thy-icon thyIconName="caret-down"></thy-icon>
        </a>
        <thy-dropdown-menu #menu>
          <wiki-add-page-menu
            [initPages]="initPages"
            [homePage]="homePage"
            [spaceId]="space._id"
            [createPageType]="createPageType.Child"
            [allowNewGroup]="false"
            [addPageHandler]="addNewPage"
            [isHome]="true"
          ></wiki-add-page-menu>
        </thy-dropdown-menu>
      }

      @if (isAllowRelated) {
        @if (homePage?.permissions | hasAnyPermission: ['page_create']) {
          <!-- 当 button 和 action 同时存在时，分隔线才会存在 -->
          <thy-divider thyColor="light" [thyVertical]="true"></thy-divider>
        }
        <a
          href="javascript:;"
          thyAction
          thyIcon="list"
          styxI18nTracking
          [thyTooltip]="'common.more' | translate"
          thyTooltipPlacement="bottom"
          thyActiveClass="active"
          (click)="openMoreMenu($event)"
        ></a>
        <ng-template #moreMenuTpl>
          <ng-content></ng-content>
        </ng-template>
      }
    </ng-template>
  </thy-header>
  <thy-content>
    <thy-content-main>
      <list-filter-view class="ml-5" mode="complex" [listCount]="pageCount" (keywordsChange)="onSearch($event)"></list-filter-view>
      <styx-table
        #table
        styxMode="lite"
        [styxLoadingDone]="tableDataLoadingDone"
        [styxData]="pageNodes"
        [styxShowExpand]="showExpand"
        [styxExpandResolve]="loadChildren"
        [styxSelectable]="false"
        [styxShowSizeChanger]="false"
        [styxShowHeaderSetting]="false"
        [styxShowColumnAction]="false"
        [styxSortable]="false"
        [styxVirtualScroll]="true"
        [styxRowHeight]="52"
        [styxSortConfig]="sortConfig"
        (styxClick)="rowClick($event)"
        (styxSelect)="rowClick($event)"
        (styxSort)="sortChange($event)"
      >
        <styx-table-column
          styxProperty
          styxKey="name"
          styxI18nTracking
          [styxName]="'common.name' | translate"
          styxFixed="left"
          styxWidth="50%"
          [styxExpandable]="expandable"
          [styxSortable]="true"
        >
          <ng-template #cell let-row="row">
            <styx-awesome-text
              [styxText]="row.name"
              [styxCode]="row.emoji_icon"
              [styxTooltips]="row.name"
              [styxIcon]="row | pageIcon"
              [styxIconColor]="row | pageIconColor"
            >
            </styx-awesome-text>
          </ng-template>
          <ng-template let-page #actions>
            @if (page | isPage) {
              <a
                href="javascript:;"
                thyAction
                thyActionIcon="publish"
                (click)="gotoPage($event, page)"
                styxI18nTracking
                [thyTooltip]="'styx.openNew' | translate"
              ></a>
            }
            @if ((page?.permissions | hasAnyPermission: ['page_create']) && page?.system !== 1) {
              <a
                href="javascript:;"
                thyAction
                thyActionIcon="more-vertical"
                styxI18nTracking
                [thyTooltip]="'common.more' | translate"
                [class.active]="active"
                (click)="openPageMenu($event, page)"
              ></a>
            }
          </ng-template>
        </styx-table-column>

        <styx-table-column styxProperty styxWidth="25%" styxKey="updated_by" styxI18nTracking [styxName]="'styx.updatedBy' | translate">
          <ng-template #cell let-row="row">
            <thy-avatar
              class="mr-1"
              thySize="xs"
              thyShowName="true"
              [thySrc]="(row.updated_by | user)?.avatar"
              [thyName]="(row.updated_by | user)?.display_name"
            >
            </thy-avatar>
          </ng-template>
        </styx-table-column>

        <styx-table-column
          styxProperty
          styxKey="updated_at"
          styxWidth="25%"
          styxI18nTracking
          [styxName]="'styx.updatedAt' | translate"
          [styxSortable]="true"
        >
          <ng-template #cell let-row="row">
            <span class="styx-item-title text-muted">
              {{ row?.updated_at | dateTimeAutoFormat }}
            </span>
          </ng-template>
        </styx-table-column>
      </styx-table>
    </thy-content-main>
  </thy-content>
</thy-layout>
