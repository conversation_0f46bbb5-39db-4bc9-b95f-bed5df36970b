import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { AppRootContext, AttachmentEntity, DriveEntity, GlobalUsersStore, PilotEntryStore, ResponseData } from '@atinc/ngx-styx';
import { Action } from '@tethys/store';
import { AttachmentScope } from '@wiki/common/interface/base-file';
import moment from 'moment';
import { Id } from 'ngx-tethys/types';
import { produce } from 'ngx-tethys/util';
import { Observable, of } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { API_PREFIX } from '../../constants';
import { StencilInfo } from '../../entities/stencil-info';
import { StencilStore } from './stencil.store';

@Injectable()
export class PageStencilEditStore extends StencilStore {
    constructor(
        public globalUsersStore: GlobalUsersStore,
        public appRootContext: AppRootContext,
        public http: HttpClient,
        public router: Router
    ) {
        super();
    }

    private pilotStore = inject(PilotEntryStore);

    private get isConfigurationModule(): boolean {
        return this.router.url.includes('configuration');
    }

    protected stencilsApiPrefix(isGlobal?: boolean) {
        const spaceApi = `${API_PREFIX}/spaces/${this.pilotStore.snapshot.detail?._id}/stencils`;
        const configurationApi = `${API_PREFIX}/configuration/stencils`;
        if (this.isConfigurationModule || isGlobal) {
            return configurationApi;
        }
        return spaceApi;
    }

    updateStencil(page: StencilInfo): Observable<any> {
        return this.http.put<ResponseData<StencilInfo>>(`${this.stencilsApiPrefix()}/${page._id}`, page).pipe(
            map(
                (responseData: ResponseData<StencilInfo>) => {
                    this.globalUsersStore.addUsers(responseData.references.members);
                    return responseData;
                },
                catchError(error => {
                    return of(false);
                })
            )
        );
    }

    @Action()
    addAttachment(stencilId: Id, driveEntity: DriveEntity, attachmentScope?: AttachmentScope): Observable<AttachmentEntity> {
        this.pureAddAttachment(driveEntity);
        return this.http
            .post(`${this.stencilsApiPrefix()}/${stencilId}/attachment`, {
                attachment_id: driveEntity._id,
                attachment_scope: attachmentScope
            })
            .pipe(
                map((data: ResponseData) => {
                    const attachment: AttachmentEntity = {
                        ...data.value,
                        attachment_id: data.value._id,
                        updated_at: data.value.updated_at ? data.value.updated_at : moment().unix(),
                        updated_by: data.value.updated_by ? data.value.updated_by : this.appRootContext.globalInfo.me
                    };
                    this.pureRemoveAttachmentById(driveEntity._id);
                    this.pureAddAttachment(attachment);
                    return attachment;
                })
            );
    }

    @Action()
    modifyAttachmentName(stencilId: string, attachmentId: string, title: string) {
        return this.http.put(`${this.stencilsApiPrefix()}/${stencilId}/attachments/${attachmentId}/title`, { title: title }).pipe(
            tap(data => {
                this.update({
                    page: {
                        ...this.snapshot.page,
                        attachments: produce(this.snapshot.page.attachments).update(attachmentId, attachment => {
                            return { ...attachment, title };
                        })
                    }
                });
            })
        );
    }

    @Action()
    deleteAttachment(stencilId: string, attachmentId: string) {
        return this.http.delete(`${this.stencilsApiPrefix()}/${stencilId}/attachments/${attachmentId}`).pipe(
            tap((data: any) => {
                this.pureRemoveAttachmentById(attachmentId);
            })
        );
    }
}
