import { Component, ElementRef, HostBinding, inject, input, Input, OnInit, ViewContainerRef } from '@angular/core';
import { Router } from '@angular/router';
import {
    Direction,
    GlobalApplicationPermissionPipe,
    Is,
    PilotEntryStore,
    StyxPricingService,
    SubAppRootContext,
    hasPermissionTransform
} from '@atinc/ngx-styx';
import { EditingPageFrom, PageTypes } from '@wiki/app/constants/page';
import { DraftInfo, PageInfo, StencilInfo } from '@wiki/app/entities';
import { PageContext } from '@wiki/app/services/context/page.context';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PageService } from '@wiki/app/services/util/page.service';
import { GlobalStencilsStore } from '@wiki/app/stores/global-stencil-list.store';
import { globalPermissionDefinition, wikiPermissionPoints } from '@wiki/common/constants';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { WikiPluginContext } from '@wiki/common/services';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { initPageStoreProviders } from '@wiki/common/util/init-page-store-provider';
import { THE_MODE_TOKEN } from '@worktile/theia';
import moment from 'moment';
import { ScrollToService } from 'ngx-tethys/core';
import { ThyDialogRef } from 'ngx-tethys/dialog';
import { finalize } from 'rxjs/operators';
import { StencilStore } from '../stencil.store';
import { SpaceStencilsStore } from '../stencils.store';

@Component({
    selector: 'wiki-stencil-content-detail',
    templateUrl: './stencil-detail.component.html',
    providers: [
        SpaceStencilsStore,
        GlobalStencilsStore,
        GlobalApplicationPermissionPipe,
        {
            provide: WikiPluginContext,
            useClass: PageContext
        },
        {
            provide: THE_MODE_TOKEN,
            useValue: {
                mode: TheExtensionMode.stencils
            }
        },
        ...initPageStoreProviders(StencilStore)
    ],
    standalone: false
})
export class StencilDetailComponent implements OnInit {
    @HostBinding('class') hostClass = 'stencil-detail-container';

    @Input() stencilId = null;

    @Input() mode: 'page-create' | 'space-setting' | 'configuration';

    @Input() parentId: string;

    afterId = input<string | null | undefined>();

    navItem: 'team' | 'space' = 'team';

    emptyContent = false;

    saving = false;

    searchText: string;

    stencilListLoadingDone = true;

    stencilInfoLoadingDone = true;

    /* 接口数据 */
    stencils: StencilInfo[] = [];

    /* 渲染的数据 */
    stencilItems: StencilInfo[] = [];

    isEdit: boolean;

    get spaceId() {
        return this.pilotStore.snapshot?.detail?._id;
    }

    get selectedStencil(): StencilInfo {
        return this.stencilStore.snapshot.page;
    }

    get stencilTitle() {
        return this.stencilStore.snapshot.page?.name || '';
    }

    get selectedStencilContent() {
        return this.stencilStore.snapshot.content;
    }

    private pilotStore = inject(PilotEntryStore);

    constructor(
        private thyDialogRef: ThyDialogRef<StencilDetailComponent>,
        public router: Router,
        public pricingService: StyxPricingService,
        private globalStencilsStore: GlobalStencilsStore,
        private spaceStencilsStore: SpaceStencilsStore,
        public stencilStore: StencilStore,
        private pageService: PageService,
        private viewContainerRef: ViewContainerRef,
        private errorService: WikiErrorService,
        private editingPageStore: CommonEditingPageStore,
        private elementRef: ElementRef,
        private subAppRootContext: SubAppRootContext,
        private globalApplicationPermissionPipe: GlobalApplicationPermissionPipe
    ) {}

    ngOnInit() {
        this.setNavItem();
        this.fetchStencils().add(() => {
            if (!this.stencilId) {
                this.stencilId = this.stencilItems?.[0]?._id;
            }
            if (this.stencilId) {
                this.fetchStencil(this.stencilId).add(() => {
                    const targetElement = this.elementRef.nativeElement.querySelector('.thy-menu-item.active');
                    const container = this.elementRef.nativeElement.querySelector('.thy-menu');
                    ScrollToService.scrollToElement(targetElement, container);
                });
            } else {
                this.emptyContent = true;
            }
        });
    }

    setNavItem() {
        this.navItem = this.mode === 'configuration' ? 'team' : 'space';
    }

    verifyEditPermission() {
        if (this.mode === 'configuration') {
            this.isEdit = true;
            return;
        }
        if (this.navItem === 'team') {
            this.isEdit = this.globalApplicationPermissionPipe.transform(
                this.subAppRootContext.snapshot.globalPermissions,
                globalPermissionDefinition.configuration_settings.key
            );
        } else {
            this.isEdit = hasPermissionTransform(this.selectedStencil?.permissions, wikiPermissionPoints, 'space_stencil_setting');
        }
    }

    fetchStencils() {
        this.stencilListLoadingDone = false;
        if (this.mode === 'configuration') {
            return this.globalStencilsStore
                .fetchStencils(this.searchText, undefined, 3000, { sortBy: 'updated_at', sortDirection: Direction.descending })
                .pipe(
                    finalize(() => {
                        this.stencilListLoadingDone = true;
                    })
                )
                .subscribe(() => {
                    this.stencils = this.globalStencilsStore.snapshot.entities;
                    this.stencilItems = this.globalStencilsStore.snapshot.entities;
                });
        } else {
            return this.spaceStencilsStore
                .fetchStencils(this.spaceId)
                .pipe(
                    finalize(() => {
                        this.stencilListLoadingDone = true;
                    })
                )
                .subscribe(data => {
                    this.stencils = data.value;
                    this.filterStencils();
                });
        }
    }

    fetchStencil(stencilId: string) {
        this.stencilInfoLoadingDone = false;
        return this.stencilStore
            .fetchPage(stencilId, { spaceId: this.spaceId, isGlobal: this.navItem === 'team' })
            .pipe(
                finalize(() => {
                    this.stencilInfoLoadingDone = true;
                })
            )
            .subscribe(() => {
                this.verifyEditPermission();
            });
    }

    search(value: string) {
        this.searchText = value.trim();
        this.searchText ? this.filterStencils() : this.clearSearch();
    }

    clearSearch() {
        if (this.mode === 'configuration') {
            this.stencilItems = this.stencils;
        } else {
            this.filterStencils();
        }
    }

    syncStencilList(stencil: StencilInfo) {
        const stencilIdx = this.stencils.findIndex(x => x._id === stencil._id);
        const stencilItemIdx = this.stencilItems.findIndex(x => x._id === stencil._id);
        if (stencilIdx !== -1 && stencilItemIdx !== -1) {
            this.stencils[stencilIdx] = stencil;
            this.stencilItems[stencilItemIdx] = stencil;
        }
    }

    filterStencils() {
        this.stencilItems =
            this.stencils?.filter(x => {
                const baseCondition = x.is_global === (this.navItem === 'team' ? Is.yes : Is.no);
                return this.searchText ? baseCondition && x.name.includes(this.searchText) : baseCondition;
            }) || [];
    }

    toggleNavItem() {
        this.navItem = this.navItem === 'space' ? 'team' : 'space';
        this.filterStencils();
    }

    selectionChange(event: Event, stencilId: string) {
        this.stencilId = stencilId;
        this.fetchStencil(stencilId);
    }

    closeDialog() {
        this.thyDialogRef.close();
    }

    editPage() {
        this.pageService.editStencilPage({
            editId: this.stencilId,
            pageType: this.selectedStencil.page_type,
            isGlobal: this.navItem === 'team',
            viewContainerRef: this.viewContainerRef,
            syncStencilList: (stencil: StencilInfo) => this.syncStencilList(stencil)
        });
    }

    useStencil() {
        if (this.saving) {
            return;
        }
        this.saving = true;
        const stencil = this.stencils.find(t => t._id === this.stencilId);
        const page: DraftInfo = {
            parent_id: this.parentId,
            stencil_id: this.stencilId,
            name: stencil.is_timestamp ? `${stencil.name} ${moment().format('YYYY-MM-DD')}` : stencil.name
        };
        if (this.afterId()) {
            page.after_id = this.afterId();
            page.append_when_nil_after_id = true;
        } else if (this.afterId() === null) {
            page.after_id = null;
            page.append_when_nil_after_id = false;
        }
        if (!page.type) {
            page.type = PageTypes.document;
        }
        this.editingPageStore
            .createPage(this.spaceId, page, EditingPageFrom.stencil)
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe({
                next: data => {
                    this.closeDialog();
                    this.router.navigate([
                        '/wiki/spaces/',
                        this.pilotStore.snapshot?.detail.identifier,
                        'draft-pages',
                        data.value.short_id ?? data.value._id,
                        'edit'
                    ]);
                },
                error: error => {
                    this.errorService.defaultErrorHandler(error);
                }
            });
    }
}
