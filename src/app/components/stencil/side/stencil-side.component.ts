import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    EventEmitter,
    inject,
    Input,
    OnDestroy,
    OnInit,
    Output,
    TemplateRef,
    ViewChild
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Is, ResponseData, StyxTranslateService, UtilService, helpers } from '@atinc/ngx-styx';
import { asyncBehavior } from '@tethys/cdk/behaviors';
import { EditingPageFrom, PageTypes } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { StencilInfo } from '@wiki/app/entities/stencil-info';
import { PageContext } from '@wiki/app/services/context/page.context';
import { getDefaultDocumentTitle } from '@wiki/common/constants/page';
import { TheDocument } from '@wiki/common/custom-types';
import { WikiEditDocumentRelationData, WikiPageContent } from '@wiki/common/interface/page';
import { WikiPluginContext } from '@wiki/common/services/context/plugin.context';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { isEmptyDocument } from '@wiki/common/util/common';
import { initPageStoreProviders } from '@wiki/common/util/init-page-store-provider';
import { THE_MODE_PROVIDER, createEmptyParagraph } from '@worktile/theia';
import { Subject, of } from 'rxjs';
import { catchError, debounceTime, filter, finalize, map, take, tap } from 'rxjs/operators';
import { StencilSideStore } from './stencil-side.store';
import { PageDraftApiService, PageEventBus } from '@wiki/common/services';
import { WikiErrorService } from '@wiki/app/services/error.service';

const INIT_TOP = 173;

@Component({
    selector: 'wiki-stencil-side',
    templateUrl: './stencil-side.component.html',
    providers: [
        THE_MODE_PROVIDER,
        {
            provide: WikiPluginContext,
            useClass: PageContext
        },
        ...initPageStoreProviders(StencilSideStore)
    ],
    host: {
        class: 'wiki-stencil-side'
    },
    standalone: false
})
export class StencilSideComponent implements OnInit, AfterViewInit, OnDestroy {
    translate = inject(StyxTranslateService);

    @Input() page!: PageInfo;

    @Input() value: TheDocument;

    @Input() escHandle: () => {};

    @Input() isStencilContent: boolean;

    @Input() set isMaxView(value: boolean) {
        this.setDefaultStencilPosition();
    }

    @Output() collapsedChange: EventEmitter<boolean> = new EventEmitter();

    @Output() valueChange: EventEmitter<{ document: WikiPageContent; stencilId: string } & WikiEditDocumentRelationData> =
        new EventEmitter();

    @ViewChild('confirmDialog', { read: TemplateRef, static: true }) confirmDialog: TemplateRef<any>;

    @ViewChild('panelContent', { read: ElementRef, static: true }) panelContent: ElementRef<HTMLElement>;

    @ViewChild('stencilContent', { read: ElementRef, static: false }) stencilContent: ElementRef<HTMLElement>;

    Is = Is;

    isGlobal = Is.no;

    isAbandonDraft = Is.no;

    isHiddenPanel = Is.yes;

    isApplyStencil: boolean;

    stencilId: string;

    loadingDone: boolean;

    onMouseEnter$ = new Subject<{ event: MouseEvent; stencil: StencilInfo }>();

    defaultEmptyDocument = [createEmptyParagraph()];

    maxTop: number;

    listStencils: StencilInfo[] = [];

    panelPosition = {
        top: INIT_TOP,
        left: 0
    };

    stencilsFetcher = asyncBehavior(() => {
        return this.stencilSideStore.fetchStencils(this.page?.space_id, PageTypes.document);
    });

    constructor(
        private util: UtilService,
        public cdr: ChangeDetectorRef,
        private editingPageStore: CommonEditingPageStore,
        public stencilSideStore: StencilSideStore,
        private destroyRef: DestroyRef,
        private globalPageStore: PageEventBus,
        private pageDraftApiService: PageDraftApiService,
        private errorService: WikiErrorService
    ) {}

    ngOnInit(): void {
        this.fetchStencils();
        this.getStencilInfo();
        this.selectStencil();
        window.onresize = () => {
            this.setDefaultStencilPosition();
        };
    }

    ngAfterViewInit() {
        setTimeout(() => {
            this.setDefaultStencilPosition();
        }, 200);
    }

    fetchStencils() {
        this.stencilsFetcher().execute(data => {
            this.listStencils = (data.value?.filter((stencil: StencilInfo) => stencil.is_global === this.isGlobal) || []) as StencilInfo[];
        });
    }

    changeStencilType(is_global: Is) {
        this.isGlobal = is_global;
        this.listStencils = (this.stencilSideStore.stencils?.filter((stencil: StencilInfo) => stencil.is_global === this.isGlobal) ||
            []) as StencilInfo[];
    }

    onMouseEnter(event: MouseEvent, stencil: StencilInfo) {
        this.onMouseEnter$.next({
            event,
            stencil
        });
        this.isHiddenPanel = Is.no;
        this.setStencilTop(event);
    }

    getStencilInfo() {
        this.onMouseEnter$
            .pipe(takeUntilDestroyed(this.destroyRef), debounceTime(150))
            .subscribe((data: { event: MouseEvent; stencil: StencilInfo }) => {
                const stencil = this.stencilSideStore.getStencilById(data.stencil._id);
                if (stencil && stencil.document) {
                    this.stencilSideStore.syncStencilContent(stencil);
                } else {
                    this.loadingDone = false;
                    this.stencilSideStore
                        .fetchPage(data.stencil._id, { spaceId: this.page?.space_id, isGlobal: !!this.isGlobal })
                        .pipe(
                            take(1),
                            finalize(() => {
                                this.loadingDone = true;
                            }),
                            catchError(error => {
                                this.util.defaultErrorHandler()(error);
                                return of(null);
                            })
                        )
                        .subscribe();
                }
            });
    }

    hiddenPanel() {
        this.isHiddenPanel = Is.yes;
    }

    setStencilTop(event: MouseEvent) {
        const { top } = (event.target as HTMLElement).getBoundingClientRect();
        if (top < 500) {
            this.panelPosition.top = Math.max(0, Math.min(INIT_TOP, this.maxTop));
        }
        if (top >= 500) {
            this.panelPosition.top = Math.max(0, Math.min(INIT_TOP + (top - 500), this.maxTop));
        }
    }

    setDefaultStencilPosition() {
        this.maxTop = window.innerHeight - this.panelContent?.nativeElement.clientHeight - 23;
        this.panelPosition = {
            top: Math.max(0, Math.min(INIT_TOP, this.maxTop)),
            left: this.stencilContent?.nativeElement.getBoundingClientRect().left - this.panelContent?.nativeElement.clientWidth
        };
    }

    toggleCollapse() {
        this.collapsedChange.emit(true);
    }

    selectStencil() {
        this.stencilSideStore
            .select$(state => state.page)
            .pipe(
                filter(stencil => !!stencil),
                tap((stencil: StencilInfo) => {
                    this.stencilSideStore.pureUpdateStencils(stencil);
                }),
                filter(stencil => stencil?._id === this.stencilId)
            )
            .subscribe(stencil => {
                this.applyStencil(stencil);
            });
    }

    applyStencil(stencilPage: StencilInfo) {
        this.stencilId = '';
        this.isApplyStencil = false;
        if (isEmptyDocument(this.value) || this.isStencilContent) {
            this.emitValueData({
                ...stencilPage,
                stencil_id: stencilPage._id
            });
        } else {
            const dialog = this.util.dialog.open(this.confirmDialog, {
                initialState: {
                    page: stencilPage
                }
            });
            dialog.afterClosed().subscribe(() => {
                this.isAbandonDraft = Is.no;
            });
        }
    }

    onMouseDown(stencil: StencilInfo) {
        this.hiddenPanel();
        if (this.isApplyStencil) {
            return;
        }
        this.stencilId = stencil._id;
        if (this.stencilSideStore.snapshot.page?._id === this.stencilId) {
            this.applyStencil(this.stencilSideStore.snapshot.page as StencilInfo);
        } else {
            this.isApplyStencil = true;
        }
    }

    close() {
        this.util.dialog.close();
    }

    createPageFromStencil(stencilPage: PageInfo) {
        if (!this.isAbandonDraft) {
            let page = { ...this.page };
            this.escHandle && this.escHandle();
            this.editingPageStore.createPage(this.page.space_id, page, EditingPageFrom.stencil).subscribe({
                next: data => {
                    page._id = data.value._id;
                    this.pageDraftApiService
                        .save(page._id, {
                            page_name: page.name || getDefaultDocumentTitle(this.translate),
                            emoji_icon: page.emoji_icon,
                            content: page.document
                        })
                        .pipe(
                            map((data: ResponseData<PageInfo>) => data.value),
                            catchError(error => {
                                this.errorService.defaultErrorHandler(error);
                                return of(null);
                            })
                        )
                        .subscribe(data => {
                            this.globalPageStore.emitPageInfo({
                                ...data,
                                published: false
                            });
                        });
                },
                error: error => {
                    this.util.defaultErrorHandler()(error);
                }
            });
        }
        this.emitValueData({
            ...stencilPage,
            stencil_id: stencilPage._id
        });

        this.close();
    }

    emitValueData(page: PageInfo) {
        this.valueChange.emit({
            document: helpers.cloneDeep(page.document),
            stencilId: page.stencil_id,
            attachments: helpers.cloneDeep(page.attachments),
            relation_pages: this.stencilSideStore.relationPageStore?.relationPages()
        });
    }

    ngOnDestroy() {
        this.onMouseEnter$.complete();
    }
}
