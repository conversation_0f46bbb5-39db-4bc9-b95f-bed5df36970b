import { Component, computed, EventEmitter, HostBinding, inject, Input, OnInit, Output } from '@angular/core';
import {
    AppRootContext,
    GlobalUsersStore,
    Id,
    IsMePipe,
    MemberInfo,
    ResponseData,
    ScopeDesignationType,
    StyxMemberSelectConfig,
    UserGroupInfo,
    UtilService,
    StyxSelectScopeItem,
    ScopeTypes,
    helpers,
    Is,
    DepartmentTreeNode,
    StyxTranslateService,
    PilotEntryStore
} from '@atinc/ngx-styx';
import {
    PagePermissionDesignationItem,
    PagePermissionParams,
    PagePermissionScope,
    PagePermissionSettingInfo,
    PermissionInfo
} from '@wiki/app/entities/page-permission-setting';
import { PagePermissionSettingStore } from '@wiki/app/stores/page-permission-setting.store';
import {
    PagePermissionScopeType,
    PagePermissionType,
    UserPermissionType,
    SelectProperty,
    getPermissionList,
    getPagePermissions
} from '@wiki/app/constants/page-sharing';
import { ObjectiveUserGroupPipe, PageUserPermissionPipe } from '@wiki/app/pipes/page.pipe';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { finalize } from 'rxjs/operators';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { SpaceInfo } from '@wiki/app/entities';

const ALL_USER_GROUP_ID = '-1';

@Component({
    selector: 'space-member',
    templateUrl: './space-member.component.html',
    host: {
        class: 'space-member'
    },
    providers: [PageUserPermissionPipe, ObjectiveUserGroupPipe, IsMePipe],
    standalone: false
})
export class SpaceMemberComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Input() spaceId: Id;

    @Input() pageId: Id;

    @Input() parentId: Id;

    @Output() permissionChange: EventEmitter<boolean> = new EventEmitter();

    @Output() confirmCallback: EventEmitter<PagePermissionParams> = new EventEmitter();

    public loading = false;

    public pagePermission = PagePermissionType.default;

    allReadGroup = [
        {
            type: ScopeDesignationType.userGroup,
            data: {
                name: this.translate.instant<I18nSourceDefinitionType>('wiki.member.all')
            },
            value: UserPermissionType.readable,
            id: ALL_USER_GROUP_ID,
            readonly: true
        }
    ];

    // 修改为 getter，使用 translate 服务获取列表
    permissionList = computed(() => {
        return getPermissionList(this.translate);
    });

    public headerPermission = UserPermissionType.readable;

    public selectedData: PagePermissionDesignationItem[] = [
        {
            type: ScopeDesignationType.user,
            data: this.appRootContext.me,
            value: UserPermissionType.editable,
            id: this.appRootContext.me.uid,
            readonly: true
        }
    ];

    public readableUser: { index: number; readableData: PagePermissionDesignationItem }[] = [];

    public permissionMembers: MemberInfo[] = [];

    public permissionUserGroups: UserGroupInfo[] = [];

    public permissionDepartments: DepartmentTreeNode[] = [];

    public immutableScopes: StyxSelectScopeItem[] = [];

    public extendsParentPermissionInfo: PermissionInfo | undefined = undefined;

    public parentPage: MemberInfo;

    public isSystem: Is = Is.no;

    public PagePermissionType = PagePermissionType;

    public selectProperty = SelectProperty;

    private originSetting: PagePermissionSettingInfo;

    private spaceMembers: MemberInfo[] = [];

    get isShowPermissionSetting() {
        return [PagePermissionType.allReadSpecificEdit, PagePermissionType.specificReadAndEdit].includes(this.pagePermission);
    }

    get isAllReadSpecificEdit() {
        return this.pagePermission === PagePermissionType.allReadSpecificEdit;
    }

    pagePermissionList = computed(() => {
        const permissions = getPagePermissions(this.translate);
        permissions.forEach(item => {
            if (item.value === PagePermissionType.default) {
                item.desc = this.extendsParentPermissionInfo
                    ? this.translate.instant<I18nSourceDefinitionType>('wiki.page.sharing.defaultWithParent')
                    : this.translate.instant<I18nSourceDefinitionType>('wiki.page.sharing.defaultWithSpace');
            }
        });
        return this.isSystem ? permissions.filter(item => item.value !== PagePermissionType.specificReadAndEdit) : permissions;
    });

    @HostBinding('class.space-member') spaceMember = true;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public pagePermissionSettingStore: PagePermissionSettingStore,
        public appRootContext: AppRootContext,
        public util: UtilService,
        public usersStore: GlobalUsersStore,
        private userPermissionPipe: PageUserPermissionPipe,
        private userGroupPipe: ObjectiveUserGroupPipe,
        private isMePipe: IsMePipe,
        private configService: StyxMemberSelectConfig,
        private pagesStore: PagesStore,
        private errorService: WikiErrorService
    ) {}

    ngOnInit(): void {
        const page = helpers.find(this.pagesStore.snapshot.entities, { _id: this.pageId });
        if (page?.system) {
            this.isSystem = Is.yes;
        }
        this.spaceMembers = this.getSpaceMembers();
        this.permissionMembers = this.spaceMembers;
        this.setPermissionUserGroups();
        this.setPermissionDepartments();
        this.fetchPermissionList();
    }

    getSpaceMembers() {
        const memberIds = this.pilotStore.snapshot.detail.members.map(member => member.uid);
        const userGroups = (this.pilotStore.snapshot.detail as SpaceInfo).members_be_user_group
            .map(membersBeUserGroup => this.usersStore.snapshot.userGroups.find(userGroup => userGroup._id === membersBeUserGroup.id))
            .filter(userGroup => userGroup);
        userGroups.forEach(userGroup => {
            memberIds.push(...userGroup.members.map(member => member.uid));
        });
        const result = memberIds.map(id => this.usersStore.getUser(id, { bypassWhenNotFound: true })).filter(u => !!u);
        return result;
    }

    setImmutableScopes() {
        this.immutableScopes = this.usersStore.snapshot.teamUsers
            .filter(user => !this.permissionMembers.some(item => item.uid === user.uid))
            .map(item => {
                return {
                    id: item.uid,
                    type: ScopeTypes.user,
                    scope: item
                };
            });
    }

    fetchPermissionList() {
        return this.pagePermissionSettingStore.fetchDetail(this.spaceId, this.pageId, PagePermissionScopeType.spaceInside).subscribe(
            (data: ResponseData<PagePermissionSettingInfo>) => {
                if (data.value) {
                    if (data.value?.extends_parent_permission) {
                        this.extendsParentPermissionInfo = data.value?.extends_parent_permission;
                        this.getParentPage();
                    }
                    this.originSetting = data.value;
                    this.initialize(data.value);
                }
            },
            error => {
                this.util.defaultErrorHandler()(error);
            }
        );
    }

    initialize(value: PagePermissionSettingInfo) {
        if (value?._id) {
            this.pagePermission = value?.permission_type;
            const spaceMemberIds = this.permissionMembers?.map(item => item.uid);
            this.selectedData = [
                ...value.scopes
                    .filter(
                        scope =>
                            (!this.isMePipe.transform(scope.id) &&
                                scope.type === ScopeDesignationType.user &&
                                spaceMemberIds.includes(scope.id)) ||
                            scope.type === ScopeDesignationType.userGroup ||
                            scope.type === ScopeDesignationType.department
                    )
                    .map(scope => {
                        return {
                            ...scope,
                            data: this.calculateSelectableMember(scope.id, scope.type),
                            readonly: this.isMePipe.transform(scope.id)
                        };
                    })
            ];
            if (this.isShowPermissionSetting) {
                const me = {
                    type: ScopeDesignationType.user,
                    data: this.appRootContext.me,
                    value: value.scopes.find(scope => scope.id === this.appRootContext.me.uid)?.value,
                    id: this.appRootContext.me.uid,
                    readonly: true
                };
                if (this.isAllReadSpecificEdit) {
                    this.headerPermission = UserPermissionType.editable;
                    if ((me.value && me.value === UserPermissionType.readable) || !me.value) {
                        this.selectedData = [...this.allReadGroup, ...this.selectedData];
                    } else {
                        this.selectedData = [...this.allReadGroup, me, ...this.selectedData];
                    }
                } else {
                    if (me.value) {
                        this.selectedData = [me, ...this.selectedData];
                    }
                }
            }
            this.filterSelectableList();
        }
    }

    compareChange() {
        const { scope_type, permission_type, scopes } = this.originSetting;
        const origin = { scope_type, permission_type, scopes };
        const current = this.getPagePermissionParams();
        current.scopes = current.scopes ?? [];
        const isChange = !helpers.isEqual(current, origin);
        this.permissionChange.emit(isChange);
    }

    filterSelectableList() {
        this.permissionMembers = this.spaceMembers.filter(spaceMember => !this.selectedData.some(item => item.id === spaceMember.uid));
        this.setPermissionUserGroups();
        this.setPermissionDepartments();
        this.setImmutableScopes();
    }

    setPermissionUserGroups() {
        this.configService.config.userGroupsResolve().subscribe(userGroups => {
            this.permissionUserGroups = [...userGroups].filter(
                userGroup => this.selectedData.findIndex(item => item.id === userGroup._id) < 0
            );
        });
    }

    setPermissionDepartments() {
        this.configService.config.departmentMembersResolve().subscribe(departments => {
            this.permissionDepartments = departments.filter(department => !this.selectedData.some(i => i.id === department.id));
        });
    }

    calculateSelectableMember(id: Id, type: ScopeDesignationType) {
        switch (type) {
            case ScopeDesignationType.user:
                return this.userPermissionPipe.transform(id, this.usersStore.snapshot.teamUsers);
            case ScopeDesignationType.userGroup:
                return this.userGroupPipe.transform(id, this.pagePermissionSettingStore.snapshot?.references?.member_groups);
            case ScopeDesignationType.department:
                const departments = this.pagePermissionSettingStore.snapshot.references.departments;
                return departments.find(item => item._id === id) || null;
        }
    }

    onSpacePermissionChange(permissionType: PagePermissionType) {
        const readGroupIndex = this.selectedData.findIndex(item => item.id === ALL_USER_GROUP_ID);
        if (this.isShowPermissionSetting && this.selectedData.length === 0) {
            this.selectedData = [
                {
                    type: ScopeDesignationType.user,
                    data: this.appRootContext.me,
                    value: UserPermissionType.editable,
                    id: this.appRootContext.me.uid,
                    readonly: true
                }
            ];
        }
        switch (permissionType) {
            case PagePermissionType.allReadSpecificEdit:
                this.handleAllReadSpecificEdit();
                break;
            case PagePermissionType.specificReadAndEdit:
                this.handleSpecificReadAndEdit(readGroupIndex);
                break;
        }
        this.filterSelectableList();
        this.compareChange();
    }

    handleAllReadSpecificEdit() {
        this.selectedData = [
            ...this.allReadGroup,
            ...this.selectedData.filter((item, index) => {
                if (item.value !== UserPermissionType.readable) {
                    return item;
                } else {
                    if (item.id !== ALL_USER_GROUP_ID) {
                        this.readableUser.push({
                            index,
                            readableData: item
                        });
                    }
                }
            })
        ];
        this.headerPermission = UserPermissionType.editable;
    }

    handleSpecificReadAndEdit(readGroupIndex: number) {
        // remove readGroup
        if (readGroupIndex >= 0) {
            this.selectedData = [...this.selectedData].slice(readGroupIndex + 1);
        }
        // add readable data
        this.readableUser.map(item => {
            if (this.selectedData.findIndex(selectedData => selectedData.id === item.readableData.id) < 0) {
                this.selectedData.splice(item.index, 0, item.readableData);
            }
        });
        this.headerPermission = UserPermissionType.readable;
    }

    getSelectedData(selectedData: PagePermissionDesignationItem[]) {
        this.selectedData = selectedData.map(item => {
            if (!item.value) {
                return {
                    ...item,
                    value: this.headerPermission
                };
            }
            return item;
        });
        this.filterSelectableList();
        this.compareChange();
    }

    changePermission(data: PagePermissionDesignationItem) {
        this.selectedData.forEach(item => {
            if (item.id === data.id) {
                item.value = data.value;
            }
        });
        this.compareChange();
    }

    getPagePermissionParams(): PagePermissionParams {
        let scopes: PagePermissionScope[] = this.selectedData
            .filter(item => item.id !== ALL_USER_GROUP_ID)
            .map(item => {
                return {
                    id: item.id,
                    type: item.type,
                    value: item.value
                };
            });
        if (!this.isShowPermissionSetting) {
            scopes = null;
        } else if (this.isAllReadSpecificEdit) {
            scopes = scopes.filter(item => item.value === UserPermissionType.editable);
        }
        return {
            scope_type: PagePermissionScopeType.spaceInside,
            permission_type: this.pagePermission,
            scopes
        };
    }

    getParentPage() {
        this.parentPage = helpers.find(this.pagesStore.snapshot.entities, { _id: this.extendsParentPermissionInfo?.page_id });
    }

    confirm() {
        this.loading = true;
        const pagePermissionParams = this.getPagePermissionParams();
        this.pagePermissionSettingStore
            .savePermissionSetting(this.spaceId, this.pageId, { ...pagePermissionParams })
            .pipe(
                finalize(() => {
                    this.loading = false;
                })
            )
            .subscribe(
                () => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.settingsSuccess'));
                    this.confirmCallback.emit(pagePermissionParams);
                },
                error => {
                    this.errorService.defaultErrorHandler(error);
                }
            );
    }

    cancel() {
        this.util.dialog.close();
    }
}
