import {
    ResponseData,
    Id,
    Is,
    UtilService,
    GlobalUsersStore,
    ScopeDesignationType,
    IsMePipe,
    AppRootContext,
    MemberInfo,
    UserGroupInfo,
    StyxMemberSelectConfig,
    StyxSelectScopeItem,
    ScopeTypes,
    helpers,
    DepartmentTreeNode,
    StyxTranslateService,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { Component, computed, EventEmitter, HostBinding, inject, Input, OnInit, Output } from '@angular/core';
import { of } from 'rxjs';
import {
    getPermissionList,
    PagePermissionScopeType,
    PagePermissionType,
    SelectProperty,
    UserPermissionType
} from '@wiki/app/constants/page-sharing';
import { PagePermissionSettingStore } from '@wiki/app/stores/page-permission-setting.store';
import { ObjectiveUserGroupPipe, PageUserPermissionPipe } from '@wiki/app/pipes/page.pipe';
import { PagePermissionDesignationItem, PagePermissionParams } from '@wiki/app/entities/page-permission-setting';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { finalize } from 'rxjs/operators';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { SpaceInfo } from '@wiki/app/entities/space-info';

@Component({
    selector: 'no-space-member',
    templateUrl: './no-space-member.component.html',
    providers: [PageUserPermissionPipe, ObjectiveUserGroupPipe, IsMePipe],
    standalone: false
})
export class NoSpaceMemberComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Input() spaceId: Id;

    @Input() pageId: Id;

    @Input() shortId: Id;

    @Output() permissionChange: EventEmitter<boolean> = new EventEmitter();

    @Output() confirmCallback: EventEmitter<PagePermissionParams> = new EventEmitter();

    loading = false;

    ScopeDesignationType = ScopeDesignationType;

    pagePermission = PagePermissionType;

    selectProperty = SelectProperty;

    defaultPermission = UserPermissionType.readable;

    isIncludeChildren = Is.no;

    selectedData: PagePermissionDesignationItem[] = [];

    isSystem: Is = Is.no;

    selectableMembers: MemberInfo[] = [];

    selectableUserGroups: UserGroupInfo[] = [];

    selectableDepartments: DepartmentTreeNode[] = [];

    immutableScopes: StyxSelectScopeItem[] = [];

    originSetting: PagePermissionParams = {
        scope_type: PagePermissionScopeType.spaceOutside,
        is_include_children: Is.no,
        scopes: []
    };

    permissionList = computed(() => {
        const permission = getPermissionList(this.translate);
        return this.isSystem ? permission.filter(x => x._id !== UserPermissionType.editable) : permission;
    });

    get pageLink() {
        const urlId = this.shortId ?? this.pageId;
        return this.util.generateTeamUrl(`/wiki/pages/${urlId}`);
    }

    @HostBinding('class.no-space-member') className = true;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public util: UtilService,
        public pagePermissionSettingStore: PagePermissionSettingStore,
        public usersStore: GlobalUsersStore,
        private userPermissionPipe: PageUserPermissionPipe,
        private userGroupPipe: ObjectiveUserGroupPipe,
        private isMePipe: IsMePipe,
        public appRootContext: AppRootContext,
        private configService: StyxMemberSelectConfig,
        private pagesStore: PagesStore,
        private errorService: WikiErrorService
    ) {}

    ngOnInit() {
        this.fetchDetail();
        const page = helpers.find(this.pagesStore.snapshot.entities, { _id: this.pageId });
        if (page?.system) {
            this.isSystem = Is.yes;
        }
    }

    fetchDetail() {
        this.pagePermissionSettingStore.fetchDetail(this.spaceId, this.pageId, PagePermissionScopeType.spaceOutside).subscribe(
            (data: ResponseData) => {
                if (data.value && !helpers.isEmpty(data.value)) {
                    this.isIncludeChildren = data.value?.is_include_children;

                    const scopes = [];
                    const spaceMemberIds = this.pilotStore.snapshot.detail.members?.map(item => item.uid);
                    const spaceUserGroupIds = (this.pilotStore.snapshot.detail as SpaceInfo).members_be_user_group?.map(item => item.id);
                    const scopeIds = spaceMemberIds.concat(spaceUserGroupIds);
                    data.value.scopes.forEach(scope => {
                        if (this.isMePipe.transform(scope.id) || scopeIds.includes(scope.id)) {
                            return;
                        }
                        const scopeData = this.calculateScopeData(scope.id, scope.type);
                        if (scopeData) {
                            scopes.push({
                                ...scope,
                                data: scopeData
                            });
                        }
                    });
                    this.selectedData = [...scopes];
                    this.originSetting = data.value;
                }

                this.filterSelectableList();
            },
            error => {
                this.util.defaultErrorHandler()(error);
            }
        );
    }

    compareChange() {
        const { scope_type, is_include_children, scopes } = this.originSetting;
        const origin = { scope_type, is_include_children, scopes };
        const current = this.getPagePermissionParams();
        const isChange = !helpers.isEqual(current, origin);
        this.permissionChange.emit(isChange);
    }

    setImmutableScopes() {
        this.immutableScopes = this.usersStore.snapshot.teamUsers
            .filter(user => !this.selectableMembers.some(item => item.uid === user.uid))
            .map(item => {
                return {
                    id: item.uid,
                    type: ScopeTypes.user,
                    scope: item
                };
            });
    }

    calculateScopeData(id: string, type: ScopeDesignationType) {
        switch (type) {
            case ScopeDesignationType.user:
                return this.userPermissionPipe.transform(id, this.usersStore.snapshot.teamUsers);
            case ScopeDesignationType.userGroup:
                return this.userGroupPipe.transform(id, this.pagePermissionSettingStore.snapshot?.references?.member_groups);
            case ScopeDesignationType.department:
                const departments = this.pagePermissionSettingStore.snapshot.references.departments;
                return departments.find(item => item._id === id) || null;
        }
    }

    filterSelectedData(uid: string) {
        const { member_groups, departments } = this.pagePermissionSettingStore.snapshot?.references;
        return this.selectedData.some(item => {
            switch (item.type) {
                case ScopeDesignationType.user:
                    return item.id === uid;
                case ScopeDesignationType.userGroup:
                    const userGroups = [...this.selectableUserGroups, ...member_groups];
                    const userGroup = this.userGroupPipe.transform(item.id, userGroups);
                    return userGroup?.uids?.includes(uid);
                case ScopeDesignationType.department:
                    const departmentMembers = [...this.selectableDepartments, ...departments];
                    const departmentMember = departmentMembers.find(department => department._id === item.id) || null;
                    return departmentMember?._id === uid;
            }
        });
    }

    filterSelectableList() {
        this.filterSelectableUserGroups();
        this.filterSelectableDepartments();
        this.selectableMembers = this.usersStore.snapshot.teamUsers.filter(member => {
            return (
                !this.pilotStore.snapshot.detail.members?.some(m => m.uid === member.uid) &&
                !this.isMePipe.transform(member.uid) &&
                !this.filterSelectedData(member.uid)
            );
        });
        this.setImmutableScopes();
    }

    filterSelectableUserGroups() {
        this.configService.config.userGroupsResolve().subscribe(memberGroups => {
            this.selectableUserGroups = [...memberGroups].filter(
                userGroup => !this.selectedData.some(item => item.type === ScopeDesignationType.userGroup && item.id === userGroup._id)
            );
        });
    }

    filterSelectableDepartments() {
        this.configService.config.departmentMembersResolve().subscribe(departments => {
            this.selectableDepartments = [...departments].filter(
                department => !this.selectedData.some(item => item.type === ScopeDesignationType.department && item.id === department._id)
            );
        });
    }

    getPagePermissionParams(): PagePermissionParams {
        return {
            scope_type: PagePermissionScopeType.spaceOutside,
            is_include_children: this.isIncludeChildren ? Is.yes : Is.no,
            scopes: this.selectedData.map(scope => {
                const { data, ...rest } = scope;
                return rest;
            })
        };
    }

    onModelChange() {
        this.filterSelectableList();
        this.compareChange();
    }

    changePermission(data: PagePermissionDesignationItem) {
        this.selectedData.forEach(item => {
            if (item.id === data.id) {
                item.value = data.value;
            }
        });
        this.compareChange();
    }

    addDesignation = () => {
        setTimeout(() => {
            this.selectedData = this.selectedData.map(member => {
                if (!member.value) {
                    member.value = this.defaultPermission;
                }
                return member;
            });
        });
        return of(true);
    };

    trackBy(index: number, item) {
        return item._id || index;
    }

    confirm() {
        this.loading = true;
        const pagePermissionParams = this.getPagePermissionParams();
        this.pagePermissionSettingStore
            .savePermissionSetting(this.spaceId, this.pageId, { ...pagePermissionParams })
            .pipe(
                finalize(() => {
                    this.loading = false;
                })
            )
            .subscribe(
                () => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.settingsSuccess'));
                    this.confirmCallback.emit(pagePermissionParams);
                },
                error => {
                    this.errorService.defaultErrorHandler(error);
                }
            );
    }

    cancel() {
        this.util.dialog.close();
    }
}
