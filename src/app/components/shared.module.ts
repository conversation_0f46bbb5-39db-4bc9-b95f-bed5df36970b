import { ObserversModule } from '@angular/cdk/observers';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { RouterModule } from '@angular/router';
import { NgxStyxModule } from '@atinc/ngx-styx';
import { WikiCommonModule } from '@wiki/common/common.module';
import { NgxPlanetModule } from '@worktile/planet';
import { TheEditorComponent } from '@worktile/theia';
import { QRCodeComponent } from 'angularx-qrcode';
import { SlateModule } from 'slate-angular';
import { PageInfoComponent } from '../features/page/components';
import { PAGE_INFO_COMPONENT_TOKEN } from '../features/page/components/page-info/page-info.component.token';
import { PIPES } from '../pipes';
import { SpaceActionMenuComponent } from './action-menu/action-menu.component';
import { WikiAddPageMenuComponent } from './add-page-menu/add-page-menu.component';
import { WikiCollaboratorsListComponent } from './collaborators-list/collaborators-list.component';
import { PageCopyComponent } from './copy/copy.component';
import { PAGE_COPY_COMPONENT_TOKEN } from './copy/copy.component.token';
import { WikiCreatePageMenuComponent } from './create-page-menu/create-page-menu.component';
import { DemonstrationToolbarComponent } from './demonstration-toolbar/demonstration-toolbar.component';
import { WikiDiscussionListComponent } from './discussion/discussion-list/discussion-list.component';
import { WikiGlobalDiscussionListComponent } from './discussion/global-discussion-list/global-discussion-list.component';
import { DraggablePageSelectorComponent } from './draggable-page-selector/draggable-page-selector.component';
import { PageGuiderComponent } from './guider/page-guider/page-guider.component';
import { SpacesGuiderComponent } from './guider/spaces-guider/spaces-guider.component';
import { ListFilterViewComponent } from './list-filter-view/list-filter-view.component';
import { PageMoveComponent } from './move/move.component';
import { PAGE_MOVE_COMPONENT_TOKEN } from './move/move.component.token';
import { PageDialogComponent } from './page-dialog/page-dialog.component';
import { PAGE_DIALOG_COMPONENT_TOKEN } from './page-dialog/page-dialog.component.token';
import { EditMenuComponent } from './page-edit/edit-menu/edit-menu.component';
import { PageEditComponent } from './page-edit/edit/edit.component';
import { PageSelectorComponent } from './page-selector/page-selector.component';
import { NoSpaceMemberComponent } from './page-sharing/no-space-member/no-space-member.component';
import { PageSharingComponent } from './page-sharing/page-sharing.component';
import { SharePubliclyComponent } from './page-sharing/share-publicly/share-publicly.component';
import { SpaceMemberComponent } from './page-sharing/space-member/space-member.component';
import { PageTableComponent } from './page-table/page-table.component';
import { WikiPageTreeOperationComponent } from './page-tree-operation/page-tree-operation.component';
import { PageTypeSelectorComponent } from './page-type-selector/page-type-selector.component';
import { PropertyItemPageTypeDisplayerComponent } from './page-type/page-type.component';
import { WikiRelationPageSelectionComponent } from './relation-page-selection/relation-page-selection.component';
import { WikiRelatedResourceComponent } from './resource/related-resource.component';
import { WikiReturnToSpaceComponent } from './return-to-space/return-to-space.component';
import { SelectPageComponent } from './select-page/select-page.component';
import { SharePageListComponent } from './share-page/list.component';
import { SpaceSettingMenuComponent } from './space-setting-menu/space-setting-menu.component';
import { StencilDetailComponent } from './stencil/detail/stencil-detail.component';
import { StencilEditComponent } from './stencil/edit/stencil-edit.component';
import { StencilSideComponent } from './stencil/side/stencil-side.component';
import { TagAggregationTableComponent } from './tag-aggregation-table/tag-aggregation-table.component';
import { WikiLoadingComponent } from './widgets/loading/loading.component';
import { WikiWxCodeComponent } from './widgets/wx-code/wx-code.component';
import { WikiKnowledgeGraphComponent } from './knowledge-graph/knowledge-graph.component';
import { PlaitBoardComponent } from '@plait/angular-board';
import { WikiKnowledgeGraphNodeIconComponent } from './knowledge-graph/node-icon.component';
import { ThyStealthView } from '@tethys/cdk/dom';
import { AITableGridView } from '@ai-table/grid-view/grid.component';
import { AITableViews } from '@ai-table/components';
import { AITableCommonGrid } from '@ai-table/common-grid/common-grid';

const COMPONENTS = [
    PageSelectorComponent,
    PageTypeSelectorComponent,
    SharePageListComponent,
    ListFilterViewComponent,
    SelectPageComponent,
    WikiWxCodeComponent,
    PageDialogComponent,
    WikiLoadingComponent,
    WikiDiscussionListComponent,
    WikiGlobalDiscussionListComponent,
    PageEditComponent,
    EditMenuComponent,
    StencilEditComponent,
    StencilDetailComponent,
    SpacesGuiderComponent,
    PageGuiderComponent,
    SpaceActionMenuComponent,
    StencilSideComponent,
    WikiCollaboratorsListComponent,
    PageSharingComponent,
    NoSpaceMemberComponent,
    SpaceMemberComponent,
    SharePubliclyComponent,
    WikiReturnToSpaceComponent,
    ListFilterViewComponent,
    SpaceSettingMenuComponent,
    DemonstrationToolbarComponent,
    WikiRelatedResourceComponent,
    WikiRelationPageSelectionComponent,
    PageTableComponent,
    PropertyItemPageTypeDisplayerComponent,
    WikiPageTreeOperationComponent,
    WikiAddPageMenuComponent,
    WikiCreatePageMenuComponent,
    PageCopyComponent,
    PageMoveComponent,
    TagAggregationTableComponent,
    DraggablePageSelectorComponent,
    WikiKnowledgeGraphComponent,
    WikiKnowledgeGraphNodeIconComponent
];

@NgModule({
    imports: [
        BrowserModule,
        NgxPlanetModule,
        RouterModule,
        CommonModule,
        ScrollingModule,
        ObserversModule,
        NgxStyxModule,
        TheEditorComponent,
        QRCodeComponent,
        SlateModule,
        WikiCommonModule,
        PlaitBoardComponent,
        AITableGridView,
        AITableViews,
        AITableCommonGrid,
        ThyStealthView
    ],
    exports: [
        BrowserModule,
        CommonModule,
        NgxStyxModule,
        NgxPlanetModule,
        RouterModule,
        WikiCommonModule,
        ThyStealthView,
        ...PIPES,
        ...COMPONENTS
    ],
    declarations: [...PIPES, ...COMPONENTS],
    providers: [
        {
            provide: PAGE_DIALOG_COMPONENT_TOKEN,
            useValue: PageDialogComponent
        },
        {
            provide: PAGE_COPY_COMPONENT_TOKEN,
            useValue: PageCopyComponent
        },
        {
            provide: PAGE_MOVE_COMPONENT_TOKEN,
            useValue: PageMoveComponent
        },
        {
            provide: PAGE_INFO_COMPONENT_TOKEN,
            useValue: PageInfoComponent
        }
    ]
})
export class SharedModule {}
