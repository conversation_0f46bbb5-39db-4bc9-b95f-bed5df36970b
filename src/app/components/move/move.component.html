<form
  class="styx-pivot-creation-form"
  name="addEditParentForm"
  thyForm
  #movePageThyForm="thyForm"
  #movePageForm="ngForm"
  [thyFormValidatorConfig]="validatorConfig"
>
  <thy-dialog-header [thyTitle]="'wiki.page.move.title' | translate" styxI18nTracking></thy-dialog-header>
  <thy-dialog-body>
    <thy-alert
      class="mb-4"
      thyTheme="naked"
      thyType="danger"
      thyIcon="lightbulb"
      [thyMessage]="'wiki.page.move.alert' | translate"
      styxI18nTracking
    ></thy-alert>
    <thy-form-group>
      <ng-template #formGroup>
        <label class="form-label col-form-label wiki-operations-model-label label-required" styxI18nTracking>
          {{ 'wiki.page.move.toSpace' | translate }}
        </label>
        <div class="form-label wiki-operations-model-content">
          <styx-pilot-select
            name="toSpaceId"
            [(ngModel)]="toSpaceId"
            [styxPilots]="spaces"
            [styxPlaceholder]="'styx.selectSpace' | translate"
            (ngModelChange)="toSpaceChange($event)"
          >
          </styx-pilot-select>
        </div>
      </ng-template>
    </thy-form-group>
    <thy-form-group>
      <ng-template #formGroup>
        <label class="form-label col-form-label wiki-operations-model-label label-required" translate="wiki.page.copy.select"></label>
        <div class="form-label wiki-operations-model-content">
          <wiki-draggable-page-selector
            name="toPageId"
            required
            [isMultiple]="isMultiple"
            [operateType]="'move'"
            [pageNodes]="pageNodes()"
            [loadingDone]="loadingDone()"
            [(ngModel)]="toPagePosition"
            [operationPage]="operationPage"
          ></wiki-draggable-page-selector>
        </div>
      </ng-template>
    </thy-form-group>
  </thy-dialog-body>
  <thy-dialog-footer>
    <button thyButton="link-secondary" (click)="close()" translate="common.cancel"></button>
    <button
      thyButton="primary"
      [thyLoadingText]="'common.submitting' | translate"
      (thyFormSubmit)="save(movePageThyForm)"
      translate="common.ok"
    ></button>
  </thy-dialog-footer>
</form>
