import { ChangeDetectorRef, Component, DestroyRef, Input, OnInit, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Is, PilotEntryStore, ResponseData, StyxTranslateService, UtilService, hasPermissionTransform, helpers } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { PageApiService } from '@wiki/app/services';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { addOccupyPage } from '@wiki/app/util/tree';
import { wikiPermissionDefinition, wikiPermissionPoints } from '@wiki/common/constants/permission';
import { PageStore } from '@wiki/common/stores/page.store';
import { ThyDialog } from 'ngx-tethys/dialog';
import { ThyFormDirective } from 'ngx-tethys/form';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'wiki-page-move',
    templateUrl: 'move.component.html',
    host: {
        class: 'styx-pivot-creation'
    },
    standalone: false
})
export class PageMoveComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Input() operationPage: PageInfo | PageInfo[];

    @Input() currentSpaceId: string;

    @Input() isMultiple = false;

    @Input() moveSuccess: (
        toSpaceId: string,
        toPagePosition: { afterId: string; parentId: string; append: boolean },
        pages: PageInfo[]
    ) => void;

    public saving = false;

    public spaces: SpaceInfo[] = [];

    public pageNodes = signal<ThyTreeNodeData[] | undefined>(undefined);

    public toSpaceId = '';

    public homePageId: string;

    public toPagePosition = { afterId: '', parentId: '', append: false };

    loadingDone = signal(false);

    public currentPages: PageInfo[];

    public validatorConfig = {
        validationMessages: {
            toSpaceId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.validator.space.required')
            },
            toPageId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.validator.page.required')
            }
        }
    };

    private fetchPages$: Subscription;

    private cdr = inject(ChangeDetectorRef);
    private util = inject(UtilService);
    private thyDialog = inject(ThyDialog);
    private destroyRef = inject(DestroyRef);
    private pilotStore = inject(PilotEntryStore);
    private pagesStore = inject(PagesStore);
    private errorService = inject(WikiErrorService);
    private pageApiService = inject(PageApiService);
    private pageStore = inject(PageStore, { optional: true });

    ngOnInit() {
        this.loadSpaces();
    }

    toSpaceChange(toSpaceId: string) {
        this.toSpaceId = toSpaceId;
        this.loadPages();
    }

    save(form: ThyFormDirective) {
        if (this.saving) {
            return;
        }
        // 不拖动，单条，移动位置不变
        if (
            this.toPagePosition.afterId === '' &&
            this.toPagePosition.parentId === '' &&
            this.toSpaceId === this.currentSpaceId &&
            !this.isMultiple
        ) {
            this.close();
            return;
        }
        // 换空间或者多选不操作，位置在主页下方
        if (
            (this.toSpaceId !== this.currentSpaceId || this.isMultiple) &&
            this.toPagePosition.afterId === '' &&
            this.toPagePosition.parentId === ''
        ) {
            this.toPagePosition.afterId = this.homePageId;
        }
        const selectPages = helpers.isArray(this.operationPage) ? this.operationPage : [this.operationPage];
        this.saving = true;
        const movePage$ = this.movePages(selectPages);
        movePage$
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe({
                next: (data: ResponseData<PageInfo[]>) => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.moveSuccess'));
                    if (this.toSpaceId === this.currentSpaceId) {
                        data.value.forEach(page => {
                            this.updatePage(page);
                        });
                    } else {
                        const selectIds = selectPages.map(p => p._id);
                        this.pagesStore.pureRemovePageById(selectIds);
                    }
                    this.moveSuccess && this.moveSuccess(this.toSpaceId, this.toPagePosition, data.value);
                    this.thyDialog.closeAll();
                },
                error: error => this.errorService.defaultErrorHandler(error)
            });
    }

    updatePage(page: PageInfo) {
        const { position, parent_id, parent_ids } = page;
        this.pagesStore.update(page._id, { position, parent_id, parent_ids });
        this.pageStore?.pureUpdatePage({ position, parent_id, parent_ids });
    }

    movePages(pages: PageInfo[]) {
        const ids = pages.map(page => page._id);
        return this.pageApiService.movePages(this.currentSpaceId, this.toSpaceId, this.toPagePosition, ids);
    }

    close() {
        this.thyDialog.close();
    }

    private loadSpaces() {
        this.pilotStore
            .fetchSelectableAuthorizedEntries({
                permission_keys: [wikiPermissionDefinition.page_create.key]
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: ResponseData<SpaceInfo[]>) => {
                this.spaces = data.value;
                if (this.spaces.some(space => space._id === this.currentSpaceId)) {
                    this.toSpaceId = this.currentSpaceId;
                } else if (this.spaces.length > 0) {
                    this.toSpaceId = this.spaces[0]._id;
                }
                this.loadPages();
            });
    }

    private loadPages() {
        if (!this.toSpaceId) {
            return;
        }
        if (this.fetchPages$) {
            this.currentPages = [];
            this.pageNodes.set(undefined);
            this.fetchPages$.unsubscribe();
        }
        this.loadingDone.set(false);
        this.fetchPages$ = this.pageApiService
            .fetchPageTree(this.toSpaceId)
            .pipe(
                finalize(() => {
                    this.loadingDone.set(true);
                    this.cdr.detectChanges();
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(pages => {
                this.homePageId = pages.find(p => p.system === 1)._id;
                const selectPages = helpers.isArray(this.operationPage) ? this.operationPage : [this.operationPage];
                const selectPageIds = selectPages.map(page => page._id);
                if (this.currentSpaceId === this.toSpaceId) {
                    this.currentPages = pages;
                }
                const allPages = addOccupyPage(pages, this.currentPages, selectPages, this.isMultiple);
                const disabledPageIds = allPages
                    .filter(
                        x =>
                            x.is_visibility === Is.no ||
                            !hasPermissionTransform(x.permissions, wikiPermissionPoints, 'page_create') ||
                            selectPageIds.includes(x._id)
                    )
                    .map(x => x._id);
                const _pages = helpers.sortBy(allPages, 'position');

                const treeNodes: ThyTreeNodeData[] = (_pages || []).map(item => {
                    const isDisabled = selectPageIds.some(
                        pageId => item.parent_ids?.includes(pageId) || disabledPageIds.includes(item._id)
                    );
                    return {
                        key: item._id,
                        title: item.name,
                        ...item,
                        disabled: isDisabled
                    };
                });
                this.pageNodes.set(
                    helpers.buildTree(treeNodes, {
                        parentIdKey: 'parent_id'
                    })
                );
            });
    }
}
