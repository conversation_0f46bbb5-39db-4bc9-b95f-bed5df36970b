import { ChangeDetectorRef, Component, DestroyRef, Input, OnInit, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Is, PilotEntryStore, ResponseData, StyxTranslateService, UtilService, hasPermissionTransform, helpers } from '@atinc/ngx-styx';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { CreatePageType, OCCUPY_ID } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { PageApiService } from '@wiki/app/services';
import { WikiErrorService } from '@wiki/app/services/error.service';
import { PagesStore } from '@wiki/app/stores/pages.store';
import { buildDraggablePageTree } from '@wiki/app/util/tree';
import { wikiPermissionDefinition, wikiPermissionPoints } from '@wiki/common/constants/permission';
import { getAfterId } from '@wiki/common/util/tree';
import { ThyDialog } from 'ngx-tethys/dialog';
import { ThyFormDirective } from 'ngx-tethys/form';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { Subscription } from 'rxjs';
import { finalize } from 'rxjs/operators';

@Component({
    selector: 'wiki-page-copy',
    templateUrl: 'copy.component.html',
    host: {
        class: 'styx-pivot-creation'
    },
    standalone: false
})
export class PageCopyComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Input() operationPage: PageInfo | PageInfo[];

    @Input() showIncludeChildren = true;

    @Input() isMultiple = false;

    @Input() currentSpaceId: string;

    @Input() copySuccess: (toSpaceId: string, pages: PageInfo[]) => void;

    loadingDone = signal(false);

    public saving = false;

    public currentPages: PageInfo[];

    public spaces: SpaceInfo[] = [];

    public pageNodes = signal<ThyTreeNodeData[] | undefined>(undefined);

    public toSpaceId = '';

    public homePageId: string;

    public toPagePosition = { afterId: '', parentId: '', append: false };

    public includeChildren = false;

    public validatorConfig = {
        validationMessages: {
            toSpaceId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.page.copy.validation.spaceRequired')
            },
            toPageId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.page.copy.validation.pageRequired')
            }
        }
    };

    private fetchPages$: Subscription;

    private cdr = inject(ChangeDetectorRef);
    private util = inject(UtilService);
    private thyDialog = inject(ThyDialog);
    private destroyRef = inject(DestroyRef);
    private pilotStore = inject(PilotEntryStore);
    private pagesStore = inject(PagesStore);
    private errorService = inject(WikiErrorService);
    private pageApiService = inject(PageApiService);

    ngOnInit() {
        this.loadSpaces();
    }

    toSpaceChange(toSpaceId: string) {
        this.toSpaceId = toSpaceId;
        this.loadPages();
    }

    save(form: ThyFormDirective) {
        if (this.saving) {
            return;
        }
        // 不拖动，单条复制，位置在原目标上方
        if (
            this.toPagePosition.afterId === '' &&
            this.toPagePosition.parentId === '' &&
            this.toSpaceId === this.currentSpaceId &&
            !this.isMultiple
        ) {
            const afterId = getAfterId({ ...this.operationPage, _id: OCCUPY_ID } as PageInfo, CreatePageType.Above, this.pageNodes());
            this.toPagePosition.afterId = afterId;
            this.toPagePosition.parentId =
                this.currentPages.find(item => item._id === (this.operationPage as PageInfo)._id).parent_id ?? '';
        } else if (
            (this.toSpaceId !== this.currentSpaceId || this.isMultiple) &&
            this.toPagePosition.afterId === '' &&
            this.toPagePosition.parentId === ''
        ) {
            // 换空间或者多选不操作，位置在主页下方
            this.toPagePosition.afterId = this.homePageId;
        }

        this.saving = true;

        const selectPages = helpers.isArray(this.operationPage) ? this.operationPage : [this.operationPage];
        const copyPage$ = this.isMultiple ? this.copyPages(selectPages) : this.copyPage(selectPages[0]);
        copyPage$
            .pipe(
                finalize(() => {
                    this.saving = false;
                })
            )
            .subscribe({
                next: (data: ResponseData) => {
                    this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>('common.copySuccess'));
                    if (this.toSpaceId === this.currentSpaceId) {
                        this.pagesStore.pureAddPages(data.value);
                    }
                    this.copySuccess && this.copySuccess(this.toSpaceId, data.value);
                    this.thyDialog.closeAll();
                },
                error: error => this.errorService.defaultErrorHandler(error)
            });
    }

    copyPage(page: PageInfo) {
        return this.pageApiService.copyPage(page._id, this.toSpaceId, this.toPagePosition, this.includeChildren ? Is.yes : Is.no);
    }

    copyPages(pages: PageInfo[]) {
        const ids = pages.map(page => page._id);
        return this.pageApiService.copyPages(this.currentSpaceId, this.toSpaceId, this.toPagePosition, ids);
    }

    close() {
        this.thyDialog.close();
    }

    private loadSpaces() {
        this.pilotStore
            .fetchSelectableAuthorizedEntries({
                permission_keys: [wikiPermissionDefinition.page_create.key]
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: ResponseData<SpaceInfo[]>) => {
                this.spaces = data.value;
                if (this.spaces.some(space => space._id === this.currentSpaceId)) {
                    this.toSpaceId = this.currentSpaceId;
                } else if (this.spaces.length > 0) {
                    this.toSpaceId = this.spaces[0]._id;
                }
                this.loadPages();
            });
    }

    private loadPages() {
        if (!this.toSpaceId) {
            return;
        }
        if (this.fetchPages$) {
            this.currentPages = [];
            this.pageNodes.set(undefined);
            this.fetchPages$.unsubscribe();
        }
        this.loadingDone.set(false);
        this.fetchPages$ = this.pageApiService
            .fetchPageTree(this.toSpaceId)
            .pipe(
                finalize(() => {
                    this.loadingDone.set(true);
                    this.cdr.detectChanges();
                }),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(pages => {
                this.homePageId = pages.find(p => p.system === 1)._id;

                const disabledPageIds = pages
                    .filter(x => x.is_visibility === Is.no || !hasPermissionTransform(x.permissions, wikiPermissionPoints, 'page_create'))
                    .map(x => x._id);

                if (this.currentSpaceId === this.toSpaceId) {
                    this.currentPages = pages;
                }
                this.pageNodes.set(
                    buildDraggablePageTree(
                        pages,
                        disabledPageIds,
                        false,
                        false,
                        this.isMultiple,
                        helpers.isArray(this.operationPage) ? this.operationPage : [this.operationPage],
                        this.currentPages
                    )
                );
            });
    }
}
