<form
  class="styx-pivot-creation-form"
  thyForm
  name="addEditParentForm"
  #copyPageThyForm="thyForm"
  #copyPageForm="ngForm"
  [thyFormValidatorConfig]="validatorConfig"
>
  <thy-dialog-header styxI18nTracking [thyTitle]="'wiki.page.copy.title' | translate"> </thy-dialog-header>
  <thy-dialog-body>
    <thy-alert
      class="mb-4"
      thyTheme="naked"
      thyType="danger"
      thyIcon="lightbulb"
      styxI18nTracking
      [thyMessage]="isMultiple ? ('wiki.page.copy.batchHint' | translate) : ('wiki.page.copy.hint' | translate)"
    ></thy-alert>
    <thy-form-group styxI18nTracking [thyLabelText]="'wiki.page.copy.toSpace' | translate" thyLabelRequired>
      <styx-pilot-select
        name="toSpaceId"
        [(ngModel)]="toSpaceId"
        [styxPlaceholder]="'styx.selectSpace' | translate"
        (ngModelChange)="toSpaceChange($event)"
      >
      </styx-pilot-select>
    </thy-form-group>
    <thy-form-group styxI18nTracking [thyLabelText]="'wiki.page.copy.select' | translate" thyLabelRequired>
      <wiki-draggable-page-selector
        name="toPageId"
        required
        [isMultiple]="isMultiple"
        [operateType]="'copy'"
        [pageNodes]="pageNodes()"
        [loadingDone]="loadingDone()"
        [(ngModel)]="toPagePosition"
        [includeChildren]="includeChildren"
        [operationPage]="operationPage"
      ></wiki-draggable-page-selector>
    </thy-form-group>
    @if (showIncludeChildren) {
      <label
        name="clear"
        thyCheckbox
        [(ngModel)]="includeChildren"
        styxI18nTracking
        [thyLabelText]="'wiki.page.copy.withChildren' | translate"
      ></label>
    }
  </thy-dialog-body>
  <thy-dialog-footer>
    <button thyButton="link-secondary" (click)="close()" translate="common.cancel"></button>
    <button
      thyButton="primary"
      [thyLoading]="saving"
      [thyLoadingText]="'common.submitting' | translate"
      (thyFormSubmit)="save(copyPageThyForm)"
      translate="common.ok"
    ></button>
  </thy-dialog-footer>
</form>
