import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { PilotEntryStore, ResponseData } from '@atinc/ngx-styx';
import { StyxRelationSelectionStore, StyxRelationSelectionSuiteInfo } from '@atinc/ngx-styx/relation';
import { PageTreeQueryScene } from '@wiki/app/constants/page';
import { PageInfo, PageSelectionConfig, PageSelectionFrom } from '@wiki/app/entities/page-info';
import type { SpaceInfo } from '@wiki/app/entities/space-info';
import { PageApiService, SpaceApiService } from '@wiki/app/services';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { SafeAny } from 'ngx-tethys/types';
import { map } from 'rxjs/operators';

@Injectable()
export class WikiRelationPageSelectionStore extends StyxRelationSelectionStore {
    private pilotStore = inject(PilotEntryStore);

    constructor(
        public http: HttpClient,
        public spaceApiService: SpaceApiService,
        public pageApiService: PageApiService
    ) {
        super(http);
    }

    suites: ThyTreeNodeData<StyxRelationSelectionSuiteInfo>;

    queryParams: SafeAny;

    config: PageSelectionConfig;

    initializeConfig = (config: PageSelectionConfig) => {
        this.config = config;
    };

    setViewParams(params: SafeAny) {
        this.queryParams = { ...this.queryParams, ...params };
    }

    fetchSelectablePilots = (keywords?: string) => {
        if (this.config?.from === PageSelectionFrom.baseline) {
            return this.fetchBaselinePilots(this.config.id);
        } else {
            // TODO #WIK-18611 分页
            return this.pilotStore.fetchEntries({}, null).pipe(
                map((data: ResponseData<SpaceInfo[]>) => {
                    this.suites = data.value;
                    return data.value;
                })
            );
        }
    };

    fetchSelectableItems = (params?: SafeAny) => {
        const spaceId = params.view.pilot_ids[0];
        if (this.config?.from === PageSelectionFrom.baseline) {
            return this.fetchBaselineSelectableItems(spaceId, this.config.baselineId);
        } else {
            return this.fetchPageItems(spaceId);
        }
    };

    fetchPageItems(spaceId: string) {
        return this.pageApiService.fetchPageTree(spaceId, { scene: PageTreeQueryScene.table }).pipe(
            map((data: PageInfo[]) => {
                this.initialize(data);
                return data;
            })
        );
    }

    fetchBaselinePilots(idOrIdentifier: string) {
        return this.spaceApiService.fetchSpace(idOrIdentifier).pipe(
            map((data: ResponseData) => {
                this.suites = [data.value];
                return [data.value];
            })
        );
    }

    fetchBaselineSelectableItems(idOrIdentifier: string, baselineId: string) {
        return this.pageApiService.fetchBaselinePageTree(idOrIdentifier, baselineId).pipe(
            map((data: PageInfo[]) => {
                this.initialize(data);
                return data;
            })
        );
    }
}
