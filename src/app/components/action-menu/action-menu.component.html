@if (!isHeaderActive) {
  <a
    href="javascript:;"
    thyAction
    thyActionIcon="more-vertical"
    styxI18nTracking
    [thyTooltip]="'common.more' | translate"
    [ngClass]="{ active: isActive() }"
    (click)="openActionMenu($event)"
  ></a>
}
@if (isHeaderActive && currentSpace?.permissions | hasPermission: 'page_create') {
  <a
    href="javascript:;"
    class="space-page-create"
    thyGuiderTarget="createPage"
    [ngClass]="{ active: isActive() }"
    (click)="openActionMenu($event)"
  >
    <thy-icon thyIconName="plus-circle-fill"></thy-icon>
  </a>
}

<ng-template #menu>
  <div class="thy-dropdown-menu">
    @if (page()?._id !== homePage?._id && isHeaderActive) {
      <ng-template [ngTemplateOutlet]="addPageMenu"></ng-template>
    }
    @if (page()?._id !== homePage?._id && !isHeaderActive) {
      @for (option of addPageOptions; track option.type) {
        <a
          href="javascript:;"
          thyPlacement="rightTop"
          thyActiveClass="action-menu-active"
          thyTrigger="hover"
          thyDropdownMenuItem
          [thyDropdown]="expand"
          [thyDisabled]="!(getVerifyPermissionNode(option.type)?.permissions | hasPermission: 'page_create')"
        >
          @if (option.icon) {
            <thy-icon thyDropdownMenuItemIcon [thyIconName]="option.icon" class="{{ 'add-page-' + option.icon }}"></thy-icon>
          }
          <span thyDropdownMenuItemName>{{ option.typeName }}</span>
          <thy-icon thyDropdownMenuItemExtendIcon thyIconName="angle-right"></thy-icon>
          <thy-dropdown-menu #expand>
            <ng-template
              [ngTemplateOutlet]="addPageMenu"
              [ngTemplateOutletContext]="{ $implicit: { createType: option.type } }"
            ></ng-template>
          </thy-dropdown-menu>
        </a>
      }
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
    }
    @if (!isHeaderActive) {
      @if (page | isPage) {
        <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
        <a thyDropdownMenuItem href="javascript:;" (click)="newWindowOpen()">
          <thy-icon thyDropdownMenuItemIcon thyIconName="publish"></thy-icon>
          <span thyDropdownMenuItemName translate="styx.openNew"></span>
        </a>
      }
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
      <a thyDropdownMenuItem href="javascript:;" [thyDisabled]="!sharingPermission" (click)="onAction('share')">
        <thy-icon thyDropdownMenuItemIcon thyIconName="share"></thy-icon>
        <span thyDropdownMenuItemName translate="wiki.page.sharing.title"></span>
      </a>
      <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
      <a
        thyDropdownMenuItem
        href="javascript:;"
        [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_edit')"
        (click)="onAction('rename')"
      >
        <thy-icon thyDropdownMenuItemIcon thyIconName="rename"></thy-icon>
        <span thyDropdownMenuItemName translate="common.rename"></span>
      </a>
      <a
        thyDropdownMenuItem
        thyType="danger"
        href="javascript:;"
        [thyDisabled]="!(page()?.permissions | hasPermission: 'page_delete')"
        (click)="deletePage()"
      >
        <thy-icon thyDropdownMenuItemIcon thyIconName="trash"></thy-icon>
        <span thyDropdownMenuItemName translate="common.delete"></span>
      </a>
    }
  </div>
</ng-template>

<ng-template #addPageMenu let-param>
  @for (menu of pageMenus; track menu.type) {
    <a
      href="javascript:;"
      thyDropdownMenuItem
      [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_create')"
      (click)="addNewPage(param?.createType, menu.type)"
    >
      <styx-awesome-text
        class="ml-n1"
        [styxText]="menu.name"
        [styxIcon]="menu.type | pageIcon"
        [styxIconColor]="menu.type | pageIconColor"
      ></styx-awesome-text>
    </a>
  }
  @if (page()?._id !== homePage?._id) {
    <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
    <a
      href="javascript:;"
      thyDropdownMenuItem
      [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_create')"
      (click)="addNewGroup(param?.createType)"
    >
      <styx-awesome-text
        class="ml-n1"
        styxI18nTracking
        [styxText]="'wiki.action.newGroup' | translate"
        [styxIcon]="2 | pageIcon"
        [styxIconColor]="2 | pageIconColor"
      ></styx-awesome-text>
    </a>
    <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
    <a
      href="javascript:;"
      thyDropdownMenuItem
      [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_create')"
      (click)="openAIWriting(param?.createType)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="ai-star"></thy-icon>
      <span thyDropdownMenuItemName translate="styx.aiWriting"></span>
    </a>
    <a
      href="javascript:;"
      thyDropdownMenuItem
      [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_create')"
      (click)="onAction('stencil', param?.createType)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="magic"></thy-icon>
      <span thyDropdownMenuItemName translate="wiki.action.newFromTemplate"></span>
    </a>
    <a
      href="javascript:;"
      thyDropdownMenuItem
      [thyDisabled]="!(menuOrigin()?.permissions | hasPermission: 'page_create')"
      (click)="openFileImportDialog(param?.createType)"
    >
      <thy-icon thyDropdownMenuItemIcon thyIconName="file-import"></thy-icon>
      <span thyDropdownMenuItemName translate="common.import"></span>
    </a>
  }
</ng-template>
