import {
    Component,
    computed,
    effect,
    ElementRef,
    EventEmitter,
    inject,
    input,
    Input,
    NgZone,
    OnInit,
    output,
    Output,
    signal,
    TemplateRef,
    ViewChild,
    ViewContainerRef
} from '@angular/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { HasPermissionPipe, PilotEntryStore, StyxTranslateService } from '@atinc/ngx-styx';
import { ActivatedRoute, Router } from '@angular/router';
import {
    AIWritingAssistant,
    AIWritingAssistantPayload,
    DriveFileIconUrlPipe,
    FileSizeDisplayPipe,
    helpers,
    StyxAIAssistantService
} from '@atinc/ngx-styx';
import { marked } from 'marked';
import { UploadPageOption } from '@wiki/app/entities/file-import';
import { FileImportComponent } from '@wiki/app/features/page/components';
import { FileImportService } from '@wiki/app/services/file-import.service';
import { CommonEditingPageStore } from '@wiki/common/stores/editing-page.store';
import { getDraftGroup } from '@wiki/common/util/create-page';
import { getDefaultPageTitle } from '@wiki/common/util/get-default-page-title';
import { getAfterId } from '@wiki/common/util/tree';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyTree, ThyTreeNode, ThyTreeNodeData } from 'ngx-tethys/tree';
import { take } from 'rxjs/operators';
import { CreatePageType, EditingPageFrom, getAddPageOptions, PageTypes } from '../../constants/page';
import { DraftInfo, PageInfo } from '../../entities/page-info';
import { SpaceInfo } from '../../entities/space-info';
import { WikiErrorService } from '../../services/error.service';
import { PageService } from '../../services/util/page.service';
import { PagesStore } from '../../stores/pages.store';
import { of, fromEvent } from 'rxjs';
import { Element } from 'slate';
import { PlaitElement } from '@plait/core';
import { TheiaConverter } from '@atinc/selene';
import { PageTreeService } from '@wiki/app/services/page-tree.service';
export type ActionType = 'rename' | 'delete' | 'add' | 'stencil' | 'import' | 'share';

export class addPageOption {
    type: CreatePageType;
    typeName: string;
    icon?: string;
}

@Component({
    selector: 'wiki-space-action-menu',
    templateUrl: './action-menu.component.html',
    providers: [DriveFileIconUrlPipe, FileSizeDisplayPipe, FileImportService, HasPermissionPipe],
    standalone: false
})
export class SpaceActionMenuComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @ViewChild('menu', { read: TemplateRef, static: true })
    actionMenuComponent: TemplateRef<any>;

    @Input() isHeaderActive: boolean;

    @Input() spaceId: string;

    page = input<PageInfo | undefined>(undefined);

    @Input() set pageNodes(pages: ThyTreeNodeData[]) {
        this._pageNodes = [...pages];
    }
    get pageNodes() {
        return this._pageNodes;
    }

    pageId = input<string | null>(null);

    @Input() tree: ThyTree;

    isHome = input<boolean>(false);

    @Input() treeNodeNameEdit: (data: ThyTreeNode) => {};

    @Output() selectImportPage: EventEmitter<string> = new EventEmitter();

    openPageSharing = output<PageInfo>();

    _pageNodes: ThyTreeNodeData[];

    get addPageOptions(): addPageOption[] {
        return getAddPageOptions(this.translate);
    }

    isActiveMenuActive = signal(false);

    isAIWritingActive = signal(false);

    isActive = computed(() => {
        return this.isActiveMenuActive() || this.isAIWritingActive();
    });

    isDraft = signal(false);

    pageMenus = [
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.action.newDoc'),
            type: PageTypes.document
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.action.newTable'),
            type: PageTypes.table
        },
        {
            name: this.translate.instant<I18nSourceDefinitionType>('wiki.action.newBoard'),
            type: PageTypes.board
        }
    ];

    public get homePage() {
        return this.pagesStore.snapshot.homePage;
    }

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    get spaceScopeType() {
        return this.pilotStore.snapshot.detail.scope_type;
    }

    get sharingPermission() {
        const page = this.page();
        const hasSharingPermission = this.hasPermission.transform(page.permissions, 'page_sharing_setting');
        if (page.type === PageTypes.group && hasSharingPermission) {
            return this.hasChildrenSharingPermission();
        }
        return hasSharingPermission;
    }

    menuOrigin = computed(() => {
        if (this.isHome() || this.isDraft()) {
            return this.pilotStore.snapshot.detail;
        } else {
            return this.page() ? this.page() : helpers.find(this.pagesStore.state().entities, { _id: this.pageId() });
        }
    });

    aiAssistantService = inject(StyxAIAssistantService);

    private hasPermission = inject(HasPermissionPipe);

    private pilotStore = inject(PilotEntryStore);

    origin?: HTMLElement;

    constructor(
        private popover: ThyPopover,
        private pagesStore: PagesStore,
        private pageService: PageService,
        private route: ActivatedRoute,
        private router: Router,
        private editingPageStore: CommonEditingPageStore,
        private errorService: WikiErrorService,
        private elementRef: ElementRef,
        private thyDialog: ThyDialog,
        private viewContainerRef: ViewContainerRef,
        private fileImportService: FileImportService,
        private pageTreeService: PageTreeService,
        private ngZone: NgZone
    ) {
        effect(() => {
            this.updateNodeWrapperClass();
        });
    }

    ngOnInit() {
        this.fileImportService.initialize();
    }

    openFileImportDialog(createPageType?: CreatePageType) {
        const popoverRef = this.thyDialog.open(FileImportComponent, {
            size: ThyDialogSizes.lg,
            viewContainerRef: this.viewContainerRef
        });

        popoverRef?.componentInstance.selectFiles.pipe(take(1)).subscribe((option: { files: File[]; uploadPageInfo: UploadPageOption }) => {
            const parentId = this.getParentId(createPageType);
            this.fileImportService.uploadPageFile(
                this.currentSpace.identifier,
                parentId,
                option.files,
                option.uploadPageInfo,
                (pageId: string) => {
                    this.selectImportPage.emit(pageId);
                },
                this.getAfterId(createPageType)
            );
            popoverRef.close();
        });
    }

    getParentId(pageType?: CreatePageType) {
        if (!pageType || pageType === CreatePageType.Child) {
            if (this.isHome() || (this.isDraft() && !this.page())) {
                return null;
            } else {
                return this.page() ? this.page()._id : this.pageId();
            }
        } else {
            return this.page()?.parent_id;
        }
    }

    getVerifyPermissionNode(pageType: CreatePageType): PageInfo | SpaceInfo {
        let verifyPermissionNode: PageInfo | SpaceInfo;
        if (pageType === CreatePageType.Child) {
            verifyPermissionNode = this.page();
        } else {
            const parentId = this.getParentId(pageType);
            if (parentId) {
                verifyPermissionNode = helpers.find(this.pagesStore.snapshot.entities, { _id: parentId });
            } else {
                verifyPermissionNode = this.currentSpace;
            }
        }
        return verifyPermissionNode;
    }

    hasChildrenSharingPermission(): boolean {
        const children = this.page()?.children;
        if (children?.length > 0) {
            return children.some(child => {
                return this.hasPermission.transform(child.permissions, 'page_sharing_setting');
            });
        }
        return false;
    }

    addNewPage(createPageType?: CreatePageType, pageType = PageTypes.document, content?: Element[] | PlaitElement[]) {
        let newPage: DraftInfo = {
            name: getDefaultPageTitle(pageType, this.translate),
            parent_id: this.getParentId(createPageType),
            type: pageType
        };
        if (content) {
            newPage.content = content;
        }
        if (createPageType && createPageType !== CreatePageType.Child) {
            const afterId = getAfterId(this.page(), createPageType, this.pageNodes);
            newPage = {
                ...newPage,
                after_id: afterId,
                append_when_nil_after_id: afterId ? true : false
            };
        }
        this.editingPageStore.createPage(this.spaceId, newPage, EditingPageFrom.create, this.router.url).subscribe({
            next: data => {
                const id = data.value.short_id ?? data.value._id;
                if (data.value.type !== PageTypes.table) {
                    this.router.navigate(['/wiki/spaces/', this.currentSpace.identifier, 'draft-pages', id, 'edit']);
                } else {
                    this.pagesStore.pureAddPages(data.value);
                    this.pageTreeService?.updatePageNodes(this.pagesStore.entities());
                    this.router.navigate(['/wiki/spaces/', this.currentSpace.identifier, 'pages', id]);
                }
            },
            error: error => {
                this.errorService.defaultErrorHandler(error);
            }
        });
    }

    openAIWriting(createPageType?: CreatePageType, pageType = PageTypes.document) {
        const subscription = this.aiAssistantService.aiAssistantIsOpen$.subscribe(state => {
            this.isAIWritingActive.set(state);
            const isClosed = state === false;
            if (isClosed) {
                subscription.unsubscribe();
            }
        });
        const payload: AIWritingAssistantPayload = {
            origin: this.origin,
            key: AIWritingAssistant.helpMeWrite,
            action: (content?: string) => {
                const html = marked(content.replace(/\n```/g, '\n\n```'), { gfm: true }) as string;
                const htmlDom = new DOMParser().parseFromString(html, 'text/html');
                const pageContent = TheiaConverter.convertToTheia(Array.from(htmlDom.body.children));
                this.addNewPage(createPageType, PageTypes.document, pageContent);
                return of(true);
            }
        };
        this.aiAssistantService.open(payload);
    }

    addNewGroup(createPageType?: CreatePageType) {
        createPageType = createPageType ?? CreatePageType.Child;
        const parentId = this.getParentId(createPageType);
        const draftGroup = getDraftGroup(this.page(), this.pageNodes, createPageType, parentId);
        this.pagesStore.addDraftGroup(draftGroup);
    }

    onAction(type: ActionType, createPageType?: CreatePageType) {
        this.popover.close();
        switch (type) {
            case 'rename':
                if (this.page) {
                    (this.elementRef.nativeElement.closest('.thy-tree-node') as HTMLElement).classList.add('rename-mode');
                    this.treeNodeNameEdit(this.page() as ThyTreeNode);
                }
                break;
            case 'add':
                if (!this.isHeaderActive) return;
                this.addNewPage();
                break;
            case 'stencil':
                const parentId = this.getParentId(createPageType);
                const afterId = this.getAfterId(createPageType);
                this.pageService.openStencilDetail(undefined, 'page-create', this.viewContainerRef, parentId, afterId);
                break;
            case 'share':
                this.openPageSharing.emit(this.page());
                break;
        }
    }

    deletePage() {
        const selectedPage = this.pagesStore.getState().entities.filter(pageInfo => pageInfo._id === this.pageId())[0];
        const isToPage = this.page()._id === this.pageId() || selectedPage?.parent_ids.includes(this.page()._id);
        const spaceIdentifier = this.currentSpace.identifier;
        this.pageService.deletePage(spaceIdentifier, this.page(), isToPage);
    }

    updateNodeWrapperClass() {
        const moreClass = 'more-active';
        const wrapDOM = this.origin?.closest('.thy-tree-node-wrapper');
        if (this.isAIWritingActive() || this.isActiveMenuActive()) {
            if (wrapDOM) {
                wrapDOM.classList.add(moreClass);
            }
        } else {
            if (wrapDOM) {
                wrapDOM.classList.remove(moreClass);
            }
        }
    }

    openActionMenu(event: Event) {
        if (this.popover) {
            this.popover.close();
        }
        if (this.route.snapshot.firstChild) {
            this.isDraft.set(this.route.snapshot.firstChild.routeConfig.path.includes('draft'));
        }
        this.origin = event.currentTarget as HTMLElement;
        const popoverRef = this.popover.open(this.actionMenuComponent, {
            origin: this.origin,
            hasBackdrop: false,
            outsideClosable: true,
            insideClosable: true,
            manualClosure: true,
            offset: this.isHeaderActive ? 9 : 0,
            placement: 'bottomLeft',
            originActiveClass: undefined
        });
        this.isActiveMenuActive.set(true);

        if (popoverRef) {
            popoverRef.afterClosed().subscribe(() => {
                this.isActiveMenuActive.set(false);
            });
        }

        this.addMouseUpListener(popoverRef);
    }

    addMouseUpListener(popoverRef?: ThyPopoverRef<any>) {
        this.ngZone.runOutsideAngular(() => {
            fromEvent<MouseEvent>(document, 'mouseup')
                .pipe(take(1))
                .subscribe(() => {
                    this.ngZone.run(() => {
                        if (popoverRef) {
                            popoverRef.close();
                        }
                    });
                });
        });
    }

    newWindowOpen() {
        const id = this.page().short_id ?? this.page()._id;
        this.pageService.newWindowOpenPage(this.currentSpace.identifier, id);
    }

    private getAfterId(createPageType?: CreatePageType): string | null | undefined {
        let afterId: string | null | undefined = undefined;
        if (createPageType && createPageType !== CreatePageType.Child) {
            afterId = getAfterId(this.page(), createPageType, this.pageNodes);
        }
        return afterId;
    }
}
