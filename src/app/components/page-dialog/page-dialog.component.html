@if (loadingDone) {
  <thy-layout
    class="w-100 dialog-layout"
    [ngClass]="{ 'has-page-tree-sidebar': hasPageTreeSidebar && showPageTree, 'has-commentSidebar': commentsSidebarFixed() }"
  >
    <thy-header class="page-dialog-header fullscreen-hidden" styxPrimaryNavContainer>
      <ng-template #headerContent>
        @if (page) {
          <wiki-common-page-bread
            [currentPage]="page"
            [pages]="pageStore.snapshot.parentPages"
            [isDraftPage]="false"
            [isDisabledClick]="page?.is_deleted"
            [isShowSpaceName]="true"
            [space]="pageStore.snapshot.space"
            (selectItem)="breadcrumbSelect($event)"
          >
          </wiki-common-page-bread>
        }
      </ng-template>
      <ng-template #headerOperation>
        @if (page) {
          @if (reviewStateOptions | findOne: { _id: page?.review_result_state }; as state) {
            <div class="d-inline-block review-state">
              <thy-tag thyTheme="weak-fill" thyColor="default">
                <thy-icon [thyIconName]="state.icon" [style.color]="state.color"></thy-icon>
                {{ state.text }}
              </thy-tag>
              <thy-divider class="ml-4 mr-2" [thyVertical]="true" thyColor="light"></thy-divider>
            </div>
          }
          @if (!isDisableEdit && !page.is_deleted && (page.permissions | hasPermission: 'page_edit') && !(page | isTable)) {
            <a
              class="mr-1"
              href="javascript:;"
              thyAction
              [thyTooltip]="'wiki.page.dialog.edit' | translate"
              thyGuiderTarget="editPage"
              thyActionIcon="edit"
              thyTooltipPlacement="bottom"
              (click)="editPage()"
              styxI18nTracking
            >
              {{ 'wiki.page.dialog.edit' | translate }}
            </a>
          }
          <a
            thyAction
            class="mr-2"
            thyIcon="ai-star"
            [thyTooltip]="'styx.askPinCo' | translate"
            href="javascript:;"
            (click)="openAI()"
            styxI18nTracking
          ></a>
          <styx-shortcut class="mr-2" [styxEntityId]="page._id" styxEntityType="page" [styxShortcutAdditions]="shortcutAdditions" #shortcut>
            <a
              thyAction
              href="javascript:;"
              [thyTooltip]="(shortcut.isInShortcuts ? 'wiki.page.dialog.cancelMinimize' : 'wiki.page.dialog.minimize') | translate"
              thyTooltipPlacement="bottom"
              styxI18nTracking
            >
              <thy-icon [ngClass]="{ 'text-primary': shortcut.isInShortcuts }" thyIconName="underline-pushpin"></thy-icon>
            </a>
          </styx-shortcut>
          @if (page | isDocument) {
            <a
              thyAction
              class="mr-2"
              thyActionIcon="fullscreen"
              styxI18nTracking
              [thyTooltip]="'wiki.page.dialog.present' | translate: { key: CONTROL_KEY }"
              [thyTooltipDisabled]="isFullScreen"
              thyTooltipPlacement="bottom"
              (click)="openFullScreen()"
              href="javascript:;"
            ></a>
          }
          <a
            thyAction
            class="mr-2"
            href="javascript:;"
            thyActionIcon="link-insert"
            thyTooltipPlacement="bottom"
            thyCopy
            [thyCopyContent]="pageLink"
            [thyCopyTips]="'styx.copyLink' | translate"
            styxI18nTracking
          ></a>
          @if (!page.is_deleted) {
            <a
              thyAction
              class="mr-2"
              href="javascript:;"
              thyActionIcon="publish"
              [thyTooltip]="'wiki.page.dialog.openInSpace' | translate"
              thyTooltipPlacement="bottom"
              (click)="openInSpace()"
              styxI18nTracking
            ></a>
          }
          <ng-container>
            <a
              thyAction
              class="action-with-badge"
              href="javascript:;"
              thyTooltipPlacement="bottom"
              styxI18nTracking
              [thyTooltip]="commentTooltip()"
              (click)="toggleSidebarFixed()"
            >
              <thy-icon [ngClass]="{ 'text-primary': commentsSidebarFixed() }" thyIconName="comment"> </thy-icon>
              @if (page.comments?.length && !commentsSidebarFixed()) {
                <thy-badge
                  class="ml-2"
                  thyType="secondary"
                  thyBackgroundColor="#f3f3f3"
                  [thyCount]="page.comments.length ?? 0"
                  [thyMaxCount]="99"
                ></thy-badge>
              }
            </a>
          </ng-container>
          @if (!commentsSidebarFixed()) {
            <thy-divider class="mx-2" [thyVertical]="true" thyColor="light"></thy-divider>
            <a thyAction thyActionIcon="close" href="javascript:;" (click)="close()"></a>
          }
        }
        @if (!page) {
          <a thyAction thyActionIcon="close" href="javascript:;" (click)="close()"></a>
        }
      </ng-template>
    </thy-header>
    @if (page | isDocument) {
      <styx-entity-detail-header class="fullscreen-header">
        <ng-template #headerMain>
          <wiki-common-fullscreen-header [editor]="editor()"></wiki-common-fullscreen-header>
        </ng-template>
      </styx-entity-detail-header>
    }
    @if (page) {
      @if (page | isDocument) {
        <ng-template *ngTemplateOutlet="documentContent"></ng-template>
      }
      @if (page | isBoard) {
        <ng-template *ngTemplateOutlet="boardContent"></ng-template>
      }
      @if (page | isTable) {
        <ng-template *ngTemplateOutlet="tableContent"></ng-template>
      }
    } @else {
      <thy-content>
        <thy-empty [thyContainer]="elementRef" thyTopAuto thySize="lg" [thyMessage]="emptyDocumentText"></thy-empty>
      </thy-content>
    }
    @if (showPageTree) {
      <div class="page-tree-sidebar fullscreen-hidden">
        <button
          class="page-tree-sidebar-action"
          thyButtonIcon="bars"
          thyShape="circle-solid"
          thySize="xs"
          (mouseenter)="enterButton(tree)"
          (mouseleave)="leaveButton()"
        ></button>
      </div>
    }
    @if (page | isDocument) {
      <wiki-common-toc
        type="read"
        class="fullscreen-hidden"
        [editor]="editor()"
        [value]="editor()?.children"
        [title]="page?.name"
        [ngClass]="{ 'd-none': headers().length === 0 }"
        [container]="'.page-dialog-content-scroll-container'"
        (collapsedChange)="tocCollapsed.set($event)"
        (headersChange)="headers.set($event)"
      >
      </wiki-common-toc>
    }
  </thy-layout>
  @if (page) {
    <div
      class="app-comments-siderbar-container fullscreen-hidden"
      [ngClass]="{ expand: commentsSidebarFixed(), collapsed: !commentsSidebarFixed() }"
    >
      <styx-entity-detail-sidebar
        styxIsolated="true"
        [styxIsHidden]="!commentsSidebarFixed()"
        [styxConfig]="sidebarConfig"
        [styxMentionOptions]="{
          userGroup: true
        }"
        [styxEntityId]="page._id"
        [styxScrollToComment]="commentId"
        [styxCustomTabs]="appendTabs"
        (styxClose)="close()"
        (styxParticipantsChange)="onParticipantsChange($event)"
      >
      </styx-entity-detail-sidebar>
    </div>
  }
} @else {
  <thy-loading [thyDone]="loadingDone"></thy-loading>
}

<ng-template #documentContent>
  <thy-content
    page-dialog-fullscreen-target
    cdkScrollable
    class="page-fullscreen-container page-dialog-content-scroll-container wiki-fullscreen-prevent-scroll wiki-preview-fullscreen wiki-editor-scroll-container"
  >
    <div
      class="page-dialog-content-container page-fullscreen-content-container preview-fullscreen-content-container page-dialog-document height-100"
      [ngClass]="{ 'toc-expanded': !tocCollapsed(), 'toc-none': headers().length === 0 }"
    >
      @if (hasPageDeleted) {
        <thy-alert
          class="mb-4"
          thyType="warning-weak"
          styxI18nTracking
          thyMessage="{{
            'wiki.page.delete.byUser'
              | translate: { display_name: (page.deleted_by | user)?.display_name, time: page.deleted_at | dateTimeAutoFormat }
          }}"
        >
        </thy-alert>
      }
      @if (page | pageTagsVisibility: versionId) {
        <wiki-page-tags
          [page]="page"
          [addable]="!hasPageDeleted && !versionId"
          [removable]="!hasPageDeleted && !versionId"
          [clickSelectable]="!hasPageDeleted"
        ></wiki-page-tags>
      }

      <div class="d-flex justify-content-between">
        <h1 class="common-page-big-title fullscreen-hidden">
          @if (page.emoji_icon) {
            <styx-emoji [styxSize]="32" class="mr-2" [styxCode]="page.emoji_icon"> </styx-emoji>
          }
          {{ page?.name }}
        </h1>
        <div class="view-port-action mt-1 d-flex align-items-center fullscreen-hidden">
          <a
            thyAction
            href="javascript:;"
            styxI18nTracking
            [thyTooltip]="isMaxView ? ('wiki.document.emoji.standardMode' | translate) : ('wiki.page.operation.fullWidth' | translate)"
            [thyActionIcon]="isMaxView ? 'min-view' : 'max-view'"
            (click)="updateViewport()"
          ></a>
        </div>
      </div>
      <wiki-common-preview
        pageContentContainer="page-dialog-content-container"
        [value]="pageContent"
        [isFullScreen]="isFullScreen"
        [isDecoration]="!isFullScreen"
        [openDiscussion]="openDiscussion"
        [addRelationResource]="addRelationResource"
        [hasEditPermission]="page?.permissions | hasPermission: 'page_edit'"
      ></wiki-common-preview>
      <div class="page-creator common-page-creator fullscreen-hidden">
        <span class="creator" styxI18nTracking>
          <thy-avatar
            thySize="xs"
            [thySrc]="(page.published_by | user)?.avatar"
            [thyName]="(page.published_by | user)?.display_name"
            thyShowName="true"
            class="mr-1"
          >
          </thy-avatar>
          {{ 'wiki.common.status.publishedAt' | translate: { updated_at: page.published_at || page.updated_at | dateTimeAutoFormat } }}
        </span>
        <thy-divider [thyVertical]="true" thyColor="light"></thy-divider>
        <span translate="wiki.common.status.reading" [translateParams]="{ reading: page?.reading }"></span>
      </div>
      @if (isFullScreen) {
        <wiki-demonstration-toolbar [isFullScreen]="isFullScreen"></wiki-demonstration-toolbar>
      }
    </div>
  </thy-content>
</ng-template>

<ng-template #boardContent>
  <thy-content class="page-dialog-board">
    @if (hasPageDeleted) {
      <thy-alert
        class="mb-4 board-content-alert"
        thyType="warning-weak"
        thyMessage="{{
          'wiki.page.delete.byUser'
            | translate: { display_name: (page.deleted_by | user)?.display_name, time: page.deleted_at | dateTimeAutoFormat }
        }}"
      >
      </thy-alert>
    }
    @if (page | pageTagsVisibility) {
      <wiki-page-tags
        [page]="page"
        [addable]="!hasPageDeleted && !versionId"
        [removable]="!hasPageDeleted && !versionId"
        [clickSelectable]="!hasPageDeleted"
      ></wiki-page-tags>
    }
    <wiki-common-preview
      [page]="page"
      [value]="pageContent"
      [hasEditPermission]="page?.permissions | hasPermission: 'page_edit'"
    ></wiki-common-preview>
  </thy-content>
</ng-template>

<ng-template #tableContent>
  <thy-content class="page-dialog-table">
    <thy-alert
      class="mb-4 board-content-alert"
      *ngIf="hasPageDeleted"
      thyType="warning-weak"
      thyMessage="{{
        'wiki.page.delete.byUser'
          | translate: { display_name: (page.deleted_by | user)?.display_name, time: page.deleted_at | dateTimeAutoFormat }
      }}"
    >
    </thy-alert>
    @if (page.tag_ids && page.tag_ids.length > 0) {
      <wiki-page-tags
        *ngIf="page | pageTagsVisibility"
        [page]="page"
        [addable]="!hasPageDeleted && !versionId"
        [removable]="!hasPageDeleted && !versionId"
        [clickSelectable]="!hasPageDeleted"
      ></wiki-page-tags>
    }
    <div class="title d-flex align-items-center">
      @if (!(page.permissions | hasPermission: 'page_edit') || isDisableEdit) {
        @if (page.emoji_icon) {
          <styx-emoji #titleEmoji class="title-emoji-action mr-1" [styxCode]="page.emoji_icon" [styxSize]="32"></styx-emoji>
        }
        {{ page.name }}
      } @else {
        @if (page.emoji_icon) {
          <div class="title-emoji-action mr-1 pt-1 title-emoji-action-edit">
            <styx-emoji
              #titleEmoji
              thyTooltipPlacement="bottom"
              styxI18nTracking
              [thyTooltip]="'wiki.document.emoji.change' | translate"
              [styxCode]="page.emoji_icon || 'smile-plus'"
              [styxSize]="28"
              (click)="openEmoji($event, titleEmoji)"
            ></styx-emoji>
          </div>
        } @else {
          <a
            class="title-emoji-add mr-1 title-emoji-action-edit"
            #emojiChange
            href="javascript:;"
            thyAction
            thyActionIcon="smile-plus"
            [thyTooltip]="'wiki.document.emoji.add' | translate"
            thyTooltipPlacement="bottom"
            (click)="openEmoji($event, emojiChange)"
            styxI18nTracking
          ></a>
        }
        <input
          class="wiki-edit-document-title"
          type="text"
          styxI18nTracking
          [placeholder]="'wiki.page.untitled.document' | translate"
          [maxlength]="titleMaxLength"
          [thyAutofocus]="isCreatePage()"
          [(ngModel)]="page.name"
          (ngModelChange)="onTitleChange()"
          (focus)="pageTitleFocus()"
          (blur)="pageTitleBlur()"
          (keydown.enter)="pageTitleEnter($event)"
        />
      }
    </div>

    <ai-table-common-grid
      [pageId]="!hasPageDeleted && !versionId ? page?._id : null"
      [readonly]="isDisableEdit || page?.is_lock || !(page.permissions | hasPermission: 'page_edit')"
      [content]="hasPageDeleted || versionId ? page?.document : null"
    ></ai-table-common-grid>
  </thy-content>
</ng-template>

<ng-template #loadingTemplate>
  <thy-loading [thyDone]="loadingDone"></thy-loading>
</ng-template>

<ng-template #noPermission>
  <thy-content>
    <thy-empty [thyContainer]="elementRef" thyTopAuto thySize="lg" [thyMessage]="emptyDocumentText"></thy-empty>
  </thy-content>
</ng-template>

<ng-template #discussion>
  <wiki-global-discussion-list
    contentContainer="page-dialog-content-scroll-container"
    class="open-detail-page-wrapper"
  ></wiki-global-discussion-list>
</ng-template>

<ng-template #tree>
  <thy-tree
    class="py-6 page-dialog-tree"
    [thyNodes]="pageTree"
    thySize="sm"
    [thyIcons]="thyIcons"
    [thySelectedKeys]="[selectedPageId]"
    (thyOnClick)="pageTreeClick($event)"
  >
    <ng-template #treeNodeTemplate let-node="node" let-page="origin">
      <styx-awesome-text
        class="ml-n1"
        [styxText]="page.name"
        [styxCode]="page.emoji_icon"
        [styxIconColor]="page | pageIconColor"
        [styxIcon]="page | pageIcon"
        [styxActive]="page._id === selectedPageId"
      >
      </styx-awesome-text>
    </ng-template>
  </thy-tree>
</ng-template>

<ng-template thyStealthView>
  <styx-business-object-brand
    styxType="page"
    [styxEntity]="page"
    [styxShowName]="true"
    [styxShowIdentifier]="true"
  ></styx-business-object-brand>
</ng-template>
