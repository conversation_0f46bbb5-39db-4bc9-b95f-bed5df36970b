<form
  thyForm
  name="addEditParentForm"
  thyStopPropagation
  #demoVerticalThyForm="thyForm"
  #demoVerticalForm="ngForm"
  [thyFormValidatorConfig]="validatorConfig"
>
  <thy-dialog-header [thyTitle]="title"></thy-dialog-header>
  <thy-dialog-body>
    <div class="d-flex">
      <thy-form-group [thyLabelText]="spaceLabelText" thyLabelRequired class="thy-col-12 pr-4">
        <styx-pilot-select
          name="toSpaceId"
          [(ngModel)]="toSpaceId"
          [styxPlaceholder]="'styx.selectSpace' | translate"
          (ngModelChange)="toSpaceChange($event)"
        ></styx-pilot-select>
      </thy-form-group>
      <thy-form-group styxI18nTracking [thyLabelText]="'wiki.page.type' | translate" thyLabelRequired class="thy-col-12 pl-4">
        <wiki-page-type-selector [(pageType)]="pageType"></wiki-page-type-selector>
      </thy-form-group>
    </div>
    <thy-form-group [thyLabelText]="pageLabelText" thyLabelRequired>
      <wiki-page-selector
        #pageTree
        name="toPageId"
        [pageNodes]="pageNodes"
        [emptyStateText]="emptyStateText"
        [loadingDone]="loadingDone"
        [(ngModel)]="toPageId"
      ></wiki-page-selector>
    </thy-form-group>
    <thy-form-group-footer class="wiki-operations-model-footer">
      <button thyButton="link-secondary" (click)="close()" translate="common.cancel"></button>
      <button
        thyButton="primary"
        [thyLoading]="saving"
        [thyLoadingText]="'common.submitting' | translate"
        (thyFormSubmit)="save(demoVerticalThyForm)"
        translate="common.ok"
      ></button>
    </thy-form-group-footer>
  </thy-dialog-body>
</form>
