import { Component, DestroyRef, Input, OnInit, ViewChild, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Is, PilotEntryStore, ResponseData, StyxTranslateService, hasPermissionTransform } from '@atinc/ngx-styx';
import { PageTypes } from '@wiki/app/constants/page';
import { buildPageTree } from '@wiki/app/util/tree';
import { wikiPermissionDefinition, wikiPermissionPoints } from '@wiki/common/constants/permission';
import { ThyDialog, ThyDialogRef } from 'ngx-tethys/dialog';
import { ThyFormDirective } from 'ngx-tethys/form';
import { ThyTreeNodeData } from 'ngx-tethys/tree';
import { finalize } from 'rxjs/operators';
import { PageInfo } from '../../entities/page-info';
import { SpaceInfo } from '../../entities/space-info';
import { PageApiService } from '../../services';
import { PageSelectorComponent } from '../page-selector/page-selector.component';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'wiki-select-page',
    templateUrl: 'select-page.component.html',
    standalone: false
})
export class SelectPageComponent implements OnInit {
    private translate = inject(StyxTranslateService);

    @Input() spaceId: string;

    @Input() title: string;

    @Input() disabledPages: PageInfo[] = [];

    @Input() level: number;

    @Input() componentMode: 'create' | '' = '';

    @ViewChild('pageTree', { static: true }) pageTree: PageSelectorComponent;

    public spaceLabelText: string = this.translate.instant<I18nSourceDefinitionType>('styx.selectSpace');

    public pageLabelText: string = this.translate.instant<I18nSourceDefinitionType>('wiki.page.copy.select');

    public loadingDone = true;

    public saving = false;

    public spaces: SpaceInfo[] = [];

    public pageNodes: ThyTreeNodeData[] = [];

    public toSpaceId = '';

    public toPageId = '';

    public validatorConfig = {
        validationMessages: {
            toSpaceId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.validator.space.required')
            },
            toPageId: {
                required: this.translate.instant<I18nSourceDefinitionType>('wiki.validator.page.required')
            }
        }
    };

    initializeLabelText() {
        if (this.componentMode === 'create') {
            this.spaceLabelText = this.translate.instant<I18nSourceDefinitionType>('wiki.page.space');
            this.pageLabelText = this.translate.instant<I18nSourceDefinitionType>('wiki.page.parent');
        }
    }

    emptyStateText = this.translate.instant<I18nSourceDefinitionType>('common.noResult');

    pageType = PageTypes.document;

    private thyDialog = inject(ThyDialog);

    private pageApiService = inject(PageApiService);

    private thyDialogRef = inject(ThyDialogRef);

    private destroyRef = inject(DestroyRef);

    private pilotStore = inject(PilotEntryStore);

    ngOnInit() {
        this.loadSpaces();
        this.initializeLabelText();
    }

    toSpaceChange(toSpaceId) {
        this.toSpaceId = toSpaceId;
        this.loadPages();
    }

    save(form: ThyFormDirective) {
        if (this.toPageId) {
            const pageInfo = this.pageTree.tree.getSelectedNode().origin;
            this.thyDialog.close({ createType: this.pageType, pageInfo });
            return;
        }
        this.thyDialogRef.close();
    }

    close() {
        this.thyDialog.close();
    }

    private loadSpaces() {
        this.pilotStore
            .fetchSelectableAuthorizedEntries({
                permission_keys: [wikiPermissionDefinition.page_create.key]
            })
            .pipe(takeUntilDestroyed(this.destroyRef))
            .subscribe((data: ResponseData<SpaceInfo[]>) => {
                if (data.value.length > 0) {
                    const spaceList = data.value.filter(space => !space?.is_archived);
                    let toSpaceId: string;
                    if (this.spaceId && spaceList.some(space => space._id === this.spaceId)) {
                        toSpaceId = this.spaceId;
                    } else if (spaceList.length > 0) {
                        toSpaceId = spaceList[0]._id;
                    }
                    this.spaces = spaceList;

                    if (toSpaceId !== this.toSpaceId) {
                        this.toSpaceId = toSpaceId;
                        this.loadPages();
                    }
                }
            });
    }

    private loadPages() {
        if (!this.toSpaceId) {
            return;
        }
        this.loadingDone = false;
        this.pageApiService
            .fetchPageTree(this.toSpaceId)
            .pipe(
                finalize(() => (this.loadingDone = true)),
                takeUntilDestroyed(this.destroyRef)
            )
            .subscribe(pages => {
                let disabledPagesIds = this.disabledPages.map(item => item._id);
                if (this.componentMode === 'create') {
                    disabledPagesIds = pages
                        .filter(
                            x => x.is_visibility === Is.no || !hasPermissionTransform(x.permissions, wikiPermissionPoints, 'page_create')
                        )
                        .map(x => x._id);
                }

                this.pageNodes = buildPageTree(pages, disabledPagesIds);
                this.toPageId = this.pageNodes[0]?.key as string;
            });
    }
}
