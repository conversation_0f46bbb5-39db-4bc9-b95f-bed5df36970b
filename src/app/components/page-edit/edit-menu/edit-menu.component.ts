import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { IsMePipe, UserInfo, StyxPricingService } from '@atinc/ngx-styx';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { Subscription } from 'rxjs';
import { TheDocument } from '../../../../../common/custom-types';
import { PageInfo } from '../../../entities/page-info';
import { PageSaveAsStencilComponent } from '../../../features/page';
import { PageHistoryComponent, SaveNamedVersionComponent } from '../../../features/page/components';
import { TheEditor } from '@worktile/theia';

@Component({
    selector: 'wiki-edit-menu',
    templateUrl: './edit-menu.component.html',
    providers: [IsMePipe],
    standalone: false
})
export class EditMenuComponent implements OnInit {
    @Input()
    page: PageInfo;

    @Input()
    readonly: boolean;

    @Input()
    pageContent: TheDocument;

    @Input()
    editor: TheEditor;

    @Input()
    collaborators: UserInfo[] = [];

    @Input()
    publishHandle: () => Subscription;

    @Input()
    saveHandle: (versionName?: string) => Subscription;

    @Output()
    noticeParticipants = new EventEmitter<boolean>();

    @Output()
    closeDialog = new EventEmitter<void>();

    constructor(
        private thyDialog: ThyDialog,
        public pricingService: StyxPricingService,
        private isMePipe: IsMePipe
    ) {}

    ngOnInit(): void {}

    noticeParticipantsChange() {
        this.noticeParticipants.emit(true);
    }

    openHistory() {
        this.thyDialog.open(PageHistoryComponent, {
            initialState: {
                spaceId: this.page.space_id,
                pageId: this.page._id,
                isLock: this.page.is_lock,
                pagPermissions: this.page.permissions,
                closePageEdit: () => {
                    if (this.collaborators.length === 1 && this.isMePipe.transform(this.collaborators[0].uid)) {
                        this.editor.provider.destroy();
                        this.closeDialog.emit();
                    }
                }
            },
            size: ThyDialogSizes.full
        });
    }

    saveNamedVersion() {
        this.thyDialog.open(SaveNamedVersionComponent, {
            size: ThyDialogSizes.md,
            hasBackdrop: true,
            backdropClosable: true,
            closeOnNavigation: true,
            initialState: {
                saveNamedVersionHandle: this.saveHandle.bind(this)
            }
        });
    }

    saveAsStencil() {
        this.thyDialog.open(PageSaveAsStencilComponent, {
            initialState: { page: this.page, content: this.pageContent },
            size: ThyDialogSizes.lg
        });
    }
}
