import { Component, OnInit, ViewChild, TemplateRef, Input, OnD<PERSON>roy, inject } from '@angular/core';
import { Router } from '@angular/router';
import { hasPermissionTransform, PilotEntryStore, StyxGuiderService, StyxTranslateService } from '@atinc/ngx-styx';
import { ThyGuiderStep } from 'ngx-tethys/guider';
import { PageInfo } from '../../../entities/page-info';
import { wikiPermissionPoints } from '@wiki/common/constants/permission';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';

@Component({
    selector: 'page-guider',
    templateUrl: './page-guider.component.html',
    standalone: false
})
export class PageGuiderComponent implements OnInit, OnDestroy {
    private translate = inject(StyxTranslateService);

    private pilotStore = inject(PilotEntryStore);

    @Input() page: PageInfo;

    @ViewChild('createPageDesc', { static: true }) createPageDescTemplate: TemplateRef<any>;

    @ViewChild('draftsDesc', { static: true }) draftsDescTemplate: TemplateRef<any>;

    @ViewChild('editPageDesc', { static: true }) editPageDescTemplate: TemplateRef<any>;

    @ViewChild('moreDesc', { static: true }) moreDescTemplate: TemplateRef<any>;

    get currentSpace() {
        return this.pilotStore.snapshot.detail;
    }

    get isPageEdit() {
        return hasPermissionTransform(this.currentSpace.permissions, wikiPermissionPoints, 'page_edit');
    }

    get isPageCreate() {
        return hasPermissionTransform(this.currentSpace.permissions, wikiPermissionPoints, 'page_create');
    }

    constructor(
        private styxGuiderService: StyxGuiderService,
        private router: Router
    ) {}

    ngOnInit() {
        if (!this.router.url.includes('edit')) {
            this.startGuider();
        }
    }

    ngOnDestroy(): void {
        this.styxGuiderService.close();
    }

    startGuider() {
        const createStep: ThyGuiderStep[] = this.isPageCreate
            ? [
                  {
                      key: 'createPage',
                      hintPlacement: 'bottomLeft',
                      pointOffset: [-2, 26],
                      hintOffset: 10,
                      data: {
                          title: this.translate.instant<I18nSourceDefinitionType>('wiki.common.action.newPage'),
                          image: '/assets/images/guider/create-page.svg',
                          description: this.createPageDescTemplate
                      }
                  }
              ]
            : [];
        const editStep: ThyGuiderStep[] = this.isPageEdit
            ? [
                  {
                      key: 'editPage',
                      hintPlacement: 'bottomRight',
                      pointOffset: [-16, 20],
                      hintOffset: 10,
                      data: {
                          title: this.translate.instant<I18nSourceDefinitionType>('wiki.guider.page.editPage'),
                          image: '/assets/images/guider/edit-page.svg',
                          description: this.editPageDescTemplate
                      }
                  }
              ]
            : [];
        const draftsStep: ThyGuiderStep = {
            key: 'drafts',
            hintPlacement: 'rightBottom',
            pointOffset: [-8, -32],
            hintOffset: 18,
            data: {
                title: this.translate.instant<I18nSourceDefinitionType>('styx.draft', { isTitle: true, isPlural: false }),
                image: '/assets/images/guider/drafts.svg',
                description: this.draftsDescTemplate
            }
        };
        const moreStep: ThyGuiderStep = {
            key: 'more',
            hintPlacement: 'bottomRight',
            pointOffset: [-7, 20],
            hintOffset: 10,
            data: {
                title: this.translate.instant<I18nSourceDefinitionType>('common.more'),
                image: '/assets/images/guider/more.svg',
                description: this.moreDescTemplate
            }
        };

        this.styxGuiderService.start('wiki_detail', {
            pointClass: 'wiki-guider-point',
            hintClass: 'wiki-guider',
            steps: [...createStep, draftsStep, ...editStep, moreStep]
        });
    }
}
