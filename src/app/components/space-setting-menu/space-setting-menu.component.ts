import { HttpClient } from '@angular/common/http';
import { Component, inject, OnInit, TemplateRef } from '@angular/core';
import { I18nSourceDefinitionType } from '@wiki/app/constants/i18n-source';
import { Router } from '@angular/router';
import {
    DateFormatPipe,
    DriveFileIconUrlPipe,
    DriveTypes,
    Is,
    PilotEntryStore,
    ProcessRef,
    ResponseData,
    StyxConfirmService,
    StyxFeedReceiver,
    StyxFileProcessManager,
    StyxPricingService,
    StyxTranslateService,
    UtilService,
    helpers
} from '@atinc/ngx-styx';
import { ExportFileType } from '@wiki/app/constants';
import { FeedEventKeys } from '@wiki/app/constants/feed';
import { PageTreeQueryScene, PageTypes } from '@wiki/app/constants/page';
import { PageInfo } from '@wiki/app/entities/page-info';
import { SpaceInfo } from '@wiki/app/entities/space-info';
import { DeskAddOrCopySpaceComponent } from '@wiki/app/features/desk/add-copy-space/add-copy-space.component';
import { SpaceCheckpointsPipe } from '@wiki/app/pipes';
import { PageApiService, SpaceApiService } from '@wiki/app/services';
import { getExcessPageIds, getSelectedPageNodes, setTreeCheckableStatus, setTreeDisabledStatus } from '@wiki/app/util/tree';
import { IsPageGroupPipe, IsTablePipe } from '@wiki/common/pipe/page.pipe';
import { download } from '@wiki/common/util/download';
import { ThyDialogRef, ThyDialogSizes } from 'ngx-tethys/dialog';
import { ThyDropdownAbstractMenu } from 'ngx-tethys/dropdown';
import { ThyTreeEmitEvent, ThyTreeNode, ThyTreeNodeData, ThyTreeService } from 'ngx-tethys/tree';
import { TinyDate } from 'ngx-tethys/util';
import { Subject, Subscription, of } from 'rxjs';
import { catchError, map, takeUntil, tap } from 'rxjs/operators';

export const MAX_SELECT_PAGE_COUNT = 100;

@Component({
    selector: 'wiki-space-setting-menu',
    templateUrl: './space-setting-menu.component.html',
    providers: [SpaceCheckpointsPipe, DriveFileIconUrlPipe],
    standalone: false
})
export class SpaceSettingMenuComponent extends ThyDropdownAbstractMenu implements OnInit {
    private translate = inject(StyxTranslateService);

    public space: SpaceInfo;

    settingsLink: string;

    spaceIdentifier: string;

    pageNodes: PageInfo[] = [];

    selectIds: string[] = [];

    selectPages: PageInfo[] = [];

    messageTips = this.translate.instant<I18nSourceDefinitionType>('wiki.space.exportPage.maxTip', { count: MAX_SELECT_PAGE_COUNT });

    loading: boolean;

    errorTips = '';

    selectPageDialogRef: ThyDialogRef<HTMLElement>;

    selectNodes: ThyTreeNode[] = [];

    private fetchPages$: Subscription;

    private pilotStore = inject(PilotEntryStore);

    constructor(
        public util: UtilService,
        public router: Router,
        private feedReceiver: StyxFeedReceiver,
        public spaceCheckpoints: SpaceCheckpointsPipe,
        private pricingService: StyxPricingService,
        private spaceApiService: SpaceApiService,
        private http: HttpClient,
        private confirmService: StyxConfirmService,
        public fileProcessManager: StyxFileProcessManager,
        public driveFileIconUrlPipe: DriveFileIconUrlPipe,
        private isPageGroupPipe: IsPageGroupPipe,
        private isTablePipe: IsTablePipe,
        private pageApiService: PageApiService
    ) {
        super();
    }

    ngOnInit(): void {
        this.spaceIdentifier = this.space.identifier;
        this.settingsLink = `/wiki/spaces/${this.spaceIdentifier}/settings`;
    }

    copySpace() {
        this.util.dialog.open(DeskAddOrCopySpaceComponent, {
            size: ThyDialogSizes.maxLg,
            initialState: {
                saveFn$: (value: SpaceInfo) => {
                    return this.pilotStore.copyEntry(value).pipe(
                        tap(data => {
                            this.util.notify.success(this.translate.instant<I18nSourceDefinitionType>(`common.copySuccess`));
                            this.router.navigate(['/wiki/spaces/', data.value.identifier]);
                        })
                    );
                },
                space: this.space
            }
        });
    }

    openSelectPageDialog(selectPageTree: TemplateRef<HTMLElement>) {
        if (!this.pricingService.ensureCheckpointEnabled('pageTreeExport')) {
            return;
        }
        this.fetchPages$ = this.pageApiService
            .fetchPageTree(this.space?._id, { scene: PageTreeQueryScene.simple })
            .subscribe((pages: PageInfo[]) => {
                this.buildPageTree(pages);
                if (!this.selectPageDialogRef) {
                    this.createSelectPageDialogRef(selectPageTree);
                }
            });
    }

    createSelectPageDialogRef(selectPageTree: TemplateRef<HTMLElement>) {
        this.selectPageDialogRef = this.util.dialog.open(selectPageTree, {
            panelClass: 'export-page-tree-panel'
        });
        this.selectPageDialogRef.afterClosed().subscribe(() => {
            this.pageNodes = [];
            this.loading = false;
            this.errorTips = '';
            this.selectPageDialogRef = null;
            this.fetchPages$?.unsubscribe();
        });
    }

    buildPageTree(pages: PageInfo[] = []) {
        const _pages = helpers.sortBy(pages, 'position');
        const treeNodes: ThyTreeNodeData[] = (_pages || []).map((item: PageInfo) => {
            return {
                key: item._id,
                title: item.name,
                ...item,
                checked: false,
                disabled: item.is_visibility === Is.no || item.type === PageTypes.table
            };
        });
        this.pageNodes = helpers.buildTree(treeNodes, {
            parentIdKey: 'parent_id'
        });
    }

    checkStateResolve = (node: ThyTreeNode) => {
        return node.isChecked;
    };

    checkboxChange(treeNodeEvent: ThyTreeEmitEvent<ThyTreeService>) {
        this.errorTips = '';
        this.selectNodes = getSelectedPageNodes(treeNodeEvent, this.selectNodes);

        const exportPageTypes = (item: ThyTreeNode) =>
            !this.isTablePipe.transform({ type: item.origin.type }) && !this.isPageGroupPipe.transform({ type: item.origin.type });
        const selectPageNodes = this.selectNodes.filter(exportPageTypes);
        const flatTreeNodes = helpers.flatTree(treeNodeEvent.node.treeService.treeNodes);
        if (selectPageNodes.length >= MAX_SELECT_PAGE_COUNT) {
            // excessPageIds：超出选中数量的节点
            const excessPageIds = getExcessPageIds(this.selectNodes, selectPageNodes, MAX_SELECT_PAGE_COUNT);
            setTreeDisabledStatus(flatTreeNodes, excessPageIds);
        } else {
            setTreeCheckableStatus(flatTreeNodes);
        }
        this.selectPages = this.selectNodes.filter(exportPageTypes).map(item => item.origin);
        this.selectIds = this.selectPages.map(item => item._id);
    }

    exportPageTree() {
        this.loading = true;
        this.errorTips = '';
        if (!this.selectIds?.length) {
            this.errorTips = this.translate.instant<I18nSourceDefinitionType>('wiki.export.emptyPages');
            this.loading = false;
            return;
        }

        this.spaceApiService
            .exportPage({
                type: ExportFileType.PageTree,
                space_id: this.space._id,
                page_ids: this.selectIds
            })
            .subscribe((data: ResponseData) => {
                const dateTimeAutoFormat = new DateFormatPipe();
                const pageName = this.space.name + '-' + dateTimeAutoFormat.transform(new TinyDate().getTime(), 'yyyyMMddhhmm');
                const processRef = this.fileProcessManager.addProcess(
                    {
                        name: pageName,
                        icon: this.driveFileIconUrlPipe.transform({
                            type: DriveTypes.file,
                            addition: {
                                ext: 'pdf'
                            }
                        })
                    },
                    {
                        cancelTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.cancel'),
                        retryTooltip: this.translate.instant<I18nSourceDefinitionType>('wiki.export.retry'),
                        retry: (processRef: ProcessRef) => {
                            this.retryExport(pageName, processRef);
                        }
                    }
                );
                this.registerExportPageTreeFeedEvents(this.space._id, data.value, pageName, processRef);
                this.selectPageDialogRef.close();
            });
    }

    retryExport(pageName: string, processRef: ProcessRef) {
        processRef.reset();
        this.spaceApiService
            .exportPage({
                type: ExportFileType.PageTree,
                space_id: this.space._id,
                page_ids: this.selectIds
            })
            .pipe(
                catchError(error => {
                    this.util.defaultErrorHandler()(error);
                    return of(null);
                })
            )
            .subscribe((data: ResponseData) => {
                this.registerExportPageTreeFeedEvents(this.space._id, data.value, pageName, processRef);
            });
    }

    registerExportPageTreeFeedEvents(
        spaceId: string,
        jobInfo: { job_id: string; timeout: number },
        pageName: string,
        processRef: ProcessRef
    ) {
        const EXPORT_TIMEOUT = jobInfo.timeout;
        let destroy$ = new Subject<boolean>();

        let timer = setTimeout(() => {
            this.suspendSocket(jobInfo.job_id, destroy$, timer);
            processRef.error(this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportError'));
        }, EXPORT_TIMEOUT);

        processRef.cancel$.subscribe(() => {
            this.suspendSocket(jobInfo.job_id, destroy$, timer);
        });
        processRef.setProgress(1);
        this.feedReceiver
            .received()
            .pipe(takeUntil(destroy$))
            .subscribe(feed => {
                if (feed.data.jobId === jobInfo.job_id) {
                    processRef?.setId(jobInfo.job_id);
                    if (feed.data.succeeded && feed.data.total) {
                        processRef.setProgress(Math.round((feed.data.succeeded / feed.data.total) * 100));
                    }
                    if (feed.data.error) {
                        processRef.error(this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportError'));
                        this.destroy(destroy$, timer);
                    } else {
                        if (feed.event_key === FeedEventKeys.finishExport) {
                            const downloadUrl = feed.data.downloadUrl;
                            const expired = {
                                date: new TinyDate(feed.data.expired).getUnixTime(),
                                message: this.translate.instant<I18nSourceDefinitionType>('wiki.error.exportLinkExpired')
                            };
                            processRef.complete(null, {
                                url: downloadUrl,
                                expired
                            });
                            this.destroy(destroy$, timer);
                            const downloadDialog = document.querySelector('.download-page-dialog');
                            if (spaceId === this.pilotStore.snapshot.detail._id && !downloadDialog) {
                                this.openConfirm(downloadUrl, pageName, expired);
                            }
                        }
                    }
                }
            });
    }

    suspendSocket(jodId: string, destroy$: Subject<boolean>, timer: NodeJS.Timeout) {
        // 断开 socket 链接
        this.spaceApiService
            .cancelExportPage(jodId)
            .pipe(
                catchError(error => {
                    this.util.defaultErrorHandler()(error);
                    return of(null);
                })
            )
            .subscribe();
        this.destroy(destroy$, timer);
    }

    destroy(destroy$: Subject<Boolean>, timer: NodeJS.Timeout) {
        clearTimeout(timer);
        timer = null;
        destroy$.next(true);
        destroy$.unsubscribe();
    }

    openConfirm(
        downloadUrl: string,
        pageName: string,
        expired: {
            date: number;
            message: string;
        }
    ) {
        const fileName = `${pageName}.pdf`;
        this.confirmService.confirm({
            title: this.translate.instant<I18nSourceDefinitionType>('wiki.export.success'),
            okLoadingText: this.translate.instant<I18nSourceDefinitionType>('wiki.export.downloading'),
            okText: this.translate.instant<I18nSourceDefinitionType>('common.download'),
            mainContent: this.translate.instant<I18nSourceDefinitionType>(`wiki.page.export.success`, {
                content: this.translate.instant<I18nSourceDefinitionType>('wiki.page.export.file', { type: 'PDF' }),
                fileName
            }),
            action: () => {
                if (new TinyDate().getUnixTime() <= expired.date) {
                    return this.http.get(downloadUrl, { responseType: 'blob' }).pipe(
                        tap((blob: Blob) => {
                            download(blob, fileName);
                        }),
                        map(() => true),
                        catchError(error => {
                            this.util.defaultErrorHandler()(error);
                            return of(true);
                        })
                    );
                } else {
                    this.util.notify.error(this.translate.instant<I18nSourceDefinitionType>('common.failure'), expired.message);
                    return of(true);
                }
            }
        });
    }

    moreSetting() {
        if (this.spaceCheckpoints.transform(this.space, 'space_member_setting')) {
            this.router.navigate(['/wiki/spaces/' + this.spaceIdentifier + '/settings']);
        } else {
            this.router.navigate(['/wiki/spaces/' + this.spaceIdentifier + '/settings/basic']);
        }
    }
}
