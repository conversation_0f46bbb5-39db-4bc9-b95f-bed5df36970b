<a thyDropdownMenuItem href="javascript:;" thyStopPropagation styxPilotInfo [styxPilot]="space" [styxSettingsLink]="settingsLink">
  <thy-icon class="icon" thyIconName="info-circle"></thy-icon>
  <span thyDropdownMenuItemName translate="wiki.space.info"></span>
  <thy-icon thyDropdownMenuItemExtendIcon thyIconName="angle-right"></thy-icon>
</a>
@if (space | spaceCheckpoints: 'space_member_setting') {
  <a thyDropdownMenuItem href="javascript:;" [routerLink]="['/wiki/spaces/' + spaceIdentifier + '/settings/members']">
    <thy-icon class="icon" thyIconName="user"></thy-icon>
    <span thyDropdownMenuItemName translate="wiki.space.member"></span>
  </a>
}
<thy-divider></thy-divider>
<a
  thyDropdownMenuItem
  href="javascript:;"
  [routerLink]="['/wiki/spaces/' + spaceIdentifier + '/settings/basic']"
  [thyDisabled]="!(space?.permissions | hasPermission: 'space_basic_setting')"
>
  <thy-icon class="icon" thyIconName="edit"></thy-icon>
  <span thyDropdownMenuItemName translate="styx.editBaseInfo"></span>
</a>
<a thyDropdownMenuItem href="javascript:;" (click)="copySpace()" [thyDisabled]="!(space?.permissions | hasPermission: 'space_copy')">
  <thy-icon class="icon" thyIconName="copy"></thy-icon>
  <span thyDropdownMenuItemName translate="wiki.space.copy"></span>
</a>
<a
  thyDropdownMenuItem
  href="javascript:;"
  (click)="openSelectPageDialog(selectPageTree)"
  [thyDisabled]="!(space?.permissions | hasPermission: 'page_export')"
>
  <thy-icon class="icon" thyIconName="file-export"></thy-icon>
  <span thyDropdownMenuItemName translate="wiki.space.exportTree"></span>
</a>
<a thyDropdownMenuItem href="javascript:;" (click)="moreSetting()">
  <thy-icon class="icon" thyIconName="list"></thy-icon>
  <span thyDropdownMenuItemName translate="styx.moreSetting"></span>
</a>
<thy-divider></thy-divider>
<a
  thyDropdownMenuItem
  href="javascript:;"
  [routerLink]="['/wiki/spaces/' + spaceIdentifier + '/settings/trash']"
  [thyDisabled]="!(space?.permissions | hasPermission: 'space_recycle_bin_setting')"
>
  <thy-icon class="icon" thyIconName="recycle-bin"></thy-icon>
  <span thyDropdownMenuItemName translate="styx.recycleBin"></span>
</a>

<ng-template #selectPageTree>
  <thy-dialog-header styxI18nTracking [thyTitle]="'wiki.space.exportTree' | translate"></thy-dialog-header>
  <thy-dialog-body [class.invalid]="errorTips">
    <thy-alert thyTheme="naked" thyType="danger" thyIcon="lightbulb" [thyMessage]="messageTips"></thy-alert>
    <p
      class="mt-4 mb-2 text-secondary"
      translate="wiki.space.exportPage.select"
      [translateParams]="{ selectPagesCount: selectPages.length }"
    ></p>
    <wiki-page-selector
      class="export-page-selector"
      [pageNodes]="pageNodes"
      [checkable]="true"
      clickBehavior="selectCheckbox"
      [checkStateResolve]="checkStateResolve"
      (checkboxChange)="checkboxChange($event)"
    >
    </wiki-page-selector>
    <div class="invalid-feedback">{{ errorTips }}</div>
  </thy-dialog-body>
  <thy-dialog-footer [thyDivided]="false">
    <button thyButton="link-secondary" (click)="util.dialog.close()" translate="common.cancel"></button>
    <button thyButton="primary" [thyLoading]="loading" (click)="exportPageTree()" translate="common.ok"></button>
  </thy-dialog-footer>
</ng-template>
