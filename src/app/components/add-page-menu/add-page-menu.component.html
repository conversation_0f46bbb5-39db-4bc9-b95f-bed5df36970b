<a thyDropdownMenuItem href="javascript:;" (click)="addNewPage()">
  <styx-awesome-text
    class="ml-n1"
    styxI18nTracking
    [styxText]="'wiki.action.newDoc' | translate"
    [styxIcon]="1 | pageIcon"
    [styxIconColor]="1 | pageIconColor"
  ></styx-awesome-text>
</a>

<a thyDropdownMenuItem href="javascript:;" (click)="addNewPage(4)">
  <styx-awesome-text
    class="ml-n1"
    [styxText]="'wiki.action.newTable' | translate"
    [styxIcon]="4 | pageIcon"
    [styxIconColor]="4 | pageIconColor"
  ></styx-awesome-text>
</a>

<a thyDropdownMenuItem href="javascript:;" (click)="addNewPage(3)">
  <styx-awesome-text
    class="ml-n1"
    styxI18nTracking
    [styxText]="'wiki.action.newBoard' | translate"
    [styxIcon]="3 | pageIcon"
    [styxIconColor]="3 | pageIconColor"
  ></styx-awesome-text>
</a>

@if (allowNewGroup()) {
  <thy-dropdown-menu-divider></thy-dropdown-menu-divider>
  <a href="javascript:;" thyDropdownMenuItem (click)="addNewGroup()">
    <styx-awesome-text
      class="ml-n1"
      styxI18nTracking
      [styxText]="'wiki.action.newGroup' | translate"
      [styxIcon]="2 | pageIcon"
      [styxIconColor]="2 | pageIconColor"
    ></styx-awesome-text>
  </a>
}

<thy-dropdown-menu-divider></thy-dropdown-menu-divider>
<a href="javascript:;" thyDropdownMenuItem [thyDisabled]="!(permissions() | hasPermission: 'page_create')" (click)="openAIWriting($event)">
  <thy-icon thyDropdownMenuItemIcon thyIconName="ai-star"></thy-icon>
  <span thyDropdownMenuItemName translate="styx.aiWriting"></span>
</a>
<a href="javascript:;" thyDropdownMenuItem [thyDisabled]="!(permissions() | hasPermission: 'page_create')" (click)="openStencil()">
  <thy-icon thyDropdownMenuItemIcon thyIconName="magic"></thy-icon>
  <span thyDropdownMenuItemName translate="wiki.action.newFromTemplate"></span>
</a>
<a href="javascript:;" thyDropdownMenuItem [thyDisabled]="!(permissions() | hasPermission: 'page_create')" (click)="openFileImportDialog()">
  <thy-icon thyDropdownMenuItemIcon thyIconName="file-import"></thy-icon>
  <span thyDropdownMenuItemName translate="common.import"></span>
</a>
