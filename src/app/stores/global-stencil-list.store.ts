import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { tap } from 'rxjs/operators';
import { EntityStore, EntityState, Action } from '@tethys/store';
import {
    PaginationResponseData,
    ResponseData,
    helpers,
    GlobalUsersStore,
    MemberInfo,
    Direction,
    Is,
    PilotEntryStore
} from '@atinc/ngx-styx';
import { inject, Injectable, OnDestroy } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { StencilInfo } from '@wiki/app/entities/stencil-info';
import { API_PREFIX } from '@wiki/app/constants';
import { RelationPages } from '@wiki/common/plugins/relation-page/type';
import { PageEventBus } from '@wiki/common/services/page-event-bus';
import { produce } from 'ngx-tethys/util';
import { PageTypes } from '../constants/page';

export interface StencilsState extends EntityState<StencilInfo> {
    stencil: StencilInfo;
    relationPages: RelationPages;
    members?: MemberInfo[];
}

@Injectable()
export class GlobalStencilsStore extends EntityStore<StencilsState, StencilInfo> implements OnDestroy {
    private pilotStore = inject(PilotEntryStore, { optional: true });

    pageInfoSubscriber: Subscription;

    constructor(
        private http: HttpClient,
        public usersStore: GlobalUsersStore,
        private router: Router,
        public globalPageStore: PageEventBus
    ) {
        super();
        this.subscribeGlobalPage();
    }

    private get isConfigurationModule() {
        return this.router.url.includes('configuration');
    }

    get stencilsApiPrefix() {
        if (this.isConfigurationModule) {
            return `${API_PREFIX}/configuration/stencils`;
        } else {
            return `${API_PREFIX}/spaces/${this.pilotStore.snapshot.detail._id}/stencils`;
        }
    }

    fetchStencils(
        keyWords?: string,
        pageIndex?: number,
        pageSize?: number,
        options?: { sortBy: string; sortDirection: Direction }
    ): Observable<PaginationResponseData<StencilInfo[]>> {
        const params = { pi: pageIndex, ps: pageSize, keywords: keyWords, sort_by: options.sortBy, sort_direction: options.sortDirection };
        return this.http
            .get(
                `${
                    this.isConfigurationModule
                        ? this.stencilsApiPrefix
                        : `${API_PREFIX}/spaces/${this.pilotStore.snapshot.detail._id}/space-stencils`
                }/${helpers.buildUrlParams(params)}`
            )
            .pipe(
                tap((data: PaginationResponseData<StencilInfo[]>) => {
                    this.initialize(data.value, {
                        pageIndex: data.page_index,
                        pageSize: data.page_size,
                        pageCount: data.page_count,
                        count: data.count
                    });

                    this.usersStore.initializeUsers(data.references.members);
                })
            );
    }

    @Action()
    addStencil(pageType: PageTypes): Observable<ResponseData<StencilInfo>> {
        const url = this.isConfigurationModule
            ? `${API_PREFIX}/configuration/stencil`
            : `${API_PREFIX}/spaces/${this.pilotStore.snapshot.detail._id}/stencil`;

        return this.http
            .post<ResponseData<StencilInfo>>(url, {
                page_type: pageType
            })
            .pipe(
                tap(data => {
                    this.update({ stencil: data.value, relationPages: {}, members: [] });
                })
            );
    }

    @Action()
    deleteStencil(stencilId: string): Observable<ResponseData<boolean>> {
        return this.http.delete<ResponseData<boolean>>(`${this.stencilsApiPrefix}/${stencilId}`).pipe(
            tap(() => {
                this.remove(stencilId);
            })
        );
    }

    subscribeGlobalPage() {
        this.pageInfoSubscriber = this.globalPageStore
            .onPageInfoChange$()
            .pipe(
                tap((stencil: StencilInfo) => {
                    if (stencil && stencil.is_stencil === Is.yes) {
                        this.update({
                            entities: produce(this.snapshot.entities).update(stencil._id, stencil)
                        });
                    }
                })
            )
            .subscribe();
    }

    ngOnDestroy() {
        if (this.pageInfoSubscriber) {
            this.pageInfoSubscriber.unsubscribe();
        }
        super.ngOnDestroy();
    }
}
