import { inject, Injectable } from '@angular/core';
import { EntityStore, EntityState, Action, OnCombineRefs, ReferencesIdDictionary } from '@tethys/store';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { Observable, Subscription } from 'rxjs';
import { tap } from 'rxjs/operators';
import { PaginationResponseData, ResponseData, helpers, GlobalUsersStore, Id, PilotEntryStore } from '@atinc/ngx-styx';
import { ShareInfo, ShareReferences, SharePageParams } from '../entities/sharing.info';
import { API_PREFIX } from '../constants';
import { PageInfo } from '../entities/page-info';
import { PageEventBus } from '@wiki/common/services/page-event-bus';

export interface SharePageState extends EntityState<ShareInfo, ShareReferences> {}

@Injectable()
export class SharePageStore extends EntityStore<SharePageState, ShareInfo> implements OnCombineRefs<ShareInfo, ShareReferences> {
    private pilotStore = inject(PilotEntryStore, { optional: true });

    pageInfoSubscriber: Subscription;

    constructor(
        private http: HttpClient,
        public usersStore: GlobalUsersStore,
        private router: Router,
        public globalPageStore: PageEventBus
    ) {
        super({ entities: [] }, { idKey: 'principal_id' });
        this.subscribeGlobalPage();
    }

    // eslint-disable-next-line
    ngOnDestroy() {
        if (this.pageInfoSubscriber) {
            this.pageInfoSubscriber.unsubscribe();
        }
        super.ngOnDestroy();
    }

    onCombineRefs(entity: ShareInfo, referencesIdMap: ReferencesIdDictionary<ShareReferences>, references: ShareReferences) {
        if (references.pilots) {
            entity.refs.pilot = referencesIdMap.pilots[entity.pilot_id];
            if (referencesIdMap?.pages && referencesIdMap.pages[entity.page_id]) {
                entity.refs.page = referencesIdMap.pages[entity.page_id];
            }
            if (referencesIdMap.principals[entity.principal_id]) {
                entity.refs.principal = referencesIdMap.principals[entity.principal_id];
                entity.emoji_icon = referencesIdMap.principals[entity.principal_id]?.emoji_icon;
            }
        }
    }

    private get isConfiguration() {
        return this.router.url.includes('configuration');
    }

    get apiPrefix() {
        if (this.isConfiguration) {
            return `${API_PREFIX}/configuration/shared`;
        } else {
            return API_PREFIX;
        }
    }

    fetchSharePages(data: SharePageParams): Observable<PaginationResponseData<ShareInfo[]>> {
        const params = this.isConfiguration
            ? {
                  pi: data?.pageIndex,
                  keywords: data?.keyWords,
                  ps: data?.pageSize,
                  sort_by: data?.sortBy,
                  sort_direction: data?.sortDirection
              }
            : { pi: data?.pageIndex, ps: data?.pageSize, keywords: data?.keyWords, sort_by: data?.sortBy, sort: data?.sortDirection };
        return this.http
            .get(
                `${
                    this.isConfiguration
                        ? `${this.apiPrefix}/pages`
                        : `${this.apiPrefix}/pages/pilots/${this.pilotStore.snapshot.detail._id}/shared-setting`
                }/${helpers.buildUrlParams(params)}`
            )
            .pipe(
                tap((data: PaginationResponseData<ShareInfo[]>) => {
                    this.initializeWithReferences(data.value, data.references, {
                        pageIndex: data.page_index,
                        pageSize: data.page_size,
                        pageCount: data.page_count,
                        count: data.count
                    });
                    this.usersStore.initializeUsers(data.references.members);
                })
            );
    }

    @Action()
    cancelSharePage(pageId: string): Observable<ResponseData<boolean>> {
        return this.http.delete<ResponseData<boolean>>(`${this.apiPrefix}/pages/${pageId}/shared-setting`).pipe(
            tap(() => {
                this.remove(pageId);
            })
        );
    }

    @Action()
    pureRemoveShared(pageId: string) {
        this.remove(pageId);
    }

    @Action()
    pureAddShared(shareInfo: ShareInfo) {
        this.add(shareInfo);
    }

    @Action()
    updateShareData(sharedInfo: ShareInfo) {
        this.update(sharedInfo.principal_id, sharedInfo);
    }

    @Action()
    pureUpdatePageById(id: Id, page: Partial<PageInfo>) {
        if (helpers.find(this.snapshot.entities, { principal_id: id })) {
            this.snapshot.references.principals.forEach(item => {
                if (item._id === id) {
                    item.emoji_icon = page.emoji_icon;
                    return;
                }
            });
            this.update(id, page);
        }
    }

    subscribeGlobalPage() {
        this.pageInfoSubscriber = this.globalPageStore
            .onPageInfoChange$()
            .pipe(
                tap((page: PageInfo) => {
                    if (page) {
                        this.pureUpdatePageById(page._id, page);
                    }
                })
            )
            .subscribe();
    }
}
