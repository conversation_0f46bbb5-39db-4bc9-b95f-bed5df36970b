import { Injectable, Optional } from '@angular/core';
import { tap } from 'rxjs/operators';
import { Router } from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { EntityState, EntityStore, Action, OnCombineRefs, ReferencesIdDictionary } from '@tethys/store';
import { GlobalUsersStore, PaginationResponseData, Is, helpers, PilotEntryStore, PilotEntryInfo } from '@atinc/ngx-styx';
import { API_PREFIX } from '../constants';
import { SpaceInfo } from '../entities/space-info';
import { ShareInfo, ShareReferences } from '../entities/sharing.info';

export interface ShareSpaceState extends EntityState<ShareInfo, ShareReferences> {}

@Injectable()
export class ShareSpaceStore extends EntityStore<ShareSpaceState, ShareInfo> implements OnCombineRefs<ShareInfo, ShareReferences> {
    constructor(
        private router: Router,
        private http: HttpClient,
        public userStore: GlobalUsersStore,
        @Optional()
        private pilotStore: PilotEntryStore
    ) {
        super({ entities: [] }, { idKey: 'principal_id' });
    }

    onCombineRefs(entity: ShareInfo, referencesIdMap: ReferencesIdDictionary<ShareReferences>, references: ShareReferences) {
        if (references.pilots) {
            entity.refs.pilot = referencesIdMap.pilots[entity.pilot_id];
        }
    }

    private get isConfiguration() {
        return this.router.url.includes('configuration');
    }

    get apiPrefix() {
        if (this.isConfiguration) {
            return `${API_PREFIX}/configuration/shared`;
        } else {
            return API_PREFIX;
        }
    }

    fetchSpace(keyWords?: string, pageIndex?: number) {
        const params = { pi: pageIndex, keywords: keyWords };

        return this.http.get(`${this.isConfiguration ? `${this.apiPrefix}/spaces` : ''}/${helpers.buildUrlParams(params)}`).pipe(
            tap((data: PaginationResponseData<SpaceInfo[]>) => {
                this.initializeWithReferences(data.value, data.references, {
                    pageIndex: data.page_index,
                    pageSize: data.page_size,
                    pageCount: data.page_count,
                    count: data.count
                });
                this.userStore.initializeUsers(data.references.members);
            })
        );
    }

    @Action()
    sharedStatusChange(spaceId: string, isShared: Is) {
        if (!isShared) {
            this.remove(spaceId);
        }
        if (this.pilotStore.snapshot.detail && this.pilotStore.snapshot.detail._id === spaceId) {
            const spaceDetail: PilotEntryInfo = {
                ...this.pilotStore.snapshot.detail,
                is_shared: isShared
            };
            this.pilotStore.updateEntryAction(spaceDetail);
        }
    }

    @Action()
    updateShareData(sharedInfo: ShareInfo) {
        this.update(sharedInfo.principal_id, sharedInfo);
    }
}
