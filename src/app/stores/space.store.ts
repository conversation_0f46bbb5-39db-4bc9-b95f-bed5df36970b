import { Injectable } from '@angular/core';
import { PilotState, PilotStore, WikiBroadObjectTypes } from '@atinc/ngx-styx';
import { SafeAny } from 'ngx-tethys/types';
import { SpaceInfo } from '../entities/space-info';

export interface WikiPilotState extends PilotState<SpaceInfo> {}

@Injectable({
    providedIn: 'root'
})
export class SpaceStore extends PilotStore<WikiPilotState, SpaceInfo, SafeAny, SpaceInfo> {
    options: { titleKey: string; type: WikiBroadObjectTypes.space } = {
        titleKey: 'styx.space',
        type: WikiBroadObjectTypes.space
    };

    constructor() {
        super();
    }

    buildReferences(): void {}
}
