import { ObserversModule } from '@angular/cdk/observers';
import { ScrollingModule } from '@angular/cdk/scrolling';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { inject, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import {
    AppRootContext,
    BaselinePrincipalsRouterKey,
    Direction,
    IMPORTANT_LEVELS,
    PILOT_MEMBER_SUPPORT_USER_GROUP,
    PilotStore,
    ReviewObjectChangeType,
    ReviewObjectInfo,
    ReviewSubjectType,
    STYX_BASELINE_PRINCIPALS_ROUTER_KEY,
    STYX_PRICING_CONFIG,
    STYX_REVIEW_CONFIG,
    StyxBroadObjectReceiver,
    StyxBusinessObjectReceiver,
    StyxConfirmService,
    StyxReviewApiService,
    StyxTheImageService,
    StyxTranslateService,
    WikiBroadObjectTypes,
    getBroadObjectDefinitionByType,
    helpers,
    isBroadObjectType,
    providePilotEntryStore,
    reviewCustomSubjectDefinitions
} from '@atinc/ngx-styx';
import { globalPermissionDefinition, wikiPermissionDefinition } from '@wiki/common/constants/permission';
import { THE_IMAGE_SERVICE_TOKEN } from '@worktile/theia';
import { QRCodeComponent } from 'angularx-qrcode';
import { THY_AUTOCOMPLETE_DEFAULT_CONFIG } from 'ngx-tethys/autocomplete';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { SafeAny } from 'ngx-tethys/types';
import { Observable, map } from 'rxjs';
import { AITableModule } from '../../ai-table/module';
import { AppRoutingModule } from './app-routing.module';
import { PageEditComponent } from './components/page-edit/edit/edit.component';
import { PAGE_EDIT_COMPONENT_TOKEN } from './components/page-edit/edit/edit.component.token';
import { SharedModule } from './components/shared.module';
import { StencilDetailComponent } from './components/stencil/detail/stencil-detail.component';
import { PAGE_STENCIL_CONTENT_DETAIL_COMPONENT } from './components/stencil/detail/stencil-detail.component.token';
import { StencilEditComponent } from './components/stencil/edit/stencil-edit.component';
import { PAGE_STENCIL_CONTENT_EDIT_COMPONENT } from './components/stencil/edit/stencil-edit.component.token';
import { APP_NAME, PageTypes } from './constants';
import { DISABLED_PROPERTY } from './constants/baseline';
import { API_PREFIX } from './constants/index';
import { getProperties } from './constants/property';
import { CrossApplicationModule } from './cross-application/module';
import { BASELINE_COMPONENTS } from './features/baseline';
import { CONFIGURATION_COMPONENTS } from './features/configuration';
import { DESK_COMPONENTS } from './features/desk';
import { FeaturesModule } from './features/features.module';
import { PAGE_COMPONENTS } from './features/page';
import { PageHistoryComponent } from './features/page/components';
import { WikiReviewSelectPageComponent } from './features/review/components/select-page.component/select-page.component';
import { WikiErrorInterceptor } from './interceptors/error.interceptor';
import { ActualRootComponent, RootComponent } from './root/root.component';
import { PageApiService } from './services';
import { ReviewApiService } from './services/review-api.service';
import { WikiBroadObjectReceiver } from './services/util/broad-object-receiver';
import { WikiBusinessObjectReceiver } from './services/util/business-object-receiver';
import { WikiGlobalEventDispatcherService } from './services/util/global-event-dispatcher.service';
import { SpaceService } from './services/util/space.service';
import { STORES } from './stores';
import { SpaceStore } from './stores/space.store';
import { VERSION } from './version';
import { i18nSourceDefinition, I18nSourceDefinitionType } from './constants/i18n-source';
import { isArray } from 'ngx-tethys/util';

export const FEATURE_CONFIG = {
    appName: APP_NAME,
    appApiPrefix: API_PREFIX,
    staticPath: `/static/${APP_NAME}`,
    appConfigurationApiPrefix: `${API_PREFIX}/configuration`,
    appPilotMemberApiPrefix: `${API_PREFIX}/spaces`,
    applicationPermissionPoints: helpers.getStoragePermissionPoints(wikiPermissionDefinition),
    applicationGlobalPermissionPoints: helpers.getStoragePermissionPoints(globalPermissionDefinition),
    appCheckpoints: { rolePermissionDetail: true },
    version: VERSION,
    i18nSourceDefinition: i18nSourceDefinition
};

export const getPricingConfig = (translate: StyxTranslateService) => ({
    expired: {
        description: translate.instant<I18nSourceDefinitionType>('wiki.pricing.expired.description'),
        image: '/assets/images/share/overdue.svg'
    },
    checkpoints: [
        {
            key: 'pageExport',
            title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.title'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.subTitle'),
            image: '/assets/images/share/page-export.svg',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.offlineReading')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.contentStyle')
                }
            ]
        },
        {
            key: 'pageTreeExport',
            title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTreeExport.title'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTreeExport.subTitle'),
            image: '/assets/images/share/page-tree-export.svg',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.offlineReading')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageExport.contentStyle')
                }
            ]
        },
        {
            key: 'pageTemplate',
            title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTemplate.title'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTemplate.subTitle'),
            image: '/assets/images/share/page-template.svg?1',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTemplate.orgTemplate')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageTemplate.spaceTemplate')
                }
            ]
        },
        {
            key: 'pageShare',
            title: translate.instant<I18nSourceDefinitionType>('wiki.page.sharing.title'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageShare.subTitle'),
            image: '/assets/images/share/page-share.svg?1',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageShare.memberPermission')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageShare.externalPermission')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.pageShare.publicShare')
                }
            ]
        },
        {
            key: 'embedment',
            title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.embedment.title'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.embedment.subTitle'),
            image: '/assets/images/space/page/pricing-embedment-preview.svg',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.embedment.onlinePlay')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.embedment.richContent')
                }
            ]
        },
        {
            key: 'spaceShare',
            title: translate.instant<I18nSourceDefinitionType>('wiki.space.sharing'),
            subTitle: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.spaceShare.subTitle'),
            image: '/assets/images/share/space-share.svg?1',
            features: [
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.spaceShare.expiry')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.spaceShare.password')
                },
                {
                    title: translate.instant<I18nSourceDefinitionType>('wiki.pricing.features.spaceShare.qrCode')
                }
            ]
        }
    ]
});

@NgModule({
    imports: [
        BrowserModule,
        AppRoutingModule,
        ObserversModule,
        SharedModule,
        ScrollingModule,
        QRCodeComponent,
        CrossApplicationModule,
        BrowserAnimationsModule,
        FeaturesModule,
        AITableModule
    ],
    declarations: [
        RootComponent,
        ActualRootComponent,
        ...DESK_COMPONENTS,
        ...PAGE_COMPONENTS,
        ...CONFIGURATION_COMPONENTS,
        ...BASELINE_COMPONENTS
    ],
    providers: [
        ...STORES,
        {
            provide: StyxBroadObjectReceiver,
            useClass: WikiBroadObjectReceiver
        },
        {
            provide: StyxBusinessObjectReceiver,
            useClass: WikiBusinessObjectReceiver
        },
        {
            provide: PilotStore,
            useExisting: SpaceStore
        },
        providePilotEntryStore({
            objectTranslateKey: 'styx.space',
            objectType: WikiBroadObjectTypes.space
        }),
        {
            provide: THE_IMAGE_SERVICE_TOKEN,
            useClass: StyxTheImageService
        },
        {
            provide: PAGE_EDIT_COMPONENT_TOKEN,
            useValue: PageEditComponent
        },
        {
            provide: PAGE_STENCIL_CONTENT_EDIT_COMPONENT,
            useValue: StencilEditComponent
        },
        {
            provide: PAGE_STENCIL_CONTENT_DETAIL_COMPONENT,
            useValue: StencilDetailComponent
        },
        { provide: HTTP_INTERCEPTORS, useClass: WikiErrorInterceptor, multi: true },
        {
            provide: STYX_PRICING_CONFIG,
            deps: [StyxTranslateService],
            useFactory: (translate: StyxTranslateService) => {
                return getPricingConfig(translate);
            }
        },
        SpaceService,
        {
            provide: THY_AUTOCOMPLETE_DEFAULT_CONFIG,
            useValue: {
                panelClass: 'wiki-auto-complete-panel'
            }
        },
        { provide: PILOT_MEMBER_SUPPORT_USER_GROUP, useValue: true },
        {
            provide: StyxReviewApiService,
            useClass: ReviewApiService
        },
        {
            provide: STYX_REVIEW_CONFIG,
            deps: [PageApiService, ThyDialog, StyxConfirmService, StyxTranslateService],
            useFactory: (
                pageApiService: PageApiService,
                thyDialog: ThyDialog,
                confirmService: StyxConfirmService,
                translate: StyxTranslateService
            ) => {
                const property = getProperties(translate);
                return {
                    apiPrefix: `${API_PREFIX}/spaces`,
                    scopeSelectableApi: `${API_PREFIX}/spaces?points=${wikiPermissionDefinition.manage_review.key}`, // 新建时获取所属空间
                    versionApiPrefix: `${API_PREFIX}/pages`, // 版本比对、版本下拉时使用
                    pilotRouterLinkPrefix: '/wiki/spaces',
                    reviewContentColumnConfig: { name: { width: 'auto' } },
                    objectTypeConfig: {
                        page: {
                            selectionComponent: WikiReviewSelectPageComponent,
                            sortConfig: {
                                sortColumn: () => pageApiService.fetchSortProperties(),
                                default: {
                                    sortBy: 'position',
                                    sortDirection: Direction.ascending
                                }
                            },
                            viewConfig: {
                                filterMode: 'customize',
                                disabledLogic: false,
                                selectableProperties: () => {
                                    return pageApiService
                                        .fetchProperties()
                                        .pipe(map(properties => properties.filter(property => !DISABLED_PROPERTY.includes(property.key))));
                                },
                                selectableOptions: {
                                    [property.important_level.oldKey]: IMPORTANT_LEVELS
                                },
                                selectableSearchScopeOptions: [
                                    { key: 'title', name: translate.instant<I18nSourceDefinitionType>('styx.title') }
                                ]
                            },
                            versionCompareAction: page => {
                                thyDialog.open(PageHistoryComponent, {
                                    initialState: {
                                        title: translate.instant<I18nSourceDefinitionType>('wiki.page.info.compare'),
                                        isVersionComparison: true,
                                        spaceId: page.space_id,
                                        pageId: page._id,
                                        originVersionId: page.change_version.from._id,
                                        targetVersionId: page.change_version.to._id
                                    },
                                    size: ThyDialogSizes.full
                                });
                            }
                        }
                    },
                    enableResubmit: true,
                    customRemoveObjectConfirm: (
                        objectOrObjects: ReviewObjectInfo | ReviewObjectInfo[],
                        objectType: ReviewSubjectType,
                        action: () => Observable<SafeAny>
                    ) => {
                        const object = isArray(objectOrObjects) ? objectOrObjects[0] : objectOrObjects;
                        const objectDefinition = isBroadObjectType(objectType)
                            ? getBroadObjectDefinitionByType(objectType, object, translate)
                            : reviewCustomSubjectDefinitions[objectType];
                        const setMainContent = function (target) {
                            if (helpers.isArray(target)) {
                                return translate.instant('wiki.review.removeConfirm.multiple', { count: target.length });
                            }
                            return translate.instant('wiki.baseline.remove.confirm.single', {
                                name: confirmService.generateTargetHtml(target.name, 'danger')
                            });
                        };

                        confirmService.confirm({
                            title: translate.instant<I18nSourceDefinitionType>('wiki.common.confirm.moveOut'),
                            mainContent: setMainContent(objectOrObjects),
                            operationName: translate.instant<I18nSourceDefinitionType>(`common.moveOut`),
                            operationType: 'danger',
                            description: translate.instant('wiki.baseline.remove.confirm.description'),
                            action: action
                        });
                    },
                    reviewContentCustomActions: [
                        {
                            icon: 'contrast-view',
                            text: translate.instant('styx.versionComparison'),
                            show: page => page.type === PageTypes.document && page.change_type === ReviewObjectChangeType.update,
                            clickAction: page => {
                                thyDialog.open(PageHistoryComponent, {
                                    initialState: {
                                        title: translate.instant<I18nSourceDefinitionType>('wiki.page.info.compare'),
                                        isVersionComparison: true,
                                        spaceId: page.space_id,
                                        pageId: page._id,
                                        originVersionId: page.change_version.from._id,
                                        targetVersionId: page.change_version.to._id
                                    },
                                    size: ThyDialogSizes.full
                                });
                            }
                        }
                    ]
                };
            }
        },
        {
            provide: STYX_BASELINE_PRINCIPALS_ROUTER_KEY,
            useValue: BaselinePrincipalsRouterKey.pages
        }
    ]
})
export class AppModule {
    constructor(private wikiGlobalEventDispatcherService: WikiGlobalEventDispatcherService) {
        this.wikiGlobalEventDispatcherService.registerGlobalEvents();
    }
}
