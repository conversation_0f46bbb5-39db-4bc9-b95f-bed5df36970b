{"wiki.title.default": "知识管理", "wiki.title.page": " | 知识管理", "wiki.desk.newSpace": "新建空间", "wiki.desk.spaces.all": "全部空间", "wiki.desk.spaces.organization": "组织空间", "wiki.desk.spaces.team": "团队空间", "wiki.desk.spaces.personal": "个人空间", "wiki.desk.spaces.archived": "归档空间", "wiki.desk.spaces.sharing": "共享中", "wiki.desk.spaces.share": "共享", "wiki.desk.share.title": "与我共享", "wiki.desk.share.sharedTime": "共享时间", "wiki.desk.share.sharedBy": "共享人", "wiki.desk.share.viewOnly": "仅查看", "wiki.desk.share.editable": "可编辑", "wiki.desk.collections.collectionTime": "收藏时间", "wiki.desk.recent.title": "最近查看", "wiki.desk.recent.visitTime": "访问时间", "wiki.desk.recent.viewTime": "查看时间", "wiki.desk.recent.editTime": "编辑时间", "wiki.desk.recent.invalid": "已失效", "wiki.draft.title": "草稿", "wiki.draft.notExist": "草稿不存在", "wiki.draft.delete": "删除草稿", "wiki.draft.publish": "发布", "wiki.draft.abandon": "放弃草稿", "wiki.draft.saveHint": "通过模板创建新页面时会将当前页面编辑的内容保存为草稿", "wiki.draft.deleteConfirm": "草稿删除后无法恢复，已发布的页面不受影响。", "wiki.action.newBoard": "新建画板", "wiki.action.newDoc": "新建文档", "wiki.action.newGroup": "新建分组", "wiki.action.newFromTemplate": "从模板新建", "wiki.page.type": "页面类型", "wiki.page.parent": "父页面", "wiki.page.space": "所属空间", "wiki.page.group": "分组", "wiki.page.discussion": "会话", "wiki.page.attachment.upload": "本地上传", "wiki.page.attachment.dragHint": "将文件拖拽至此上传附件", "wiki.page.attachment.title": "页面附件", "wiki.page.attachment.count": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">个附件</span>", "wiki.page.copy.withChildren": "连同子页面一起复制", "wiki.page.copy.select": "选择页面", "wiki.page.copy.toSpace": "复制至空间", "wiki.page.copy.batchHint": "批量复制时会同时复制子项，通过拖拽页面或点击选择父级将其复制至新位置", "wiki.page.copy.hint": "通过拖拽页面或点击选择父级将其复制至新位置", "wiki.page.copy.title": "复制页面", "wiki.page.copy.validation.spaceRequired": "所选空间不能为空", "wiki.page.copy.validation.pageRequired": "所属页面不能为空", "wiki.page.edit.title": "编辑提示", "wiki.page.edit.notifyFollowers": "页面有较大更新，通知页面关注者", "wiki.page.edit.publishOptions": "发布选项", "wiki.page.edit.noPermission": "您没有操作该功能的权限,请联系企业拥有者或者管理员", "wiki.page.edit.publish": "发布", "wiki.page.edit.publishing": "发布中...", "wiki.page.edit.lastSaved": "{{display_name}} 最后保存于 {{updated_at}}", "wiki.page.sharing.title": "共享", "wiki.page.sharing.viewOnly": "仅查看", "wiki.page.sharing.editable": "可编辑", "wiki.page.sharing.configuring": "设置中...", "wiki.page.sharing.selectMembers": "选择成员、部门或团队", "wiki.page.sharing.permissions": "页面权限", "wiki.page.sharing.independentPermission": "设置独立权限，控制空间成员的可见范围", "wiki.page.sharing.inheritPermission": "设置权限后（除默认权限），子页面默认继承父页面权限，同时支持设置独立权限。", "wiki.page.sharing.defaultPermission": "默认权限", "wiki.page.sharing.defaultWithParent": "默认与父页面权限一致。", "wiki.page.sharing.defaultWithSpace": "默认与空间权限一致。", "wiki.page.sharing.allMemberViewEdit": "所有的空间成员均可查看和编辑", "wiki.page.sharing.allMemberViewEditHint": "空间的所有成员都能查看和编辑。", "wiki.page.sharing.allMemberViewPartEdit": "所有的空间成员均可查看，部分人可以编辑", "wiki.page.sharing.allMemberViewPartEditHint": "空间的所有成员都能查看，只有指定的成员可编辑。", "wiki.page.sharing.specifiedMember": "只有指定的空间成员可以查看或编辑", "wiki.page.sharing.specifiedMemberHint": "设置后，添加的指定空间成员可以查看或者编辑此页面", "wiki.page.sharing.shareWithChildren": "同时共享子页面，展示为页面树", "wiki.page.sharing.pageLink": "页面链接", "wiki.page.sharing.nonSpaceMemberHint": "添加访问权限，非空间成员可在【与我共享】界面查看或编辑", "wiki.page.sharing.switchConfirm": "切换后，当前设置将不生效，确认切换吗？", "wiki.page.sharing.exitConfirm": "退出后，当前页面的权限设置将不生效，确认退出吗？", "wiki.page.history.version": "版本", "wiki.page.history.showDiff": "显示内容差异", "wiki.page.history.showPublished": "仅显示已发布版本", "wiki.page.history.restore": "恢复此版本", "wiki.page.history.saveNamed": "保存命名版本", "wiki.page.info.title": "页面信息", "wiki.page.info.info": "信息", "wiki.page.info.compare": "版本比对", "wiki.page.info.visitRecord": "访问记录", "wiki.page.info.charCount": "字符数", "wiki.page.info.charCountWithSpace": "字符数（计空格）", "wiki.page.info.charCountNoSpace": "字符数（不计空格）", "wiki.page.info.totalWords": "总字数", "wiki.page.info.charCountWithSpaceHint": "（计空格）", "wiki.page.info.charCountNoSpaceHint": "（不计空格）", "wiki.page.move.title": "移动页面", "wiki.page.move.toSpace": "移动至空间", "wiki.page.move.alert": "移动页面时会同时移动子项，通过拖拽页面或点击选择父级将其移至新位置", "wiki.page.dialog.edit": "编辑（E）", "wiki.page.dialog.askAI": "问拼扣", "wiki.page.dialog.minimize": "最小化", "wiki.page.dialog.cancelMinimize": "取消最小化", "wiki.page.dialog.present": "演示（{{key}}+Shift+P）", "wiki.page.dialog.copyLink": "复制链接", "wiki.page.dialog.openInSpace": "空间中打开", "wiki.page.dialog.comment": "评论", "wiki.page.dialog.hideComment": "收起评论", "wiki.page.version.saveNamed": "保存为命名版本", "wiki.page.version.all": "全部版本", "wiki.page.version.named": "命名版本", "wiki.page.version.compare": "与版本", "wiki.page.version.restore.description": "该操作将产生一个新的页面版本，内容与{{name}}相同", "wiki.page.version.restore.confirm": "确认恢复版本<span class=\"styx-confirm-target styx-confirm-target-primary text-truncate single-line\">{{name}}</span>吗?", "wiki.page.create.child": "新建子页面", "wiki.page.create.above": "上方新建页面", "wiki.page.create.below": "下方新建页面", "wiki.page.delete.description": "删除页面会一起删除子页面，可以通过空间设置里的回收站进行恢复。", "wiki.page.delete.confirmMultiple": "确认删除选择的<span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span>个页面吗？", "wiki.page.delete.confirmSingle": "确认删除页面<span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{name}}</span>？", "wiki.page.delete.byUser": "{{display_name}}于{{time}}删除了页面", "wiki.page.name.placeholder": "输入标题", "wiki.page.import.sizeLimit": "文件大小不能超过 1 G", "wiki.page.import.success": "<p class=\"styx-confirm-container\">已导入<span class=\"styx-confirm-target styx-confirm-target-primary\">{{count}}</span>个页面，点击查看页面详情。</p>", "wiki.page.import.attachmentSizeLimit": "附件内容大小不能超过 {{size}}", "wiki.page.export.file": "{{type}} 文件", "wiki.page.export.success": "<div class=\"styx-confirm-target-wrap\">已将页面导出为{{content}}，点击下载<span class=\"styx-confirm-target styx-confirm-target-primary download-page-dialog text-truncate\">{{fileName}}</span></div>", "wiki.page.total.count": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个页面</span>", "wiki.page.operation.edit": "编辑（E）", "wiki.page.operation.ai": "问拼扣", "wiki.page.operation.present": "演示（{key}+Shift+P）", "wiki.page.operation.presentMode": "演示模式", "wiki.page.operation.favorite": "收藏页面", "wiki.page.operation.unfavorite": "取消收藏", "wiki.page.operation.lock": "锁定页面", "wiki.page.operation.fullWidth": "宽屏模式", "wiki.page.operation.print": "打印页面", "wiki.page.operation.export": "导出为", "wiki.page.operation.details": "查看详情", "wiki.page.publish.success": "发布成功", "wiki.page.publish.time": "发布于", "wiki.page.reading": "阅读 · {{count}}", "wiki.page.noPermission": "没有权限！", "wiki.page.template.namePlaceholder": "输入模板名称", "wiki.page.untitled.document": "无标题文档", "wiki.page.untitled.board": "无标题画板", "wiki.presentation.exit": "退出演示模式", "wiki.presentation.timer": "关灯计时", "wiki.presentation.pointer": "激光笔", "wiki.discussion.comments": "{{display_name}}等人共发表了 {{count}} 条评论", "wiki.discussion.mention.placeholder": "@提及成员（Ctrl+Enter发送）", "wiki.discussion.header.commentCount": "共{{count}}条评论", "wiki.discussion.header.solveDiscussion": "解决会话", "wiki.discussion.header.reopenDiscussion": "重新打开", "wiki.discussion.footer.resolvedBy": "已由 {{name}} 解决于 {{time}}", "wiki.discussion.action.reply": "回复", "wiki.discussion.action.delete": "删除", "wiki.discussion.reply.prefix": "回复 @{{name}} : \n >{{content}}", "wiki.discussion.version.name": "版本名称", "wiki.collaboration.editing": "人正在编辑", "wiki.guider.page.operations": "，页面相关操作都在这里：页面共享、查看历史版本、锁定最终版本、导出文件，更多操作等你发现。", "wiki.guider.page.editor": "，进入编辑器进行文档编写。编辑器提供丰富的页面元素、支持Markdown语法、多人协同编辑并实时保存协作内容。", "wiki.guider.page.draft": "，空间中已编辑未发布的页面都展示在这里，页面编辑过程中自动保存至草稿。", "wiki.guider.page.new": "，为空间添加新的页面。提供结构化页面树，如需为某页面添加子页面，点击页面的更多-新建进行操作。", "wiki.guider.page.editPage": "编辑页面", "wiki.guider.page.accessSpace": "访问空间", "wiki.guider.page.createSpace": "新建空间", "wiki.guider.space.settings": "，进行空间、页面模板、角色权限配置等全局管理。", "wiki.guider.space.shared": "，右侧内容区展示开启共享的页面列表，如果成员将你加入到某个页面的共享中，可以从这里进行查看。", "wiki.guider.space.list": "，右侧内容区展示我参与的全部空间列表，点击空间查看空间的页面树及页面详情。", "wiki.guider.space.purpose": "，构建团队知识体系，可以是企业制度规范，可以是项目文档，也可以是接口文档，随心所欲。", "wiki.knowledgeGraph.reference": "引用", "wiki.knowledgeGraph.referenced": "被引用", "wiki.knowledgeGraph.title": "知识图谱", "wiki.knowledgeGraph.pageInfo": "页面信息", "wiki.knowledgeGraph.relatedItems": "关联事项", "wiki.knowledgeGraph.pageAttachments": "页面附件", "wiki.space.identifier": "空间标识", "wiki.space.description": "输入空间描述", "wiki.space.identifierHint": "空间标识将作为页面编号的前缀", "wiki.space.namePlaceholder": "输入空间名称", "wiki.space.member": "空间成员", "wiki.space.template": "空间模板", "wiki.space.sharing": "空间共享", "wiki.space.component": "空间组件", "wiki.space.directory": "目录管理", "wiki.space.statistics": "数据统计", "wiki.space.nameRequired": "空间名称不能为空", "wiki.space.identifierRequired": "空间标识不能为空", "wiki.space.identifierExists": "该标识已被空间使用，请使用其他标识", "wiki.space.removeMemberHint": "移除后该成员将无法再编辑当前空间，该成员产生的数据不受影响。", "wiki.space.archiveHint": "归档后如果想重新使用该空间，可以通过配置中心由管理员进行激活。", "wiki.space.deleteHint": "删除后如果想找回空间，可以通过配置中心由管理员进行恢复。", "wiki.space.baselineHint": "基线设立后，当前基线下页面版本不再变更。", "wiki.space.componentConfigHint": "修改当前空间组件的配置后，不会同步修改已有用户的自定义配置", "wiki.space.delete": "删除空间", "wiki.space.activate": "激活空间", "wiki.space.archive": "归档空间", "wiki.space.move": "移动空间", "wiki.space.info": "空间信息", "wiki.space.copy": "复制空间", "wiki.space.exportTree": "导出页面树", "wiki.space.stencils": "页面模板", "wiki.space.exportPage.select": "选择导出页面 · {{selectPagesCount}} ", "wiki.space.exportPage.maxTip": "最多可选 {{count}} 个页面，批量导出为 PDF", "wiki.space.identifierPattern": "英文字母/下划线/数字/连接线（不超过15字符）", "wiki.space.newSpaceDescription": "空间是记录信息和知识的页面集合，通过组织页面层级将知识系统化、结构化，在知识管理层面助力企业更快更好的发布产品。", "wiki.space.settings.move.description": "如果要变更空间的所属关系及可见范围，你可以移动它。移动后不影响空间下的页面。", "wiki.space.settings.archive.description": "如果此空间已经结束了，你可以归档它。归档后如果想重新使用该空间，可以通过配置中心由管理员进行激活。", "wiki.space.settings.activate.description": "如果想重新使用该空间，你可以激活它。", "wiki.space.settings.delete.description": "如果此空间已经不再需要了，你可以删除它。删除后如果想找回空间，可以通过配置中心由管理员进行恢复。", "wiki.space.settings.directory.level": "层级", "wiki.space.settings.directory.levelTooltip": "页面层级", "wiki.space.settings.directory.showAll": "展示全部层级", "wiki.space.settings.directory.showLevel": "展示 {{level}} 级", "wiki.space.settings.directory.viewPage": "查看页面", "wiki.space.settings.pageShared.nonSpaceMember": "非空间成员", "wiki.space.settings.pageShared.publicShared": "公开共享", "wiki.space.settings.spaceShared.title": "共享空间", "wiki.space.settings.share.success": "外部共享内容已更新", "wiki.space.settings.share.logoSizeLimit": "logo文件不能大于1M", "wiki.space.settings.memberDetails.department": "部门", "wiki.space.settings.memberDetails.position": "职位", "wiki.space.settings.identifier.pattern": "英文字母/下划线/数字/连接线（不超过15字符）", "wiki.space.empty.title": "未关联空间", "wiki.space.empty.desc": "当前{{type}}未关联空间，关联空间后，可以查看空间的页面列表", "wiki.space.relate.title": "关联空间", "wiki.space.relate.select": "请选择空间", "wiki.space.relate.success": "{{title}}成功", "wiki.space.selector.empty": "空", "wiki.template.center": "模板中心", "wiki.template.organization": "组织模板", "wiki.template.space": "空间模板", "wiki.template.name": "模板名称", "wiki.template.saveAs": "另存为模板", "wiki.template.orgScope": "（组织所有空间可用）", "wiki.template.spaceScope": "（当前空间可用）", "wiki.template.create": "从模板新建页面", "wiki.template.exitConfirm.title": "确定要退出编辑模板 {{name}} 吗？", "wiki.template.exitConfirm.description": "退出编辑将无法保存修改的内容，且不能找回。", "wiki.template.count": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个模版</span>", "wiki.error.unknown": "未知错误", "wiki.error.invalidImageType": "不被允许的图片类型", "wiki.error.imageTooLarge": "图片太大了", "wiki.error.operationFailed": "操作失败", "wiki.error.noPermission": "你没有该页面操作权限！", "wiki.error.pageLocked": "该页面已锁定，不能进行该操作！", "wiki.error.pageBeingEdited": "当前页面正在被编辑", "wiki.error.networkDisconnected": "网络已中断", "wiki.error.waitForReconnect": "请等待重新连接后继续编辑", "wiki.error.confirmExit": "确定要退出编辑吗？", "wiki.error.waitForUpload": "退出编辑将无法保存上传内容，请等待上传再退出", "wiki.error.messageServiceDisconnected": "消息服务已中断", "wiki.error.permissionRemoved": "该页面编辑权限被移除，请联系管理员", "wiki.error.pageDeleted": "该页面已被删除，请联系管理员", "wiki.error.pageNotExist": "页面不存在或者你没有权限查看该页面", "wiki.error.noAccessPermission": "你没有该页面的访问权限", "wiki.error.deletePermissionDenied": "你没有页面的删除权限！", "wiki.error.movePermissionDenied": "你没有页面的移动权限！", "wiki.error.copyPermissionDenied": "你没有页面的复制权限！", "wiki.error.editPermissionDenied": "你没有该页面编辑权限！", "wiki.error.contentExceedLimit": "内容超限，请重新再试", "wiki.error.unauthorized": "未授权使用", "wiki.error.moveError": "移动失败", "wiki.error.moveNoPermission": "你没有目标页面编辑权限！", "wiki.error.exportError": "导出失败", "wiki.error.exportLinkExpired": "下载链接过期，请重新导出", "wiki.error.spaceNameRequired": "空间名称不能为空", "wiki.error.spaceIdentifierRequired": "空间标识不能为空", "wiki.error.spaceIdentifierExists": "该标识已被空间使用，请使用其他标识", "wiki.error.templateNameRequired": "模板名称不能为空", "wiki.error.templateScopeRequired": "使用范围不能为空", "wiki.error.titleLengthExceed": "标题长度不能超过32字符", "wiki.error.title.operationFailed": "操作失败", "wiki.error.title.editFailed": "编辑失败", "wiki.error.title.deleteFailed": "删除失败", "wiki.error.title.moveFailed": "移动失败", "wiki.error.title.copyFailed": "复制失败", "wiki.error.title.createFailed": "创建失败", "wiki.error.title.publishFailed": "发布失败", "wiki.error.title.pageBeingEdited": "当前页面正在被编辑", "wiki.error.message.noPermission": "你没有该页面操作权限！", "wiki.error.message.pageLocked": "该页面已锁定，不能进行该操作！", "wiki.error.message.restoreConflict": "恢复会导致编辑的内容丢失，请等待其他用户退出编辑后再进行恢复！", "wiki.error.message.deleteNoPermission": "你没有页面的删除权限！", "wiki.error.message.moveNoPermission": "你没有页面的移动权限！", "wiki.error.message.copyNoPermission": "你没有页面的复制权限！", "wiki.error.message.editNoPermission": "你没有该页面编辑权限！", "wiki.error.message.collaborativeEditLimit": "检测到当前页面已有{{expect_value}}人正在协同编辑，为保证编辑体验，请稍后编辑！", "wiki.error.message.editPageLimit": "检测到当前你已打开{{expect_value}}个编辑页面，为保证编辑体验，请关闭其他页面！", "wiki.error.message.usageLimit": "使用次数已达到上限", "wiki.error.message.unauthorized": "未授权使用", "wiki.error.message.contentExceedLimit": "内容超限，请重新再试", "wiki.error.message.sensitiveContent": "内容包含敏感词，请检查后重试", "wiki.error.message.retryLater": "操作失败，请重新再试", "wiki.import.title": "导入文档", "wiki.import.cancel": "取消导入", "wiki.import.failed": "导入失败", "wiki.import.complete": "导入完成", "wiki.import.fileNotExist": "文件不存在", "wiki.import.invalidFormat": "文件格式错误", "wiki.import.supportMarkdown": "支持导入以 .markdown，.md，.mark，.txt 结尾的文件", "wiki.import.supportZip": "支持导入以 .zip 结尾的文件", "wiki.import.supportDocx": "支持导入以 .docx 结尾的文件", "wiki.import.supportConfluence": "支持 Confluence 空间中以 HTML 格式导出的 zip 文件", "wiki.export.title": "导出页面", "wiki.export.cancel": "取消导出", "wiki.export.retry": "重新导出", "wiki.export.failed": "导出失败", "wiki.export.success": "导出成功", "wiki.export.downloading": "下载中...", "wiki.export.exporting": "页面导出中...", "wiki.export.linkExpired": "下载链接过期，请重新导出", "wiki.export.emptyPages": "导出页面不能为空", "wiki.baseline.comparison": "版本比对", "wiki.baseline.pages.plan": "规划页面", "wiki.baseline.remove.confirm.multiple": "确认移出选择的<span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span>个页面吗？", "wiki.baseline.remove.confirm.single": "确认移出页面{{name}}吗？", "wiki.baseline.remove.confirm.description": "移出后可以重新规划页面。", "wiki.statistics.title": "数据统计", "wiki.statistics.overview": "数据概览", "wiki.statistics.newThisWeek": "本周新增", "wiki.statistics.memberDetails": "成员明细", "wiki.statistics.pageDetails": "页面明细", "wiki.statistics.member.name": "姓名", "wiki.statistics.member.createdPages": "创建页面数", "wiki.statistics.member.editedPages": "参与编辑页面数", "wiki.statistics.member.department": "部门", "wiki.statistics.member.position": "职位", "wiki.statistics.member.count": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个成员</span>", "wiki.statistics.page.name": "页面名称", "wiki.statistics.page.versions": "版本数", "wiki.statistics.page.words": "字数", "wiki.statistics.page.reads": "阅读量", "wiki.statistics.page.comments": "评论量", "wiki.statistics.deleteTime": "删除时间", "wiki.statistics.deleteBy": "删除人", "wiki.statistics.restoreSuccess": "恢复成功", "wiki.resource.productRequirement": "产品需求", "wiki.resource.workOrder": "工单", "wiki.resource.testCase": "测试用例", "wiki.resource.objective": "目标", "wiki.resource.relatedItems": "关联事项", "wiki.sort.byViewTime": "按查看时间排序", "wiki.sort.byPublishTime": "按发布时间排序", "wiki.sort.byCreateTime": "按创建时间排序", "wiki.sort.byTitle": "按标题排序", "wiki.spaceSharing.title": "空间共享", "wiki.spaceSharing.enabled": "已开启", "wiki.spaceSharing.hint": "开启共享后，获得链接的人可以访问该空间。", "wiki.spaceSharing.displaySettings": "展示设置", "wiki.spaceSharing.contentSettings": "内容设置", "wiki.spaceSharing.brandLogo": "品牌标识", "wiki.spaceSharing.brandLogoHint": "（仅支持JPG、PNG格式图片，建议100x30）", "wiki.spaceSharing.uploadLogo": "点击上传", "wiki.spaceSharing.copyright": "版权信息", "wiki.spaceSharing.showCopyright": "是否显示版权信息", "wiki.spaceSharing.customPages": "自定义页面", "wiki.spaceSharing.allPages": "全部页面", "wiki.spaceSharing.selectSharePages": "选择共享页面", "wiki.spaceSharing.sharePagesRequired": "共享页面不能为空", "wiki.spaceSharing.exitConfirm": "退出后，当前空间外部共享内容的设置将不生效，确认退出吗？", "wiki.common.confirm.delete": "确定删除", "wiki.common.confirm.deleteComment": "确定要删除评论吗？", "wiki.common.confirm.exit": "确定要退出编辑吗？", "wiki.common.confirm.restore": "确认恢复", "wiki.common.confirm.publish": "确认发布", "wiki.common.confirm.moveOut": "确认移出", "wiki.common.status.reading": "阅读 · {{reading}}", "wiki.common.status.publishedAt": "发布于 {{updated_at}}", "wiki.common.action.openInSpace": "空间中打开", "wiki.common.action.scanQRCode": "扫一扫", "wiki.common.action.viewInMiniApp": "在小程序查看页面", "wiki.common.action.searchPage": "输入页面标题搜索", "wiki.common.action.browseRecent": "最近浏览", "wiki.common.action.newPage": "新建页面", "wiki.common.action.saveAsTemplate": "另存为模板", "wiki.common.action.saveNamedVersion": "保存命名版本", "wiki.common.action.publishWithNotify": "发布并通知关注者", "wiki.common.action.publishPage": "发布页面", "wiki.common.count.subPages": "等 {{count}} 个页面", "wiki.common.count.includeSubPages": "包含 {{count}} 个子页面", "wiki.configuration.title": "空间配置", "wiki.configuration.manage.title": "空间管理", "wiki.configuration.manage.shareSettings": "共享设置", "wiki.configuration.manage.deleteHint": "删除后如果想找回空间，可以通过配置中心由管理员进行恢复。", "wiki.configuration.manage.column.sharedBy": "共享人", "wiki.configuration.manage.column.sharedAt": "共享时间", "wiki.configuration.space.componentPermission": "组件权限", "wiki.configuration.space.configHint": "修改组件的全局配置后，不会同步修改已有数据的配置", "wiki.configuration.review.description": "自定义空间内的评审流程及规则", "wiki.configuration.review.configHint": "修改评审配置后，不会影响已有数据；开启本地化配置后，当前评审配置不再受全局配置影响", "wiki.configuration.stencil.delete.description": "删除后无法恢复。", "wiki.configuration.tags.deleteDescription": "标签删除后不可恢复。共 {{related_count}} 个页面正在使用此标签，删除后会从对应页面中移除。", "wiki.plugin.alert": "提示框", "wiki.plugin.alertDesc": "使用彩色提示框高亮重点内容", "wiki.plugin.attachment": "已有文件", "wiki.plugin.attachmentDesc": "支持插入附件列表中的内容", "wiki.plugin.attachmentCloud": "本地文件", "wiki.plugin.attachmentCloudDesc": "支持 Office、PDF 等文件", "wiki.plugin.audio": "本地音频", "wiki.plugin.audioDesc": "支持在线播放音频", "wiki.plugin.date": "日期", "wiki.plugin.mindmap": "思维导图", "wiki.plugin.board": "画板", "wiki.plugin.flowchart": "流程图", "wiki.plugin.formula": "LaTex 公式", "wiki.plugin.formulaDesc": "支持插入 LaTex 语法公式", "wiki.plugin.tagDesc": "自定义状态标签", "wiki.plugin.layout": "布局", "wiki.plugin.layoutDesc": "使用布局分栏展示页面内容", "wiki.plugin.outline": "大纲", "wiki.plugin.pageOutline": "页面大纲", "wiki.plugin.pageOutlineDesc": "插入本页面的大纲", "wiki.plugin.saveDraft": "保存草稿", "wiki.plugin.fontSize": "字号", "wiki.plugin.insert": "插入", "wiki.plugin.productManage": "产品管理", "wiki.plugin.projectManage": "项目管理", "wiki.plugin.testManage": "测试管理", "wiki.plugin.wikiManage": "知识管理", "wiki.plugin.collaborativeSpace": "协作空间", "wiki.plugin.relationIdea": "产品需求", "wiki.plugin.relationIdeaList": "产品需求列表", "wiki.plugin.relationObjectiveList": "目标列表", "wiki.plugin.relationPageTree": "页面树", "wiki.plugin.relationTestCaseList": "测试用例列表", "wiki.plugin.relationTicketList": "工单列表", "wiki.plugin.relationWorkItemList": "工作项列表", "wiki.plugin.searchReplace": "搜索替换", "wiki.plugin.textDiagram": "文本绘图", "wiki.plugin.toggleList": "折叠列表", "wiki.plugin.video": "视频", "wiki.plugin.localVideo": "本地视频", "wiki.plugin.graphics": "图形", "wiki.plugin.placeholder": "输入 '/' 快速插入内容", "wiki.plugin.newNode": "新建节点", "wiki.plugin.end": "结束", "wiki.plugin.yes": "是", "wiki.plugin.no": "否", "wiki.plugin.process": "过程", "wiki.plugin.decision": "判断", "wiki.attachments.tabs.picture": "图片", "wiki.attachments.tabs.audio": "音频", "wiki.attachments.tabs.video": "视频", "wiki.validator.space.required": "所选空间不能为空", "wiki.validator.page.required": "所属页面不能为空", "wiki.pagination.range": "第 ", "wiki.permission.type.readonly": "仅查看", "wiki.permission.type.editable": "可编辑", "wiki.permission.page.default.name": "默认权限", "wiki.permission.page.default.desc": "当前页面的权限默认与空间权限一致。", "wiki.permission.page.allReadAndEdit.name": "所有的空间成员均可查看和编辑", "wiki.permission.page.allReadAndEdit.desc": "空间的所有成员都能查看和编辑。", "wiki.permission.page.allReadSpecificEdit.name": "所有的空间成员均可查看，部分人可以编辑", "wiki.permission.page.allReadSpecificEdit.desc": "空间的所有成员都能查看，只有指定的成员可编辑。", "wiki.permission.page.specificReadAndEdit.name": "只有指定的空间成员可以查看或编辑", "wiki.permission.page.specificReadAndEdit.desc": "设置后，添加的指定空间成员可以查看或者编辑此页面。", "wiki.property.review.status": "评审状态", "wiki.property.review.result": "评审结果", "wiki.property.importance": "重要程度", "wiki.pricing.expired.description": "提供结构化空间来记载信息和知识，便于团队沉淀经验、共享资源", "wiki.pricing.features.pageExport.title": "页面导出", "wiki.pricing.features.pageExport.subTitle": "将页面内容导出为文件，支持 PDF、Word 和 Markdown 格式", "wiki.pricing.features.pageExport.offlineReading": "离线阅读", "wiki.pricing.features.pageExport.contentStyle": "无差别内容样式", "wiki.pricing.features.pageTreeExport.title": "导出页面树", "wiki.pricing.features.pageTreeExport.subTitle": "将页面内容批量导出为 PDF 文件，最多可选 100 个页面", "wiki.pricing.features.pageTemplate.title": "页面模板", "wiki.pricing.features.pageTemplate.subTitle": "让团队的知识沉淀标准化", "wiki.pricing.features.pageTemplate.orgTemplate": "组织模板管理", "wiki.pricing.features.pageTemplate.spaceTemplate": "空间模板管理", "wiki.pricing.features.pageShare.title": "页面共享", "wiki.pricing.features.pageShare.subTitle": "灵活设置不同角色的页面权限，促进企业内外部知识资源的传播共享", "wiki.pricing.features.pageShare.memberPermission": "支持给空间成员设置页面独立权限", "wiki.pricing.features.pageShare.externalPermission": "支持给空间外部成员设置共享权限", "wiki.pricing.features.pageShare.publicShare": "支持生成共享链接对外公开页面", "wiki.pricing.features.embedment.title": "插入本地音频/视频文件", "wiki.pricing.features.embedment.subTitle": "支持 mp4、mkv、webm、mov、mp3、wma、wav、ape、flac、ogg、m4r、m4a 格式", "wiki.pricing.features.embedment.onlinePlay": "视频/音频文件在线播放", "wiki.pricing.features.embedment.richContent": "丰富页面的内容，展示多元化信息", "wiki.pricing.features.spaceShare.title": "空间共享", "wiki.pricing.features.spaceShare.subTitle": "生成共享链接使知识资源传播更广泛", "wiki.pricing.features.spaceShare.expiry": "设置有效期", "wiki.pricing.features.spaceShare.password": "设置访问密码", "wiki.pricing.features.spaceShare.qrCode": "扫码阅读", "wiki.review.title": "标题", "wiki.review.versionCompare": "版本比对", "wiki.review.removeConfirm.title": "确认移出", "wiki.review.removeConfirm.action": "移出", "wiki.review.removeConfirm.multiple": "确认移出选择的<span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span>个页面吗？", "wiki.review.removeConfirm.single": "<span class=\"styx-confirm-nowrap\">确认移出页面</span>{{name}}吗？", "wiki.review.removeConfirm.description": "移出后可以重新添加{{text}}。", "wiki.tag.delete.description": "标签删除后不可恢复。共 {{related_count}} 个页面正在使用此标签，删除后会从对应页面中移除。", "wiki.relation.disconnect.description": "取消关联后可以重新建立关联。", "wiki.relation.disconnect.confirm": "确认取消与页面{name}的关联吗？", "wiki.relation.pageTitle": "搜索页面标题", "wiki.relation.pageDeleted": "该页面已被删除或没有权限查看", "wiki.relation.moreWorkItems": "更多工作项", "wiki.relation.workItemTitle": "搜索工作项标题或编号", "wiki.relation.workItemDeleted": "该工作项已被删除或没有权限查看", "wiki.relation.moreCases": "更多用例", "wiki.relation.caseTitle": "搜索测试用例标题或编号", "wiki.relation.caseDeleted": "该测试用例已被删除或没有权限查看", "wiki.relation.moreRequirements": "更多需求", "wiki.relation.requirementTitle": "搜索产品需求标题或编号", "wiki.relation.requirementDeleted": "该产品需求已被删除或没有权限查看", "wiki.relation.moreTickets": "更多工单", "wiki.relation.ticketTitle": "搜索工单标题或编号", "wiki.relation.ticketDeleted": "该工单已被删除或没有权限查看", "wiki.relation.moreObjectives": "更多目标", "wiki.relation.objectiveTitle": "搜索目标标题", "wiki.relation.objectiveDeleted": "该目标已被删除或没有权限查看", "wiki.media.title": "插入本地音频/视频文件", "wiki.media.onlinePlay": "视频/音频文件在线播放", "wiki.member.all": "所有人", "wiki.document.wordCount": "共 {{count}} 字", "wiki.document.emoji.change": "更改表情", "wiki.document.emoji.standardMode": "标准模式", "wiki.document.emoji.remove": "移除表情", "wiki.document.emoji.add": "添加表情", "wiki.document.placeholder": "请输入内容", "wiki.document.imageError.sizeLimit": "不支持上传50M以上图片。", "wiki.document.imageError.formatError": "不支持上传的图片格式。", "wiki.shortcuts.title": "快捷键", "wiki.shortcuts.textFormat": "文本格式", "wiki.shortcuts.markdownSyntax": "Markdown 语法", "wiki.shortcuts.canvas": "画布", "wiki.shortcuts.fontFormat": "文字格式", "wiki.shortcuts.markdown.heading1": "标题1", "wiki.shortcuts.markdown.heading2": "标题2", "wiki.shortcuts.markdown.heading3": "标题3", "wiki.shortcuts.markdown.heading4": "标题4", "wiki.shortcuts.markdown.heading5": "标题5", "wiki.shortcuts.markdown.heading6": "标题6", "wiki.shortcuts.markdown.bold": "**粗体**", "wiki.shortcuts.markdown.italic": "*斜体*", "wiki.shortcuts.markdown.strikethrough": "~~删除线~~", "wiki.shortcuts.actions.zoom": "缩放", "wiki.shortcuts.actions.scroll": "滚动", "wiki.shortcuts.actions.actualSize": "实际大小", "wiki.shortcuts.actions.insertSubset": "插入子集", "wiki.shortcuts.actions.insertSibling": "插入同级", "wiki.shortcuts.actions.undo": "撤销", "wiki.shortcuts.actions.redo": "重做", "wiki.shortcuts.actions.quickInsert": "快速插入菜单", "wiki.shortcuts.actions.saveDraft": "保存草稿", "wiki.shortcuts.actions.increaseIndent": "增加缩进", "wiki.shortcuts.actions.decreaseIndent": "减少缩进", "wiki.shortcuts.actions.emoji": "表情符号", "wiki.shortcuts.actions.mention": "提及", "wiki.shortcuts.actions.bold": "粗体", "wiki.shortcuts.actions.italic": "斜体", "wiki.shortcuts.actions.underline": "下划线", "wiki.shortcuts.actions.inlineCode": "行内代码", "wiki.shortcuts.actions.selectCell": "选择单元格", "wiki.shortcuts.actions.selectCellRange": "选择单元格区域", "wiki.shortcuts.actions.multiSelectCell": "多选单元格", "wiki.shortcuts.actions.strikethrough": "删除线", "wiki.shortcuts.actions.orderedList": "有序列表", "wiki.shortcuts.actions.unorderedList": "无序列表", "wiki.shortcuts.actions.todoList": "待办列表", "wiki.shortcuts.actions.codeBlock": "代码块", "wiki.shortcuts.actions.divider": "分割线", "wiki.shortcuts.actions.editBoard": "编辑画板", "wiki.shortcuts.actions.softEnter": "换行(软回车)", "wiki.shortcuts.actions.expandCollapseNode": "展开/折叠节点", "wiki.material.use": "使用", "wiki.material.useCase": "使用场景", "wiki.material.library": "素材库", "wiki.material.all": "全部素材", "wiki.preview.createItem": "创建事项", "wiki.preview.comment": "发起评论", "wiki.search.noResult": "未搜索到任何数据", "wiki.search.replace.next": "下一个", "wiki.search.replace.previous": "上一个", "wiki.search.replace.replace": "替换", "wiki.search.replace.replaceAll": "全部替换", "wiki.search.replace.input": "请输入", "wiki.search.replace.replaceTo": "替换为", "wiki.search.replace.find": "查找", "wiki.plugins.alert.backgroundColor": "背景色", "wiki.plugins.alert.deleteEmoji": "删除表情", "wiki.plugins.alert.addEmoji": "添加表情", "wiki.plugins.alert.danger": "危险", "wiki.plugins.attachment.linkFailed": "文件关联失败，通过附件列表关联或重新上传", "wiki.plugins.attachment.deleted": "文件已被删除", "wiki.plugins.attachment.select": "选择已有文件", "wiki.plugins.attachment.upload": "从本地上传", "wiki.plugins.diagramBoard.exportImage": "导出图片", "wiki.plugins.formula.input": "输入 LaTeX 公式", "wiki.plugins.formula.empty": "公式不能为空", "wiki.plugins.formula.invalid": "非法 LaTex 公式", "wiki.plugins.label.inputName": "输入标签名称", "wiki.plugins.label.set": "设置标签", "wiki.plugins.layout.twoColumns": "两列", "wiki.plugins.layout.threeColumns": "三列", "wiki.plugins.layout.leftSidebar": "左边栏", "wiki.plugins.layout.rightSidebar": "右边栏", "wiki.plugins.layout.bothSidebars": "左右边栏", "wiki.plugins.outline.list": "列表", "wiki.plugins.outline.showNodes": "显示节数", "wiki.plugins.outline.level1": "一级", "wiki.plugins.outline.level2": "二级", "wiki.plugins.outline.level3": "三级", "wiki.plugins.outline.level4": "四级", "wiki.plugins.outline.levelTitle": "大纲层级", "wiki.plugins.outline.options": "大纲选项", "wiki.plugins.toggleList.clickToInput": "点击输入内容", "wiki.plugins.toggleList.inputTitle": "输入折叠标题", "wiki.plugins.toggleList.placeholder": "请输入...", "wiki.plugins.textDiagram.templates.sequence.name": "时序图(Sequence)", "wiki.plugins.textDiagram.templates.sequence.actors.user": "用户", "wiki.plugins.textDiagram.templates.sequence.actors.browser": "浏览器", "wiki.plugins.textDiagram.templates.sequence.actors.server": "服务端", "wiki.plugins.textDiagram.templates.sequence.actions.inputUrl": "输入 URL", "wiki.plugins.textDiagram.templates.sequence.actions.requestServer": "请求服务器", "wiki.plugins.textDiagram.templates.sequence.actions.renderTemplate": "模板渲染", "wiki.plugins.textDiagram.templates.sequence.actions.returnHtml": "返回 HTML", "wiki.plugins.textDiagram.templates.sequence.actions.comment": "这是一个注释", "wiki.plugins.textDiagram.templates.useCase.name": "用例图(Use Case)", "wiki.plugins.textDiagram.templates.class.name": "类图(Class)", "wiki.plugins.textDiagram.templates.flow.name": "流程图(Flow)", "wiki.plugins.textDiagram.templates.activity.name": "活动图(Activity)", "wiki.plugins.textDiagram.templates.component.name": "组件图(Component)", "wiki.plugins.textDiagram.templates.state.name": "状态图(State)", "wiki.plugins.textDiagram.templates.object.name": "对象图(Object)", "wiki.plaitEditor.toolbar.arrow.endpoint": "终点", "wiki.plaitEditor.toolbar.arrow.convert": "转换", "wiki.plaitEditor.toolbar.arrow.startPoint": "起点", "wiki.plaitEditor.toolbar.arrow.border": "边框", "wiki.plaitEditor.toolbar.arrow.type": "类型", "wiki.plaitEditor.toolbar.common.fill": "节点填充", "wiki.plaitEditor.toolbar.common.shape": "切换图形", "wiki.plaitEditor.toolbar.common.link.tooltip": "链接", "wiki.plaitEditor.toolbar.common.link.urlPlaceholder": "请输入链接", "wiki.plaitEditor.toolbar.common.link.textPlaceholder": "请输入文本", "wiki.plaitEditor.toolbar.common.link.invalidUrl": "请输入正确的链接", "wiki.plaitEditor.toolbar.common.node.fill": "节点填充", "wiki.plaitEditor.toolbar.common.node.border": "节点边框", "wiki.plaitEditor.toolbar.common.text.alignment": "对齐方式", "wiki.plaitEditor.toolbar.common.text.color": "字体颜色", "wiki.plaitEditor.toolbar.draw.border": "边框", "wiki.plaitEditor.toolbar.draw.fill": "填充", "wiki.plaitEditor.toolbar.mind.summary": "概要", "wiki.plaitEditor.toolbar.mind.nodeShape": "节点形状", "wiki.plaitEditor.toolbar.mind.branch": "分支", "wiki.plaitEditor.toolbar.mind.structure": "结构", "wiki.plaitEditor.toolbar.multiple.border": "边框", "wiki.plaitEditor.toolbar.multiple.fill": "填充", "wiki.plaitEditor.toolbar.swimlane.border": "边框", "wiki.plaitEditor.toolbar.swimlane.fill": "填充", "wiki.plaitEditor.toolbar.swimlane.lanes": "泳道数", "wiki.plaitEditor.shape.recentlyUsed": "最近使用", "wiki.plaitEditor.mainToolbar.connection": "连线", "wiki.plaitEditor.mainToolbar.mindmap": "思维导图 M", "wiki.plaitEditor.rightTopToolbar.themeColor": "主题配色", "wiki.plaitEditor.rightTopToolbar.changeTheme": "更换主题", "wiki.plaitEditor.settingPanel.borderOther.width": "粗细", "wiki.plaitEditor.settingPanel.borderOther.color": "边框颜色", "wiki.plaitEditor.settingPanel.nodeOther.shape": "节点形状", "wiki.plaitEditor.settingPanel.text.alignment": "对齐方式", "wiki.plaitEditor.settingPanel.text.color": "文本颜色", "wiki.plaitEditor.settingPanel.draw.fillColor": "填充颜色", "wiki.plaitEditor.settingPanel.draw.shape": "形状", "wiki.plaitEditor.settingPanel.mind.branchType": "分支类型", "wiki.plaitEditor.settingPanel.mind.layout": "结构", "wiki.plaitEditor.settingPanel.distribution": "分布", "wiki.plaitEditor.settingPanel.border": "边框", "wiki.plaitEditor.settingPanel.swimlane": "泳道", "wiki.plaitEditor.settingPanel.endpoint": "端点", "wiki.plaitEditor.settingPanel.connection": "连线", "wiki.plaitEditor.settingPanel.node": "节点", "wiki.plaitEditor.settingPanel.branch": "分支", "wiki.plaitEditor.settingPanel.displayMode": "展示方式", "wiki.plaitEditor.settingPanel.previewConfig": "预览页配置", "wiki.plaitEditor.settingPanel.borderWidth": "边框粗细", "wiki.plaitEditor.settingPanel.displayModeEnum.auto": "自动适配", "wiki.plaitEditor.settingPanel.displayModeEnum.sameAsEdit": "与编辑一致", "wiki.plaitEditor.arrangement.leftAlign": "左对齐", "wiki.plaitEditor.arrangement.centerAlign": "左右居中", "wiki.plaitEditor.arrangement.verticalDistribute": "垂直均分", "wiki.plaitEditor.contextMenu.addText": "添加文字", "wiki.plaitEditor.contextMenu.rightAlign": "右对齐", "wiki.plaitEditor.contextMenu.horizontalDistribute": "水平均分", "wiki.plaitEditor.contextMenu.topAlign": "顶对齐", "wiki.plaitEditor.contextMenu.middleAlign": "上下居中", "wiki.plaitEditor.contextMenu.bottomAlign": "底对齐", "wiki.plaitEditor.contextMenu.addShape": "添加图形", "wiki.plaitEditor.contextMenu.addLine": "添加连线", "wiki.plaitEditor.contextMenu.addMindmap": "添加思维导图", "wiki.plaitEditor.contextMenu.paste": "粘贴", "wiki.plaitEditor.contextMenu.cut": "剪切", "wiki.plaitEditor.contextMenu.copy": "拷贝", "wiki.plaitEditor.contextMenu.arrange": "排列", "wiki.plaitEditor.contextMenu.addSummary": "添加概要", "wiki.plaitEditor.contextMenu.child": "子级", "wiki.plaitEditor.contextMenu.sibling": "同级", "wiki.plaitEditor.group.group": "成组", "wiki.plaitEditor.group.ungroup": "取消成组", "wiki.plaitEditor.geometry.basic.title": "基础图形", "wiki.plaitEditor.geometry.basic.rectangle": "矩形", "wiki.plaitEditor.geometry.basic.roundRectangle": "圆⻆矩形", "wiki.plaitEditor.geometry.basic.circle": "圆形", "wiki.plaitEditor.geometry.basic.triangle": "三⻆形", "wiki.plaitEditor.geometry.basic.diamond": "菱形", "wiki.plaitEditor.geometry.basic.parallelogram": "平行四边形", "wiki.plaitEditor.geometry.basic.trapezoid": "梯形", "wiki.plaitEditor.geometry.basic.cross": "十字", "wiki.plaitEditor.geometry.basic.pentagon": "五边形", "wiki.plaitEditor.geometry.basic.hexagon": "六边形", "wiki.plaitEditor.geometry.basic.octagon": "八边形", "wiki.plaitEditor.geometry.basic.star": "五⻆星", "wiki.plaitEditor.geometry.basic.annotation": "标注", "wiki.plaitEditor.geometry.basic.cloud": "云朵", "wiki.plaitEditor.geometry.arrow.left": "左箭头", "wiki.plaitEditor.geometry.arrow.right": "右箭头", "wiki.plaitEditor.geometry.arrow.bidirectional": "双向箭头", "wiki.plaitEditor.geometry.arrow.pentagon": "五边箭头", "wiki.plaitEditor.geometry.arrow.flow": "流程箭头", "wiki.plaitEditor.geometry.swimlane.vertical": "垂直泳道", "wiki.plaitEditor.geometry.swimlane.horizontal": "水平泳道", "wiki.plaitEditor.geometry.flowchart.process": "过程", "wiki.plaitEditor.geometry.flowchart.decision": "决策", "wiki.plaitEditor.geometry.flowchart.terminator": "起止符", "wiki.plaitEditor.geometry.flowchart.predefinedProcess": "预定义流程", "wiki.plaitEditor.geometry.flowchart.document": "多文档", "wiki.plaitEditor.geometry.flowchart.manualInput": "手动输入", "wiki.plaitEditor.geometry.flowchart.preparation": "准备", "wiki.plaitEditor.geometry.flowchart.data": "数据", "wiki.plaitEditor.geometry.flowchart.database": "数据库", "wiki.plaitEditor.geometry.flowchart.hardDisk": "硬盘", "wiki.plaitEditor.geometry.flowchart.internalStorage": "内部存储", "wiki.plaitEditor.geometry.flowchart.manualLoop": "手动循环", "wiki.plaitEditor.geometry.flowchart.delay": "延迟", "wiki.plaitEditor.geometry.flowchart.storedData": "存储数据", "wiki.plaitEditor.geometry.flowchart.connector": "连接器", "wiki.plaitEditor.geometry.flowchart.or": "汇总连接", "wiki.plaitEditor.geometry.flowchart.offPageConnector": "页外连接符", "wiki.plaitEditor.geometry.uml.note": "批注", "wiki.plaitEditor.geometry.uml.package": "包", "wiki.plaitEditor.geometry.uml.frame": "组合片段", "wiki.plaitEditor.geometry.uml.comment": "注释", "wiki.plaitEditor.geometry.uml.container": "容器", "wiki.plaitEditor.geometry.uml.class": "类", "wiki.plaitEditor.geometry.uml.activeClass": "活动类", "wiki.plaitEditor.geometry.uml.simpleClass": "简单类", "wiki.plaitEditor.geometry.uml.interface": "接口", "wiki.plaitEditor.geometry.uml.reflection": "反射", "wiki.plaitEditor.geometry.uml.requirement": "需求接口", "wiki.plaitEditor.geometry.uml.provided": "预留接口", "wiki.plaitEditor.geometry.uml.port": "端口", "wiki.plaitEditor.geometry.uml.fork": "分支合并", "wiki.plaitEditor.geometry.uml.component": "组件盒", "wiki.plaitEditor.geometry.text.bold": "加粗", "wiki.plaitEditor.geometry.text.leftAlign": "左对齐", "wiki.plaitEditor.geometry.text.centerAlign": "居中对齐", "wiki.plaitEditor.geometry.text.rightAlign": "右对齐", "wiki.plaitEditor.zIndex.bottom": "置底", "wiki.plaitEditor.zIndex.forward": "上移一层", "wiki.plaitEditor.zIndex.backward": "下移一层", "wiki.empty.space.title": "无法访问空间", "wiki.empty.space.subtitle": "空间不存在或者已过期", "wiki.empty.page.title": "无法访问页面", "wiki.empty.page.subtitle": "页面不存在或者已过期", "wiki.password.title": "访问密码", "wiki.password.placeholder": "请输入密码", "wiki.password.required": "请输入访问密码", "wiki.password.verifyFailed": "访问密码验证失败，请重新输入", "wiki.pageNav.previous": "上一篇：", "wiki.pageNav.next": "下一篇：", "wiki.pageNav.empty": "无", "wiki.directoryTree.title": "目录", "wiki.outside.signup": "注册", "wiki.outside.page.empty": "无", "wiki.question": "吗？", "wiki.space.brand": "空间", "wiki.space.spaceName": "空间名称", "wiki.space.relate.cancelConfirm": "确认取消关联空间{{name}}", "wiki.space.numberOfSpaces": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">个空间</span>", "wiki.resource.productRequirementCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个产品需求</span>", "wiki.resource.workOrderCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个工单</span>", "wiki.resource.workItemCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个工作项</span>", "wiki.resource.testCaseCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个测试用例</span>", "wiki.resource.objectiveCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个目标</span>", "wiki.resource.relatedItemsCount": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>个关联事项</span>", "wiki.plaitEditor.toolbar.image": "图片", "wiki.page.attachment.name": "附件", "wiki.common.property_setting.update_success": "更新成功", "wiki.common.maxView.back": "返回页面", "wiki.aiTable.dataPickerPlaceholder": "选择日期", "wiki.aiTable.linkTooltip": "链接", "wiki.aiTable.invalidLinkFormat": "链接格式不正确", "wiki.aiTable.linkRequired": "链接不能为空", "wiki.aiTable.linkText": "文本", "wiki.aiTable.linkUrl": "链接", "wiki.aiTable.inputText": "输入文本", "wiki.aiTable.inputUrl": "输入链接", "wiki.aiTable.fieldColumnName": "表格列名", "wiki.aiTable.fieldColumnNamePlaceholder": "输入列名称", "wiki.aiTable.fieldType": "列类型", "wiki.aiTable.allowMultipleMembers": "允许选择多个成员", "wiki.aiTable.cancel": "取消", "wiki.aiTable.apply": "应用", "wiki.aiTable.fieldNameRequired": "列名不能为空", "wiki.aiTable.fieldNameDuplicate": "列名已存在", "wiki.aiTable.confirm": "确定", "wiki.aiTable.copiedCells": "已复制 {count} 个单元格", "wiki.aiTable.invalidPasteContent": "粘贴内容不符合当前类型", "wiki.aiTable.fieldTypeText": "单行文本", "wiki.aiTable.fieldTypeSelect": "单选", "wiki.aiTable.fieldTypeMultiSelect": "多选", "wiki.aiTable.fieldTypeNumber": "数字", "wiki.aiTable.fieldTypeDate": "日期", "wiki.aiTable.fieldTypeMember": "成员", "wiki.aiTable.fieldTypeProgress": "进度", "wiki.aiTable.fieldTypeRate": "评分", "wiki.aiTable.fieldTypeLink": "链接", "wiki.aiTable.fieldTypeAttachment": "附件", "wiki.aiTable.fieldTypeCreatedBy": "创建人", "wiki.aiTable.fieldTypeCreatedAt": "创建时间", "wiki.aiTable.fieldTypeUpdatedBy": "更新人", "wiki.aiTable.fieldTypeUpdatedAt": "更新时间", "wiki.aiTable.copyField": "复制列", "wiki.aiTable.removeRecords": "删除行", "wiki.aiTable.copy": "复制", "wiki.aiTable.copySuffix": "副本", "wiki.aiTable.paste": "粘贴", "wiki.aiTable.tableView": "表格视图", "wiki.aiTable.editField": "编辑列", "wiki.aiTable.removeField": "删除列", "wiki.action.newTable": "新建表格", "wiki.aiTable.sort.title": "排序", "wiki.aiTable.sort.autoSort": "自动排序", "wiki.aiTable.sort.optionAsc": "选项正序", "wiki.aiTable.sort.optionDesc": "选项倒序", "wiki.aiTable.sort.numberDesc": "按 9 → 1 排序", "wiki.aiTable.sort.selectDesc": "按选项倒序排序", "wiki.aiTable.sort.defaultDesc": "按 Z → A 排序", "wiki.aiTable.sort.numberAsc": "按 1 → 9 排序", "wiki.aiTable.sort.selectAsc": "按选项正序排序", "wiki.aiTable.sort.defaultAsc": "按 A → Z 排序", "wiki.aiTable.sort.selectColumn": "选择一列进行排序", "wiki.aiTable.filter.byColumn": "按本列筛选", "wiki.aiTable.filter.removeCondition": "取消筛选", "wiki.space.settings.pageShared.spaceMember": "空间成员", "wiki.plugins.label.pluginName": "标签", "wiki.page.table": "表格", "wiki.aiTable.fieldTypeRichText": "多行文本", "wiki.page.shared.title": "页面共享", "wiki.aiTable.insertUpward": "向上插入", "wiki.aiTable.insertDownward": "向下插入", "wiki.aiTable.upward": "行", "wiki.aiTable.downward": "行", "wiki.aiTable.pasteOverMaxRecords": "粘贴内容超过最大记录数", "wiki.aiTable.pasteOverMaxFields": "粘贴内容超过最大字段数", "wiki.import.supportExcel": "支持导入以 .xlsx，.csv 结尾的文件", "wiki.aiTable.viewNameExist": "视图名称已存在", "wiki.aiTable.duplicateView": "复制视图", "wiki.aiTable.fieldGroupBase": "基础", "wiki.aiTable.fieldGroupAdvanced": "高级", "wiki.aiTable.rowAddFilterTooltip": "本记录已被筛选过滤，点击该记录以外位置将被隐藏", "wiki.plugin.insight": "效能度量", "wiki.plugin.relationReport": "报表", "wiki.relation.reportDeleted": "该报表已被删除或没有权限查看", "wiki.plugin.drawio": "Draw.io", "wiki.plugin.noGraphics": "无图形", "wiki.plugin.thirdPartyService": "第三方服务", "wiki.aiTable.none": "不展示", "wiki.aiTable.countAll": "记录总数", "wiki.aiTable.countAllResult": "{{statValue}} 条记录", "wiki.aiTable.filled": "已填写数", "wiki.aiTable.filledResult": "已填写数 {{statValue}}", "wiki.aiTable.empty": "未填写数", "wiki.aiTable.emptyResult": "未填写数 {{statValue}}", "wiki.aiTable.unique": "唯一数", "wiki.aiTable.uniqueResult": "唯一数 {{statValue}}", "wiki.aiTable.percentFilled": "已填写占比", "wiki.aiTable.percentFilledResult": "已填写占比 {{statValue}}%", "wiki.aiTable.percentEmpty": "未填写占比", "wiki.aiTable.percentEmptyResult": "未填写占比 {{statValue}}%", "wiki.aiTable.percentUnique": "唯一数占比", "wiki.aiTable.percentUniqueResult": "唯一数占比 {{statValue}}%", "wiki.aiTable.sum": "求和", "wiki.aiTable.sumResult": "求和 {{statValue}}", "wiki.aiTable.max": "最大值", "wiki.aiTable.maxResult": "最大值 {{statValue}}", "wiki.aiTable.min": "最小值", "wiki.aiTable.minResult": "最小值 {{statValue}}", "wiki.aiTable.average": "平均值", "wiki.aiTable.averageResult": "平均值 {{statValue}}", "wiki.aiTable.checked": "已勾选", "wiki.aiTable.checkedResult": "已勾选 {{statValue}}", "wiki.aiTable.unChecked": "未勾选", "wiki.aiTable.unCheckedResult": "未勾选 {{statValue}}", "wiki.aiTable.percentChecked": "已勾选占比", "wiki.aiTable.percentCheckedResult": "已勾选占比 {{statValue}}%", "wiki.aiTable.percentUnChecked": "未勾选占比", "wiki.aiTable.percentUnCheckedResult": "未勾选占比 {{statValue}}%", "wiki.aiTable.earliestTime": "最早时间", "wiki.aiTable.earliestTimeResult": "最早时间 {{statValue}}", "wiki.aiTable.latestTime": "最晚时间", "wiki.aiTable.latestTimeResult": "最晚时间 {{statValue}}", "wiki.aiTable.dateRangeOfDays": "时间范围(日)", "wiki.aiTable.dateRangeOfDaysResult": "时间范围 {{statValue}} 天", "wiki.aiTable.dateRangeOfMonths": "时间范围(月)", "wiki.aiTable.dateRangeOfMonthsResult": "时间范围 {{statValue}} 月", "wiki.aiTable.selectedRecordsCount": "已选中 {count} 条记录", "wiki.aiTable.selectedCellsCount": "已选中 {count} 个单元格", "wiki.aiTable.stat": "统计", "wiki.aiTable.fieldTypeCheckbox": "复选框"}