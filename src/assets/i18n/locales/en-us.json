{"wiki.title.default": "Wiki", "wiki.title.page": " | Wiki", "wiki.desk.newSpace": "Create space", "wiki.desk.spaces.all": "All spaces", "wiki.desk.spaces.organization": "Organization spaces", "wiki.desk.spaces.team": "Team spaces", "wiki.desk.spaces.personal": "Personal spaces", "wiki.desk.spaces.archived": "Archived spaces", "wiki.desk.spaces.sharing": "Sharing", "wiki.desk.spaces.share": "Share", "wiki.desk.share.title": "Shared to me", "wiki.desk.share.sharedTime": "Shared at", "wiki.desk.share.sharedBy": "Shared by", "wiki.desk.share.viewOnly": "View only", "wiki.desk.share.editable": "Editable", "wiki.desk.collections.collectionTime": "Favorite time", "wiki.desk.recent.title": "Recent view", "wiki.desk.recent.visitTime": "Access time", "wiki.desk.recent.viewTime": "View time", "wiki.desk.recent.editTime": "Edited at", "wiki.desk.recent.invalid": "Expired", "wiki.draft.title": "Draft", "wiki.draft.notExist": "Draft does not exist", "wiki.draft.delete": "Delete draft", "wiki.draft.publish": "Publish", "wiki.draft.abandon": "Discard draft", "wiki.draft.saveHint": "When creating a new page via a template, the content of the current page being edited will be saved as a draft", "wiki.draft.deleteConfirm": "Drafts cannot be recovered after deletion, and published pages remain unaffected.", "wiki.action.newBoard": "Whiteboard", "wiki.action.newDoc": "Document", "wiki.action.newGroup": "Folder", "wiki.action.newFromTemplate": "Use template", "wiki.page.type": "Page type", "wiki.page.parent": "Parent page", "wiki.page.space": "Belongs to space", "wiki.page.group": {"singular": "folder", "plural": "folders"}, "wiki.page.discussion": "Inline comments", "wiki.page.attachment.upload": "Upload", "wiki.page.attachment.dragHint": "Drop your files here to upload", "wiki.page.attachment.title": "Attachments", "wiki.page.attachment.count": {"singular": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">attachment</span>", "plural": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">attachments</span>"}, "wiki.page.copy.withChildren": "Copy together with subpages", "wiki.page.copy.select": "Select page", "wiki.page.copy.toSpace": "Copy to space", "wiki.page.copy.batchHint": "When copy in bulk, sub items will also be copied. Drag the page or click to select the parent item to copy it to a new location.", "wiki.page.copy.hint": "Copy it to a new location by drag the page or click to select parent.", "wiki.page.copy.title": "Copy page", "wiki.page.copy.validation.spaceRequired": "The selected space is required", "wiki.page.copy.validation.pageRequired": "Page is required", "wiki.page.edit.title": "Edit prompt", "wiki.page.edit.notifyFollowers": "Page has been significantly updated, notify page followers", "wiki.page.edit.publishOptions": "Publish options", "wiki.page.edit.noPermission": "You don't have permission to operate this function, please contact the enterprise owner or administrator", "wiki.page.edit.publish": "Publish", "wiki.page.edit.publishing": "Publishing...", "wiki.page.edit.lastSaved": "{{display_name}} last saved at {{updated_at}}", "wiki.page.sharing.title": "Share", "wiki.page.sharing.viewOnly": "View only", "wiki.page.sharing.editable": "Editable", "wiki.page.sharing.configuring": "Setting in progress...", "wiki.page.sharing.selectMembers": "Select members, departments or groups", "wiki.page.sharing.permissions": "Page permissions", "wiki.page.sharing.independentPermission": "Set independent permissions to control the visibility scope of space members.", "wiki.page.sharing.inheritPermission": "After configuring permissions (excluding default permissions), child pages will inherit permissions from their parent page while retaining the capability for independent permission configuration.", "wiki.page.sharing.defaultPermission": "Default permission", "wiki.page.sharing.defaultWithParent": "Inherit permissions from the parent page by default.", "wiki.page.sharing.defaultWithSpace": "Inherit permissions from the sppace by default", "wiki.page.sharing.allMemberViewEdit": "All space members can view and edit", "wiki.page.sharing.allMemberViewEditHint": "All space members can view and edit.", "wiki.page.sharing.allMemberViewPartEdit": "All space members can view, part of members can edit", "wiki.page.sharing.allMemberViewPartEditHint": "All space members can view, but only specified members can edit.", "wiki.page.sharing.specifiedMember": "Only specified space members can view or edit", "wiki.page.sharing.specifiedMemberHint": "After setting, the specified space members added can view or edit this page.", "wiki.page.sharing.shareWithChildren": "Sharing subpages, displayed as a page tree structure", "wiki.page.sharing.pageLink": "Private link", "wiki.page.sharing.nonSpaceMemberHint": "Add access permissions to allow non-space members to view or edit in the [Shared to me].", "wiki.page.sharing.switchConfirm": "After switching, the current settings will no longer take effect. Confirm switch?", "wiki.page.sharing.exitConfirm": "After exiting, the permission settings of the current page will no longer take effect. Confirm to exit?", "wiki.page.history.version": "Version", "wiki.page.history.showDiff": "Display differences", "wiki.page.history.showPublished": "Only show published versions", "wiki.page.history.restore": "Rest<PERSON>", "wiki.page.history.saveNamed": "Save as", "wiki.page.info.title": "Page information", "wiki.page.info.info": "Information", "wiki.page.info.compare": "Version comparision", "wiki.page.info.visitRecord": "View history", "wiki.page.info.charCount": "Character count", "wiki.page.info.charCountWithSpace": "Character count(Whitespace-inclusive)", "wiki.page.info.charCountNoSpace": "Character count(Non-whitespace)", "wiki.page.info.totalWords": "Total word count", "wiki.page.info.charCountWithSpaceHint": "(Whitespace-inclusive)", "wiki.page.info.charCountNoSpaceHint": "(Non-whitespace)", "wiki.page.move.title": "Move page", "wiki.page.move.toSpace": "Move to space", "wiki.page.move.alert": "Move page will also move its sub items simultaneously. You can drag page or click to select parent item to move it to new position.", "wiki.page.dialog.edit": "Edit(E)", "wiki.page.dialog.askAI": "<PERSON>", "wiki.page.dialog.minimize": "Minimize", "wiki.page.dialog.cancelMinimize": "Cancel minimize", "wiki.page.dialog.present": "Preview（{{key}}+Shift+P）", "wiki.page.dialog.copyLink": "Copy link", "wiki.page.dialog.openInSpace": "Open in space", "wiki.page.dialog.comment": "Comment", "wiki.page.dialog.hideComment": "Collapse", "wiki.page.version.saveNamed": "Save as a named version", "wiki.page.version.all": "All version", "wiki.page.version.named": "Named version", "wiki.page.version.compare": "with version", "wiki.page.version.restore.description": "This operation will create a new page version, with content identical to {{name}}.", "wiki.page.version.restore.confirm": "Confirm restoring version <span class=\"styx-confirm-target styx-confirm-target-primary text-truncate single-line\">{{name}}</span>?", "wiki.page.create.child": "New", "wiki.page.create.above": "Insert above", "wiki.page.create.below": "Insert below", "wiki.page.delete.description": "Deleting page will also delete its subpages, and it can be restored through the trash in space settings.", "wiki.page.delete.confirmMultiple": {"singular": "Are you sure you want to delete the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> page?", "plural": "Are you sure you want to delete the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> pages?"}, "wiki.page.delete.confirmSingle": "Confirm delete page<span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{name}}</span>?", "wiki.page.delete.byUser": "{{display_name}} deleted the page at {{time}}", "wiki.page.name.placeholder": "Enter title", "wiki.page.import.sizeLimit": "File size cannot exceed 1G", "wiki.page.import.success": {"singular": "<p class=\"styx-confirm-container\">{{count}} page have been imported. Click to view Page details.</p>", "plural": "<p class=\"styx-confirm-container\">{{count}} pages have been imported. Click to view Page details.</p>"}, "wiki.page.import.attachmentSizeLimit": "Attachment content size cannot exceed {{size}}", "wiki.page.export.file": "{{type}} file", "wiki.page.export.success": "<div class=\"styx-confirm-target-wrap\">The page has been exported as {{content}}, click to download <span class=\"styx-confirm-target styx-confirm-target-primary download-page-dialog text-truncate\">{{fileName}}</span></div>", "wiki.page.total.count": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>page</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>pages</span>"}, "wiki.page.operation.edit": "Edit(E)", "wiki.page.operation.ai": "<PERSON>", "wiki.page.operation.present": "Preview（{key}+Shift+P）", "wiki.page.operation.presentMode": "Preview", "wiki.page.operation.favorite": "Favorite", "wiki.page.operation.unfavorite": "Remove from favorites", "wiki.page.operation.lock": "Lock page", "wiki.page.operation.fullWidth": "Full width", "wiki.page.operation.print": "Print", "wiki.page.operation.export": "Export", "wiki.page.operation.details": "View details", "wiki.page.publish.success": "Published successfully", "wiki.page.publish.time": "Last updated", "wiki.page.reading": "Read · {{count}}", "wiki.page.noPermission": "No permission.", "wiki.page.template.namePlaceholder": "Enter template name", "wiki.page.untitled.document": "Untitled", "wiki.page.untitled.board": "Untitled", "wiki.presentation.exit": "Exit preview", "wiki.presentation.timer": "Turn off the lights and read silently", "wiki.presentation.pointer": "Click to show pointer", "wiki.discussion.comments": {"singular": "{{display_name}} et al. have made {{count}} comment", "plural": "{{display_name}} et al. have made {{count}} comments"}, "wiki.discussion.mention.placeholder": "@Mention members (Ctrl+Enter to send)", "wiki.discussion.header.commentCount": {"singular": "Total {{count}} comment", "plural": "Total {{count}} comments"}, "wiki.discussion.header.solveDiscussion": "Resolve inline comments", "wiki.discussion.header.reopenDiscussion": "Reopen", "wiki.discussion.footer.resolvedBy": "Resolved by {{name}} at {{time}}", "wiki.discussion.action.reply": "Reply", "wiki.discussion.action.delete": "Delete", "wiki.discussion.reply.prefix": "Reply to @{{name}}: >{{content}}", "wiki.discussion.version.name": "Version name", "wiki.collaboration.editing": "editing", "wiki.guider.page.operations": "Page-related operations are all here: page sharing, viewing history versions, locking the final version, exporting files, and more operations await your discovery.", "wiki.guider.page.editor": "Enter the editor to write documents. The editor provides rich page elements, supports Markdown syntax, allows multi-user collaborative editing, and saves collaborative content in real time.", "wiki.guider.page.draft": "Unpublished pages that have been edited in the space are displayed here, and pages are automatically saved as drafts during the editing process.", "wiki.guider.page.new": "Add a new page to the space. Provide a structured page tree. If you need to add a subpage to a certain page, click more-new under the page to perform the operation.", "wiki.guider.page.editPage": "Edit page", "wiki.guider.page.accessSpace": "Access space", "wiki.guider.page.createSpace": "Create space", "wiki.guider.space.settings": "For global management such as space, page template, role permission configuration, etc.", "wiki.guider.space.shared": "The content area on the right displays a list of pages with shared access enabled. If a member adds you to the shared access of a certain page, you can view it from here.", "wiki.guider.space.list": "The right content area displays a full list of spaces I am involved in. Click on a space to view its page tree and page details.", "wiki.guider.space.purpose": "To build a team knowledge system, it can be corporate rules and regulations, project documents, or interface documents, as you wish.", "wiki.knowledgeGraph.reference": "Quoted", "wiki.knowledgeGraph.referenced": "Referred by", "wiki.knowledgeGraph.title": "Knowledge graph", "wiki.knowledgeGraph.pageInfo": "Page information", "wiki.knowledgeGraph.relatedItems": "Link items", "wiki.knowledgeGraph.pageAttachments": "Page attachments", "wiki.space.identifier": "Key", "wiki.space.description": "Enter space description", "wiki.space.identifierHint": "The ​space key will be a ​prefix to the ​pages", "wiki.space.namePlaceholder": "Enter space name", "wiki.space.member": "Members", "wiki.space.template": "Space template", "wiki.space.sharing": "Space share", "wiki.space.component": "Components", "wiki.space.directory": "Directories", "wiki.space.statistics": "Analytics", "wiki.space.nameRequired": "Space name is required", "wiki.space.identifierRequired": "Space key is required", "wiki.space.identifierExists": "This key has been used by a space. Please use another key.", "wiki.space.removeMemberHint": "After removal, the member will no longer be able to edit the current space, and the data generated by the member will not be affected.", "wiki.space.archiveHint": "If you want to reuse the space after archiving, it can be activated by the administrator through the settings.", "wiki.space.deleteHint": "If you want to restore space after deletion, an administrator can restore it through the settings.", "wiki.space.baselineHint": "After the baseline is set up, the versions of pages under the current baseline will no longer change", "wiki.space.componentConfigHint": "After modifying the configuration of the current space components, the custom configurations of existing users will not be modified synchronously.", "wiki.space.delete": "Delete space", "wiki.space.activate": "Activate space", "wiki.space.archive": "Archive space", "wiki.space.move": "Move space", "wiki.space.info": "Space information", "wiki.space.copy": "Copy space", "wiki.space.exportTree": "Export pages", "wiki.space.stencils": "Templates", "wiki.space.exportPage.select": "Select export pages · {{selectPagesCount}}", "wiki.space.exportPage.maxTip": "Select at most {{count}} pages and bulk ecxport to PDF.", "wiki.space.identifierPattern": "English letters/numbers/underscores/hyphens (less than 15 characters)", "wiki.space.newSpaceDescription": "Space is a collection of pages, whiteboards and tables for knowledge. By organizing pages into a ​hierarchical structure, it systematizes and structures knowledge, enabling enterprises to ​accelerate product releases with higher quality.", "wiki.space.settings.move.description": "If you want to change the ownership and visibility scope of a space, you can move it. Moving the space will not affect the pages under the space.", "wiki.space.settings.archive.description": "If this space has been completed, you can archive it. After archiving, if you want to reuse the space, an administrator can activate it through the settings.", "wiki.space.settings.activate.description": "If you want to reuse this space, you can activate it.", "wiki.space.settings.delete.description": "If this space is no longer needed, you can delete it. If you want to restore space after deletion, you can restore it by the administrator through settings.", "wiki.space.settings.directory.level": "Hierarchy", "wiki.space.settings.directory.levelTooltip": "Page hierarchy", "wiki.space.settings.directory.showAll": "All", "wiki.space.settings.directory.showLevel": "Level {{level}}", "wiki.space.settings.directory.viewPage": "View page", "wiki.space.settings.pageShared.nonSpaceMember": "Non-space member", "wiki.space.settings.pageShared.publicShared": "Public", "wiki.space.settings.spaceShared.title": "Share space", "wiki.space.settings.share.success": "External shared content has been updated", "wiki.space.settings.share.logoSizeLimit": "Logo file cannot be larger than 1M", "wiki.space.settings.memberDetails.department": "Department", "wiki.space.settings.memberDetails.position": "Job", "wiki.space.settings.identifier.pattern": "English letters/numbers/underscores/hyphens (less than 15 characters)", "wiki.space.empty.title": "Unlinked space", "wiki.space.empty.desc": "Current {{type}} is not linked to any space. After linking to a space, you can view the page list of the space.", "wiki.space.relate.title": "Link spaces", "wiki.space.relate.select": "Select space", "wiki.space.relate.success": "{{title}} successfully", "wiki.space.selector.empty": "None", "wiki.template.center": "Template center", "wiki.template.organization": "Organazation template", "wiki.template.space": "Space template", "wiki.template.name": "Template name", "wiki.template.saveAs": "Save as template", "wiki.template.orgScope": "(available for all spaces in the organization)", "wiki.template.spaceScope": "(Current space available)", "wiki.template.create": "Use template", "wiki.template.exitConfirm.title": "Are you sure to exit editing template {{name}}?", "wiki.template.exitConfirm.description": "Exiting edit mode will discard all changes, and they cannot be recovered.", "wiki.template.count": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>template</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>templates</span>"}, "wiki.error.unknown": "Unknown error", "wiki.error.invalidImageType": "Unallowed image type", "wiki.error.imageTooLarge": "Image is too large", "wiki.error.operationFailed": "Operation failed", "wiki.error.noPermission": "You don't have permission to operate on this page.", "wiki.error.pageLocked": "This page has been locked and the operation is not allowed.", "wiki.error.pageBeingEdited": "Current page is being edited", "wiki.error.networkDisconnected": "Network has been interrupted", "wiki.error.waitForReconnect": "Please wait to continue editing after reconnection", "wiki.error.confirmExit": "Are you sure to exit editing?", "wiki.error.waitForUpload": "Exiting the edit mode will not save the uploaded content. Please wait for the upload to complete before exiting.", "wiki.error.messageServiceDisconnected": "Message service has been interrupted", "wiki.error.permissionRemoved": "Edit permission for this page has been removed, please contact the administrator", "wiki.error.pageDeleted": "This page has been deleted, please contact the administrator", "wiki.error.pageNotExist": "Page is unavailable or you have no permissions to access", "wiki.error.noAccessPermission": "You don't have access permission to this page", "wiki.error.deletePermissionDenied": "You don't have permission to delete the page.", "wiki.error.movePermissionDenied": "You don't have permission to move the page.", "wiki.error.copyPermissionDenied": "You don't have the permission to copy the page.", "wiki.error.editPermissionDenied": "You don't have edit permissions for this page.", "wiki.error.contentExceedLimit": "Content exceeds the limit, please try again", "wiki.error.unauthorized": "Unauthorized", "wiki.error.moveError": "Move failed", "wiki.error.moveNoPermission": "You don't have permission to edit the target page.", "wiki.error.exportError": "Export failed", "wiki.error.exportLinkExpired": "Download link expired, please re-export", "wiki.error.spaceNameRequired": "Space name is required", "wiki.error.spaceIdentifierRequired": "Space key is required", "wiki.error.spaceIdentifierExists": "This key has been used by a space. Please use another key.", "wiki.error.templateNameRequired": "Template name is required", "wiki.error.templateScopeRequired": "Use scope is required", "wiki.error.titleLengthExceed": "Title length must be less than 32 characters", "wiki.error.title.operationFailed": "Operation failed", "wiki.error.title.editFailed": "Edit failed", "wiki.error.title.deleteFailed": "Delete failed", "wiki.error.title.moveFailed": "Move failed", "wiki.error.title.copyFailed": "Co<PERSON> failed", "wiki.error.title.createFailed": "Creation failed", "wiki.error.title.publishFailed": "Publish failed", "wiki.error.title.pageBeingEdited": "Current page is being edited", "wiki.error.message.noPermission": "You don't have permission to operate on this page.", "wiki.error.message.pageLocked": "This page has been locked and the operation is not allowed.", "wiki.error.message.restoreConflict": "Recovery will cause the edited content to be lost. Please wait until other users exit the editing before performing the recovery.", "wiki.error.message.deleteNoPermission": "You don't have permission to delete the page.", "wiki.error.message.moveNoPermission": "You don't have permission to move the page.", "wiki.error.message.copyNoPermission": "You don't have permission to copy the page.", "wiki.error.message.editNoPermission": "You don't have permission to edit this page.", "wiki.error.message.collaborativeEditLimit": {"singular": "Detected {{expect_value}} member are currently co-editing on this page. To ensure a good editing experience, please edit later.", "plural": "Detected {{expect_value}} members are currently co-editing on this page. To ensure a good editing experience, please edit later."}, "wiki.error.message.editPageLimit": "Detected that you have opened {{expect_value}} edit pages. For a better editing experience, please close other pages.", "wiki.error.message.usageLimit": "Usage times have reached the upper limit", "wiki.error.message.unauthorized": "Unauthorized use", "wiki.error.message.contentExceedLimit": "Content exceeds the limit, please try again", "wiki.error.message.sensitiveContent": "Content contains sensitive words, please check and try again", "wiki.error.message.retryLater": "Operation failed, please try again", "wiki.import.title": "Import from", "wiki.import.cancel": "Cancel import", "wiki.import.failed": "Import failed", "wiki.import.complete": "Import completed", "wiki.import.fileNotExist": "File does not exist", "wiki.import.invalidFormat": "File format error", "wiki.import.supportMarkdown": "Support importing files ending with .markdown, .md, .mark, .txt", "wiki.import.supportZip": "Support importing files ending with .zip", "wiki.import.supportDocx": "Support import files ending with .docx", "wiki.import.supportConfluence": "Support for zip files exported in HTML format from Confluence spaces", "wiki.export.title": "Export page", "wiki.export.cancel": "Cancel export", "wiki.export.retry": "Re-export", "wiki.export.failed": "Export failed", "wiki.export.success": "Export successfully", "wiki.export.downloading": "Downloading...", "wiki.export.exporting": "Page exporting in progress...", "wiki.export.linkExpired": "Download link expired, please re-export", "wiki.export.emptyPages": "Export page is required", "wiki.baseline.comparison": "Version comparision", "wiki.baseline.pages.plan": "Planning page", "wiki.baseline.remove.confirm.multiple": {"singular": "Are you sure you want to move out the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> page?", "plural": "Are you sure you want to move out the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> pages?"}, "wiki.baseline.remove.confirm.single": "Confirm to move out page {{name}}?", "wiki.baseline.remove.confirm.description": "Move out and you can replan the page.", "wiki.statistics.title": "Analytics", "wiki.statistics.overview": "Overview", "wiki.statistics.newThisWeek": "Added this week", "wiki.statistics.memberDetails": "Member detail", "wiki.statistics.pageDetails": "Page detail", "wiki.statistics.member.name": "Display name", "wiki.statistics.member.createdPages": "Create page count", "wiki.statistics.member.editedPages": "Edited page count", "wiki.statistics.member.department": "Department", "wiki.statistics.member.position": "Job", "wiki.statistics.member.count": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>member</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>members</span>"}, "wiki.statistics.page.name": "Page name", "wiki.statistics.page.versions": "Version count", "wiki.statistics.page.words": "Word count", "wiki.statistics.page.reads": "Read count", "wiki.statistics.page.comments": "Comment count", "wiki.statistics.deleteTime": "Deleted at", "wiki.statistics.deleteBy": "Deleted by", "wiki.statistics.restoreSuccess": "Restored successfully", "wiki.resource.productRequirement": "Idea", "wiki.resource.workOrder": "Ticket", "wiki.resource.testCase": "Test case", "wiki.resource.objective": "Goal", "wiki.resource.relatedItems": "Link items", "wiki.sort.byViewTime": "By last viewed time", "wiki.sort.byPublishTime": "By published time", "wiki.sort.byCreateTime": "By created time", "wiki.sort.byTitle": "By title-A to Z", "wiki.spaceSharing.title": "Space share", "wiki.spaceSharing.enabled": "Opened", "wiki.spaceSharing.hint": "After enabling sharing, member who get the link can access this space.", "wiki.spaceSharing.displaySettings": "Display", "wiki.spaceSharing.contentSettings": "Content", "wiki.spaceSharing.brandLogo": "Brand logo", "wiki.spaceSharing.brandLogoHint": "(Only JPG and PNG format images are supported, optimal size 100x30)", "wiki.spaceSharing.uploadLogo": "Upload ", "wiki.spaceSharing.copyright": "Copyright", "wiki.spaceSharing.showCopyright": "Is it allowed to show copyright information?", "wiki.spaceSharing.customPages": "Custom pages", "wiki.spaceSharing.allPages": "All pages", "wiki.spaceSharing.selectSharePages": "Select pages to share", "wiki.spaceSharing.sharePagesRequired": "Share page is required", "wiki.spaceSharing.exitConfirm": "After exiting, the settings for externally shared content in the current space will no longer take effect. Confirm to exit?", "wiki.common.confirm.delete": "Confirm delete", "wiki.common.confirm.deleteComment": "Confirm to delete the comment?", "wiki.common.confirm.exit": "Are you sure you want to exit editing?", "wiki.common.confirm.restore": "Confirm restore", "wiki.common.confirm.publish": "Confirm release", "wiki.common.confirm.moveOut": "Confirm move out", "wiki.common.status.reading": "Read · {{reading}}", "wiki.common.status.publishedAt": "last updated  {{updated_at}}", "wiki.common.action.openInSpace": "Open in space", "wiki.common.action.scanQRCode": "<PERSON><PERSON>", "wiki.common.action.viewInMiniApp": "View page in the mini program", "wiki.common.action.searchPage": "Enter page title to search", "wiki.common.action.browseRecent": "Recent viewed", "wiki.common.action.newPage": "New page", "wiki.common.action.saveAsTemplate": "Save as template", "wiki.common.action.saveNamedVersion": "Save named version", "wiki.common.action.publishWithNotify": "Publish and notify", "wiki.common.action.publishPage": "Publish this page", "wiki.common.count.subPages": {"singular": "{{count}} page etc.", "plural": "{{count}} pages etc."}, "wiki.common.count.includeSubPages": {"singular": "Include {{count}} child", "plural": "Include {{count}} childen"}, "wiki.configuration.title": "Space", "wiki.configuration.manage.title": "Spaces", "wiki.configuration.manage.shareSettings": "Share settings", "wiki.configuration.manage.deleteHint": "If you want to restore space after deletion, an administrator can restore it through the settings.", "wiki.configuration.manage.column.sharedBy": "Shared by", "wiki.configuration.manage.column.sharedAt": "Shared at", "wiki.configuration.space.componentPermission": "Visibility", "wiki.configuration.space.configHint": "After modifying the global configuration of the component, the configurations of existing sapces will not be synchronized.", "wiki.configuration.review.description": "Customize the revview processes and rules within the space.", "wiki.configuration.review.configHint": "Updating the review configuration will not affect existing data. After enabling the localization configuration, the current review configuration will no longer be affected by the global configuration.", "wiki.configuration.stencil.delete.description": "Deleted items cannot be restored.", "wiki.configuration.tags.deleteDescription": {"singular": "Labels cannot be restored after deletion. A total of {{count}} page are using this label. It will be removed from the corresponding pages after deletion.", "plural": "Labels cannot be restored after deletion. A total of {{count}} pages are using this label. It will be removed from the corresponding pages after deletion."}, "wiki.plugin.alert": "Prompt", "wiki.plugin.alertDesc": "Highlight key content using colored hint boxes", "wiki.plugin.attachment": "Existing file", "wiki.plugin.attachmentDesc": "Support inserting content from the attachment list", "wiki.plugin.attachmentCloud": "Local file", "wiki.plugin.attachmentCloudDesc": "Support for Office, PDF and other files", "wiki.plugin.audio": "Local audio", "wiki.plugin.audioDesc": "Support online audio playback", "wiki.plugin.date": "Date", "wiki.plugin.mindmap": "Mindmap", "wiki.plugin.board": "Whiteboard", "wiki.plugin.flowchart": "Flowchat", "wiki.plugin.formula": "LaTex", "wiki.plugin.formulaDesc": "Support inserting LaTex syntax formulas", "wiki.plugin.tagDesc": "Custom status tag", "wiki.plugin.layout": "Layouts", "wiki.plugin.layoutDesc": "Use layout columns to display page content", "wiki.plugin.outline": "Outline", "wiki.plugin.pageOutline": "Page outline", "wiki.plugin.pageOutlineDesc": "Insert outline of this page", "wiki.plugin.saveDraft": "Save draft", "wiki.plugin.fontSize": "Font", "wiki.plugin.insert": "Add", "wiki.plugin.productManage": "Ship", "wiki.plugin.projectManage": "Project", "wiki.plugin.testManage": "<PERSON><PERSON><PERSON>", "wiki.plugin.wikiManage": "Wiki", "wiki.plugin.collaborativeSpace": "Teams", "wiki.plugin.relationIdea": "Idea", "wiki.plugin.relationIdeaList": "Idea list", "wiki.plugin.relationObjectiveList": "Goal list", "wiki.plugin.relationPageTree": "Page tree", "wiki.plugin.relationTestCaseList": "Test case list", "wiki.plugin.relationTicketList": "Ticket list", "wiki.plugin.relationWorkItemList": "Workitem list", "wiki.plugin.searchReplace": "Find and replace", "wiki.plugin.textDiagram": "Text diagram", "wiki.plugin.toggleList": "Toggle list", "wiki.plugin.video": "Video", "wiki.plugin.localVideo": "Local video", "wiki.plugin.graphics": "<PERSON><PERSON><PERSON>", "wiki.plugin.placeholder": "Type '/' for commands", "wiki.plugin.newNode": "New node", "wiki.plugin.end": "End", "wiki.plugin.yes": "Yes", "wiki.plugin.no": "No", "wiki.plugin.process": "Process", "wiki.plugin.decision": "Judge", "wiki.attachments.tabs.picture": "Image", "wiki.attachments.tabs.audio": "Audio", "wiki.attachments.tabs.video": "Video", "wiki.validator.space.required": "The selected space is required", "wiki.validator.page.required": "Page is required", "wiki.pagination.range": "The", "wiki.permission.type.readonly": "View only", "wiki.permission.type.editable": "Editable", "wiki.permission.page.default.name": "Default permission", "wiki.permission.page.default.desc": "Permissions for the current page are default consistent with the space permissions.", "wiki.permission.page.allReadAndEdit.name": "All space members can view and edit", "wiki.permission.page.allReadAndEdit.desc": "All space members can view and edit.", "wiki.permission.page.allReadSpecificEdit.name": "All space members can view, part of members can edit", "wiki.permission.page.allReadSpecificEdit.desc": "All space members can view, but only specified members can edit.", "wiki.permission.page.specificReadAndEdit.name": "Only specified space members can view or edit", "wiki.permission.page.specificReadAndEdit.desc": "After setting, the specified space members added can view or edit this page.", "wiki.property.review.status": "Review status", "wiki.property.review.result": "Review result", "wiki.property.importance": "Important level", "wiki.pricing.expired.description": "Provide a structured space for recording information and knowledge, making it easy for teams to accumulate experience and share resources.", "wiki.pricing.features.pageExport.title": "Page export", "wiki.pricing.features.pageExport.subTitle": "Export page content as a file, supporting PDF, Word, and Markdown formats", "wiki.pricing.features.pageExport.offlineReading": "Offline reading", "wiki.pricing.features.pageExport.contentStyle": "No difference content style", "wiki.pricing.features.pageTreeExport.title": "Export pages", "wiki.pricing.features.pageTreeExport.subTitle": "Bulk export page content as PDF files. Select at most 100 pages.", "wiki.pricing.features.pageTemplate.title": "Templates", "wiki.pricing.features.pageTemplate.subTitle": "Standardize the knowledge accumulation of the team", "wiki.pricing.features.pageTemplate.orgTemplate": "Organazation template management", "wiki.pricing.features.pageTemplate.spaceTemplate": "Space templates management", "wiki.pricing.features.pageShare.title": "Page share", "wiki.pricing.features.pageShare.subTitle": "Flexible settings for page permissions of different roles, promoting the sharing of knowledge resources inside and outside the enterprise", "wiki.pricing.features.pageShare.memberPermission": "Support setting independent page permissions for space members", "wiki.pricing.features.pageShare.externalPermission": "Support setting shared permissions for external members of the space", "wiki.pricing.features.pageShare.publicShare": "Support generating shared links to publicly disclose pages", "wiki.pricing.features.embedment.title": "Insert local audio/video", "wiki.pricing.features.embedment.subTitle": "Support mp4, mkv, webm, mov, mp3, wma, wav, ape, flac, ogg, m4r, m4a formats", "wiki.pricing.features.embedment.onlinePlay": "Video/audio files play online", "wiki.pricing.features.embedment.richContent": "Enrich the page content to display diversified information", "wiki.pricing.features.spaceShare.title": "Space share", "wiki.pricing.features.spaceShare.subTitle": "Generate shared links to make knowledge resources spread more widely", "wiki.pricing.features.spaceShare.expiry": "Set expiration", "wiki.pricing.features.spaceShare.password": "Set access password", "wiki.pricing.features.spaceShare.qrCode": "<PERSON><PERSON> to read", "wiki.review.title": "Title", "wiki.review.versionCompare": "Version comparision", "wiki.review.removeConfirm.title": "Confirm move out", "wiki.review.removeConfirm.action": "Move out", "wiki.review.removeConfirm.multiple": {"singular": "Are you sure you want to move out the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> page?", "plural": "Are you sure you want to move out the selected <span class=\"styx-confirm-target styx-confirm-target-danger text-truncate\">{{count}}</span> pages?"}, "wiki.review.removeConfirm.single": "<span class=\"styx-confirm-nowrap\">Confirm move out of page</span> {{name}}?", "wiki.review.removeConfirm.description": "Move out and you can re - add %s.", "wiki.tag.delete.description": {"singular": "Labels cannot be restored after deletion. A total of {{count}} page are using this label. It will be removed from the corresponding pages after deletion.", "plural": "Labels cannot be restored after deletion. A total of {{count}} pages are using this label. It will be removed from the corresponding pages after deletion."}, "wiki.relation.disconnect.description": "After unlinking, you can relink.", "wiki.relation.disconnect.confirm": "Confirm to unlink with page %s?", "wiki.relation.pageTitle": "Search page title", "wiki.relation.pageDeleted": "The page has been deleted or you don't have permission to view it", "wiki.relation.moreWorkItems": "More workitems", "wiki.relation.workItemTitle": "Search workitem title or ID", "wiki.relation.workItemDeleted": "The workitem has been deleted or you don't have permission to view it", "wiki.relation.moreCases": "More test cases", "wiki.relation.caseTitle": "Search test case title or ID", "wiki.relation.caseDeleted": "The test case has been deleted or you don't have permission to view it", "wiki.relation.moreRequirements": "More ideas", "wiki.relation.requirementTitle": "Search idea title or number", "wiki.relation.requirementDeleted": "This idea has been deleted or you don't have permission to view it", "wiki.relation.moreTickets": "More tickets", "wiki.relation.ticketTitle": "Search ticket title or ID", "wiki.relation.ticketDeleted": "The ticket has been deleted or you don't have permission to view it", "wiki.relation.moreObjectives": "More goals", "wiki.relation.objectiveTitle": "Search goal title", "wiki.relation.objectiveDeleted": "The goal has been deleted or you don't have permission to view it", "wiki.media.title": "Insert local audio/video", "wiki.media.onlinePlay": "Play video/audio files online", "wiki.member.all": "All members", "wiki.document.wordCount": {"singular": "Total {{count}} word", "plural": "Total {{count}} words"}, "wiki.document.emoji.change": "Change emoji", "wiki.document.emoji.standardMode": "Standard mode", "wiki.document.emoji.remove": "Remove emoji", "wiki.document.emoji.add": "Add emoji", "wiki.document.placeholder": "Enter content", "wiki.document.imageError.sizeLimit": "Upload of images larger than 50M is not supported.", "wiki.document.imageError.formatError": "Image format not supported for upload.", "wiki.shortcuts.title": "Shortcut", "wiki.shortcuts.textFormat": "Text format", "wiki.shortcuts.markdownSyntax": "Markdown syntax", "wiki.shortcuts.canvas": "<PERSON><PERSON>", "wiki.shortcuts.fontFormat": "Text format", "wiki.shortcuts.markdown.heading1": "Heading 1", "wiki.shortcuts.markdown.heading2": "Heading 2", "wiki.shortcuts.markdown.heading3": "Heading 3", "wiki.shortcuts.markdown.heading4": "Heading 4", "wiki.shortcuts.markdown.heading5": "Heading 5", "wiki.shortcuts.markdown.heading6": "Heading 6", "wiki.shortcuts.markdown.bold": "**Bold**", "wiki.shortcuts.markdown.italic": "*Italic*", "wiki.shortcuts.markdown.strikethrough": "~~Strikethrough~~", "wiki.shortcuts.actions.zoom": "Zoom", "wiki.shortcuts.actions.scroll": "<PERSON><PERSON>", "wiki.shortcuts.actions.actualSize": "Actual size", "wiki.shortcuts.actions.insertSubset": "Insert subset", "wiki.shortcuts.actions.insertSibling": "Insert sibling", "wiki.shortcuts.actions.undo": "Undo", "wiki.shortcuts.actions.redo": "Redo", "wiki.shortcuts.actions.quickInsert": "Quick insert", "wiki.shortcuts.actions.saveDraft": "Save draft", "wiki.shortcuts.actions.increaseIndent": "Indent", "wiki.shortcuts.actions.decreaseIndent": "Outdent", "wiki.shortcuts.actions.emoji": "<PERSON><PERSON><PERSON>", "wiki.shortcuts.actions.mention": "Mention", "wiki.shortcuts.actions.bold": "Bold", "wiki.shortcuts.actions.italic": "Italic", "wiki.shortcuts.actions.underline": "Underline", "wiki.shortcuts.actions.inlineCode": "Inline code", "wiki.shortcuts.actions.selectCell": "Select cells", "wiki.shortcuts.actions.selectCellRange": "Select cell range", "wiki.shortcuts.actions.multiSelectCell": "Multi-select cells", "wiki.shortcuts.actions.strikethrough": "Strikethrough", "wiki.shortcuts.actions.orderedList": "Numbered list", "wiki.shortcuts.actions.unorderedList": "Bullet list", "wiki.shortcuts.actions.todoList": "To-do", "wiki.shortcuts.actions.codeBlock": "Code block", "wiki.shortcuts.actions.divider": "Divider", "wiki.shortcuts.actions.editBoard": "Edit whiteboard", "wiki.shortcuts.actions.softEnter": "Line break (soft return)", "wiki.shortcuts.actions.expandCollapseNode": "Expand/collapse node", "wiki.material.use": "Use", "wiki.material.useCase": "Usage scenario", "wiki.material.library": "Material library", "wiki.material.all": "All materials", "wiki.preview.createItem": "New", "wiki.preview.comment": "Write a comment...", "wiki.search.noResult": "No data available", "wiki.search.replace.next": "Next", "wiki.search.replace.previous": "Previous", "wiki.search.replace.replace": "Replace", "wiki.search.replace.replaceAll": "Replace all", "wiki.search.replace.input": "Enter", "wiki.search.replace.replaceTo": "Replace with", "wiki.search.replace.find": "Find", "wiki.plugins.alert.backgroundColor": "Highlight color", "wiki.plugins.alert.deleteEmoji": "Delete emoji", "wiki.plugins.alert.addEmoji": "Add emoji", "wiki.plugins.alert.danger": "Danger", "wiki.plugins.attachment.linkFailed": "File link failed. Link through the attachment list or re - upload.", "wiki.plugins.attachment.deleted": "File has been deleted", "wiki.plugins.attachment.select": "Select existing file", "wiki.plugins.attachment.upload": "Upload from local", "wiki.plugins.diagramBoard.exportImage": "Export image", "wiki.plugins.formula.input": "Enter LaTex", "wiki.plugins.formula.empty": "Formula is required", "wiki.plugins.formula.invalid": "Illegal laTeX formula", "wiki.plugins.label.inputName": "Enter tag name", "wiki.plugins.label.set": "Set tag", "wiki.plugins.layout.twoColumns": "Two columns", "wiki.plugins.layout.threeColumns": "Three columns", "wiki.plugins.layout.leftSidebar": "Left sidebar", "wiki.plugins.layout.rightSidebar": "Right sidebar", "wiki.plugins.layout.bothSidebars": "Left/right sidebar", "wiki.plugins.outline.list": "List", "wiki.plugins.outline.showNodes": "Show the number of sections", "wiki.plugins.outline.level1": "Level 1", "wiki.plugins.outline.level2": "Level 2", "wiki.plugins.outline.level3": "Level 3", "wiki.plugins.outline.level4": "Level 4", "wiki.plugins.outline.levelTitle": "Outline level", "wiki.plugins.outline.options": "Outline options", "wiki.plugins.toggleList.clickToInput": "Click to enter content", "wiki.plugins.toggleList.inputTitle": "Enter collapse title", "wiki.plugins.toggleList.placeholder": "Enter...", "wiki.plugins.textDiagram.templates.sequence.name": "Sequence diagram", "wiki.plugins.textDiagram.templates.sequence.actors.user": "User", "wiki.plugins.textDiagram.templates.sequence.actors.browser": "Browser", "wiki.plugins.textDiagram.templates.sequence.actors.server": "Server", "wiki.plugins.textDiagram.templates.sequence.actions.inputUrl": "Enter url", "wiki.plugins.textDiagram.templates.sequence.actions.requestServer": "Request server", "wiki.plugins.textDiagram.templates.sequence.actions.renderTemplate": "Template render", "wiki.plugins.textDiagram.templates.sequence.actions.returnHtml": "Return HTML", "wiki.plugins.textDiagram.templates.sequence.actions.comment": "This is a comment", "wiki.plugins.textDiagram.templates.useCase.name": "Use case diagram", "wiki.plugins.textDiagram.templates.class.name": "Class diagram", "wiki.plugins.textDiagram.templates.flow.name": "Flowchart (Flow)", "wiki.plugins.textDiagram.templates.activity.name": "Activity diagram", "wiki.plugins.textDiagram.templates.component.name": "Component diagram", "wiki.plugins.textDiagram.templates.state.name": "Status diagram", "wiki.plugins.textDiagram.templates.object.name": "Object diagram", "wiki.plaitEditor.toolbar.arrow.endpoint": "End point", "wiki.plaitEditor.toolbar.arrow.convert": "Convert", "wiki.plaitEditor.toolbar.arrow.startPoint": "Start point", "wiki.plaitEditor.toolbar.arrow.border": "Border", "wiki.plaitEditor.toolbar.arrow.type": "Type", "wiki.plaitEditor.toolbar.common.fill": "Node fill", "wiki.plaitEditor.toolbar.common.shape": "Switch shape", "wiki.plaitEditor.toolbar.common.link.tooltip": "Link", "wiki.plaitEditor.toolbar.common.link.urlPlaceholder": "Enter the link", "wiki.plaitEditor.toolbar.common.link.textPlaceholder": " Enter text", "wiki.plaitEditor.toolbar.common.link.invalidUrl": "Enter a valid link", "wiki.plaitEditor.toolbar.common.node.fill": "Node fill", "wiki.plaitEditor.toolbar.common.node.border": "Node border", "wiki.plaitEditor.toolbar.common.text.alignment": "Alignment", "wiki.plaitEditor.toolbar.common.text.color": "Font color", "wiki.plaitEditor.toolbar.draw.border": "Border", "wiki.plaitEditor.toolbar.draw.fill": "Fill", "wiki.plaitEditor.toolbar.mind.summary": "Summary", "wiki.plaitEditor.toolbar.mind.nodeShape": "Node shape", "wiki.plaitEditor.toolbar.mind.branch": "Branch", "wiki.plaitEditor.toolbar.mind.structure": "Structure", "wiki.plaitEditor.toolbar.multiple.border": "Border", "wiki.plaitEditor.toolbar.multiple.fill": "Fill", "wiki.plaitEditor.toolbar.swimlane.border": "Border", "wiki.plaitEditor.toolbar.swimlane.fill": "Fill", "wiki.plaitEditor.toolbar.swimlane.lanes": "Swimlanes count", "wiki.plaitEditor.shape.recentlyUsed": "Recent used", "wiki.plaitEditor.mainToolbar.connection": "Line", "wiki.plaitEditor.mainToolbar.mindmap": "Mindmap M", "wiki.plaitEditor.rightTopToolbar.themeColor": "Theme", "wiki.plaitEditor.rightTopToolbar.changeTheme": "Change theme", "wiki.plaitEditor.settingPanel.borderOther.width": "Boldness", "wiki.plaitEditor.settingPanel.borderOther.color": "Border color", "wiki.plaitEditor.settingPanel.nodeOther.shape": "Node shape", "wiki.plaitEditor.settingPanel.text.alignment": "Alignment", "wiki.plaitEditor.settingPanel.text.color": "Text color", "wiki.plaitEditor.settingPanel.draw.fillColor": "Fill color", "wiki.plaitEditor.settingPanel.draw.shape": "<PERSON><PERSON><PERSON>", "wiki.plaitEditor.settingPanel.mind.branchType": "Branch type", "wiki.plaitEditor.settingPanel.mind.layout": "Structure", "wiki.plaitEditor.settingPanel.distribution": "Distribution", "wiki.plaitEditor.settingPanel.border": "Border", "wiki.plaitEditor.settingPanel.swimlane": "<PERSON><PERSON><PERSON><PERSON>", "wiki.plaitEditor.settingPanel.endpoint": "Endpoint", "wiki.plaitEditor.settingPanel.connection": "Line", "wiki.plaitEditor.settingPanel.node": "Node", "wiki.plaitEditor.settingPanel.branch": "Branch", "wiki.plaitEditor.settingPanel.displayMode": "Display mode", "wiki.plaitEditor.settingPanel.previewConfig": "Preview page configuration", "wiki.plaitEditor.settingPanel.borderWidth": "Border thickness", "wiki.plaitEditor.settingPanel.displayModeEnum.auto": "Auto fit", "wiki.plaitEditor.settingPanel.displayModeEnum.sameAsEdit": "Match editing", "wiki.plaitEditor.arrangement.leftAlign": "<PERSON><PERSON> left ", "wiki.plaitEditor.arrangement.centerAlign": "Align center", "wiki.plaitEditor.arrangement.verticalDistribute": "Distribute vertically", "wiki.plaitEditor.contextMenu.addText": "Add text", "wiki.plaitEditor.contextMenu.rightAlign": "Align right", "wiki.plaitEditor.contextMenu.horizontalDistribute": "Distribute horizontally", "wiki.plaitEditor.contextMenu.topAlign": "Align top", "wiki.plaitEditor.contextMenu.middleAlign": "Vertical center", "wiki.plaitEditor.contextMenu.bottomAlign": "Align bottom", "wiki.plaitEditor.contextMenu.addShape": "Add shape", "wiki.plaitEditor.contextMenu.addLine": "Line", "wiki.plaitEditor.contextMenu.addMindmap": "Add mindmap", "wiki.plaitEditor.contextMenu.paste": "Paste", "wiki.plaitEditor.contextMenu.cut": "Cut", "wiki.plaitEditor.contextMenu.copy": "Copy", "wiki.plaitEditor.contextMenu.arrange": "<PERSON><PERSON><PERSON>", "wiki.plaitEditor.contextMenu.addSummary": "Add summary", "wiki.plaitEditor.contextMenu.child": "Child", "wiki.plaitEditor.contextMenu.sibling": "<PERSON><PERSON>", "wiki.plaitEditor.group.group": "Group", "wiki.plaitEditor.group.ungroup": "Ungroup", "wiki.plaitEditor.geometry.basic.title": "Basic shape", "wiki.plaitEditor.geometry.basic.rectangle": "Rectangle", "wiki.plaitEditor.geometry.basic.roundRectangle": "Rounded rectangle", "wiki.plaitEditor.geometry.basic.circle": "Circle", "wiki.plaitEditor.geometry.basic.triangle": "Triangle", "wiki.plaitEditor.geometry.basic.diamond": "Diamond", "wiki.plaitEditor.geometry.basic.parallelogram": "Parallelogram", "wiki.plaitEditor.geometry.basic.trapezoid": "Trapezoid", "wiki.plaitEditor.geometry.basic.cross": "Cross", "wiki.plaitEditor.geometry.basic.pentagon": "Pentagon", "wiki.plaitEditor.geometry.basic.hexagon": "Hexagon", "wiki.plaitEditor.geometry.basic.octagon": "Octagon", "wiki.plaitEditor.geometry.basic.star": "Five-pointed star", "wiki.plaitEditor.geometry.basic.annotation": "Callout", "wiki.plaitEditor.geometry.basic.cloud": "Cloud", "wiki.plaitEditor.geometry.arrow.left": "Left arrow", "wiki.plaitEditor.geometry.arrow.right": "Right arrow", "wiki.plaitEditor.geometry.arrow.bidirectional": "Double arrow", "wiki.plaitEditor.geometry.arrow.pentagon": "Chevron arrow", "wiki.plaitEditor.geometry.arrow.flow": "Flow arrow", "wiki.plaitEditor.geometry.swimlane.vertical": "Vertical swimlane", "wiki.plaitEditor.geometry.swimlane.horizontal": "Horizontal swimlane", "wiki.plaitEditor.geometry.flowchart.process": "Process", "wiki.plaitEditor.geometry.flowchart.decision": "Decision", "wiki.plaitEditor.geometry.flowchart.terminator": "Start/End event", "wiki.plaitEditor.geometry.flowchart.predefinedProcess": "Predefined process", "wiki.plaitEditor.geometry.flowchart.document": "Multiple documents", "wiki.plaitEditor.geometry.flowchart.manualInput": "Manual input", "wiki.plaitEditor.geometry.flowchart.preparation": "Prepare", "wiki.plaitEditor.geometry.flowchart.data": "Data", "wiki.plaitEditor.geometry.flowchart.database": "Database", "wiki.plaitEditor.geometry.flowchart.hardDisk": "Hard disk", "wiki.plaitEditor.geometry.flowchart.internalStorage": "Internal storage", "wiki.plaitEditor.geometry.flowchart.manualLoop": "Manual loop", "wiki.plaitEditor.geometry.flowchart.delay": "Delay", "wiki.plaitEditor.geometry.flowchart.storedData": "Data store", "wiki.plaitEditor.geometry.flowchart.connector": "Connectors", "wiki.plaitEditor.geometry.flowchart.or": "Aggregation Connector", "wiki.plaitEditor.geometry.flowchart.offPageConnector": "Off-page Connector", "wiki.plaitEditor.geometry.uml.note": "Annotation", "wiki.plaitEditor.geometry.uml.package": "Package", "wiki.plaitEditor.geometry.uml.frame": "Combined fragment", "wiki.plaitEditor.geometry.uml.comment": "Comment", "wiki.plaitEditor.geometry.uml.container": "Container", "wiki.plaitEditor.geometry.uml.class": "Class", "wiki.plaitEditor.geometry.uml.activeClass": "Active class", "wiki.plaitEditor.geometry.uml.simpleClass": "Simple class", "wiki.plaitEditor.geometry.uml.interface": "Interface", "wiki.plaitEditor.geometry.uml.reflection": "Reflection", "wiki.plaitEditor.geometry.uml.requirement": "Required interface", "wiki.plaitEditor.geometry.uml.provided": "Provided interface", "wiki.plaitEditor.geometry.uml.port": "Port", "wiki.plaitEditor.geometry.uml.fork": "Fork", "wiki.plaitEditor.geometry.uml.component": "Component box", "wiki.plaitEditor.geometry.text.bold": "Bold", "wiki.plaitEditor.geometry.text.leftAlign": "<PERSON><PERSON> left ", "wiki.plaitEditor.geometry.text.centerAlign": "Align center", "wiki.plaitEditor.geometry.text.rightAlign": "Align right", "wiki.plaitEditor.zIndex.bottom": "Send to back", "wiki.plaitEditor.zIndex.forward": "To front", "wiki.plaitEditor.zIndex.backward": "To back", "wiki.empty.space.title": "Unable to access space", "wiki.empty.space.subtitle": "Space does not exist or has expired", "wiki.empty.page.title": "Unable to access page", "wiki.empty.page.subtitle": "Page does not exist or has expired", "wiki.password.title": "Access password", "wiki.password.placeholder": "Enter password", "wiki.password.required": "Enter the access password", "wiki.password.verifyFailed": "Access password verification failed, please re-enter", "wiki.pageNav.previous": "Previous:", "wiki.pageNav.next": "Next:", "wiki.pageNav.empty": "None", "wiki.directoryTree.title": "Directory", "wiki.outside.signup": "Sign up", "wiki.outside.page.empty": "None", "wiki.question": "?", "wiki.space.brand": "Space", "wiki.space.spaceName": "Space name", "wiki.space.relate.cancelConfirm": "Confirm to cancel the linked space {{name}}", "wiki.space.numberOfSpaces": {"singular": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">space</span>", "plural": "<span class=\"text-secondary\">{{ count }}</span><span class=\"text-muted ml-1\">spaces</span>"}, "wiki.resource.productRequirementCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>idea</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>ideas</span>"}, "wiki.resource.workOrderCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>ticket</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>tickets</span>"}, "wiki.resource.workItemCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>workitem</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>workitems</span>"}, "wiki.resource.testCaseCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>test case</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>test cases</span>"}, "wiki.resource.objectiveCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>goal</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>goals</span>"}, "wiki.resource.relatedItemsCount": {"singular": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>linked item</span>", "plural": "<span class='text-secondary'>{{ count }}</span><span class='text-muted ml-1'>linked items</span>"}, "wiki.plaitEditor.toolbar.image": "Image", "wiki.page.attachment.name": "Attachments", "wiki.common.property_setting.update_success": "Updated successfully", "wiki.common.maxView.back": "Back to page", "wiki.aiTable.dataPickerPlaceholder": "Select date", "wiki.aiTable.linkTooltip": "Link", "wiki.aiTable.invalidLinkFormat": "Link format is incorrect", "wiki.aiTable.linkRequired": "Link is required", "wiki.aiTable.linkText": "Text", "wiki.aiTable.linkUrl": "Link", "wiki.aiTable.inputText": "Enter text", "wiki.aiTable.inputUrl": "Enter link", "wiki.aiTable.fieldColumnName": "Field name", "wiki.aiTable.fieldColumnNamePlaceholder": "Enter field name", "wiki.aiTable.fieldType": "Field type", "wiki.aiTable.allowMultipleMembers": "Allow selecting multiple members", "wiki.aiTable.cancel": "Cancel", "wiki.aiTable.apply": "Apply", "wiki.aiTable.fieldNameRequired": "Field name is required", "wiki.aiTable.fieldNameDuplicate": "Field name already exists", "wiki.aiTable.confirm": "OK", "wiki.aiTable.copiedCells": {"singular": "Copied {count} cell", "plural": "Copied {count} cells"}, "wiki.aiTable.invalidPasteContent": "The pasted content does not match the current type", "wiki.aiTable.fieldTypeText": "Text", "wiki.aiTable.fieldTypeSelect": "Select", "wiki.aiTable.fieldTypeMultiSelect": "Multi-select", "wiki.aiTable.fieldTypeNumber": "Number", "wiki.aiTable.fieldTypeDate": "Date", "wiki.aiTable.fieldTypeMember": "Member", "wiki.aiTable.fieldTypeProgress": "Progress", "wiki.aiTable.fieldTypeRate": "Rating", "wiki.aiTable.fieldTypeLink": "Link", "wiki.aiTable.fieldTypeAttachment": "Attachments", "wiki.aiTable.fieldTypeCreatedBy": "Created by", "wiki.aiTable.fieldTypeCreatedAt": "Created at", "wiki.aiTable.fieldTypeUpdatedBy": "Updated by", "wiki.aiTable.fieldTypeUpdatedAt": "Updated at", "wiki.aiTable.copyField": "Duplicate field", "wiki.aiTable.removeRecords": "Delete record", "wiki.aiTable.copy": "Copy", "wiki.aiTable.copySuffix": "Copy", "wiki.aiTable.paste": "Paste", "wiki.aiTable.tableView": "View", "wiki.aiTable.editField": "Edit field", "wiki.aiTable.removeField": "Delete field", "wiki.action.newTable": "Table", "wiki.aiTable.sort.title": "Sort", "wiki.aiTable.sort.autoSort": "Automatically sort", "wiki.aiTable.sort.optionAsc": "Asc", "wiki.aiTable.sort.optionDesc": "Desc", "wiki.aiTable.sort.numberDesc": "9→1", "wiki.aiTable.sort.selectDesc": "Desc", "wiki.aiTable.sort.defaultDesc": "Z→A", "wiki.aiTable.sort.numberAsc": "1→9", "wiki.aiTable.sort.selectAsc": "Asc", "wiki.aiTable.sort.defaultAsc": "A→Z", "wiki.aiTable.sort.selectColumn": "Pick a field", "wiki.aiTable.filter.byColumn": "Filter by this field", "wiki.aiTable.filter.removeCondition": "Cancel fillter", "wiki.space.settings.pageShared.spaceMember": "Space member", "wiki.plugins.label.pluginName": "Tag", "wiki.page.table": {"singular": "table", "plural": "tables"}, "wiki.aiTable.fieldTypeRichText": "Text area", "wiki.page.shared.title": "Page share", "wiki.aiTable.insertUpward": "Insert", "wiki.aiTable.insertDownward": "Insert", "wiki.aiTable.upward": "above", "wiki.aiTable.downward": "below", "wiki.aiTable.pasteOverMaxRecords": "The pasted content exceeds the maximum number of records allowed.", "wiki.aiTable.pasteOverMaxFields": "The pasted content exceeds the maximum number of fileds allowed.", "wiki.import.supportExcel": "Support importing files ending with .xlsx, .csv", "wiki.aiTable.viewNameExist": "View name already exists", "wiki.aiTable.duplicateView": "Duplicate view", "wiki.aiTable.fieldGroupBase": "Basic", "wiki.aiTable.fieldGroupAdvanced": "Advanced", "wiki.aiTable.rowAddFilterTooltip": "This record has been filtered. Clicking outside the record will hide it.", "wiki.plugin.insight": "Insight", "wiki.plugin.relationReport": "Report", "wiki.relation.reportDeleted": "The report has been deleted or you don't have permission to view it", "wiki.plugin.drawio": "Draw.io", "wiki.plugin.noGraphics": "Empty", "wiki.plugin.thirdPartyService": "Third-party services", "wiki.aiTable.none": "None", "wiki.aiTable.countAll": "Count", "wiki.aiTable.countAllResult": "{{statValue}} records", "wiki.aiTable.filled": "Filled", "wiki.aiTable.filledResult": "Filled {{statValue}}", "wiki.aiTable.empty": "Empty", "wiki.aiTable.emptyResult": "Empty {{statValue}}", "wiki.aiTable.unique": "Unique", "wiki.aiTable.uniqueResult": "Unique {{statValue}}", "wiki.aiTable.percentFilled": "Percent filled", "wiki.aiTable.percentFilledResult": "Percent filled {{statValue}}%", "wiki.aiTable.percentEmpty": "Percent empty", "wiki.aiTable.percentEmptyResult": "Percent empty {{statValue}}%", "wiki.aiTable.percentUnique": "Percent unique", "wiki.aiTable.percentUniqueResult": "Percent unique {{statValue}}%", "wiki.aiTable.sum": "Sum", "wiki.aiTable.sumResult": "Sum {{statValue}}", "wiki.aiTable.max": "Max", "wiki.aiTable.maxResult": "Max {{statValue}}", "wiki.aiTable.min": "Min", "wiki.aiTable.minResult": "Min {{statValue}}", "wiki.aiTable.average": "Average", "wiki.aiTable.averageResult": "Average {{statValue}}", "wiki.aiTable.checked": "Checked", "wiki.aiTable.checkedResult": "Checked {{statValue}}", "wiki.aiTable.unChecked": "Unchecked", "wiki.aiTable.unCheckedResult": "unchecked {{statValue}}", "wiki.aiTable.percentChecked": "Percent checked", "wiki.aiTable.percentCheckedResult": "Percent checked {{statValue}}", "wiki.aiTable.percentUnChecked": "Percent unchecked", "wiki.aiTable.percentUnCheckedResult": "Percent unchecked {{statValue}}", "wiki.aiTable.earliestTime": "Earliest time", "wiki.aiTable.earliestTimeResult": "Earliest time {{statValue}}", "wiki.aiTable.latestTime": "Lastest time", "wiki.aiTable.latestTimeResult": "Lastest time {{statValue}}", "wiki.aiTable.dateRangeOfDays": "Date range (days)", "wiki.aiTable.dateRangeOfDaysResult": "Range {{statValue}} days", "wiki.aiTable.dateRangeOfMonths": "Date range (months)", "wiki.aiTable.dateRangeOfMonthsResult": "Range {{statValue}} months", "wiki.aiTable.selectedRecordsCount": "{count} recordss selected", "wiki.aiTable.selectedCellsCount": "{count} cells selected", "wiki.aiTable.stat": "<PERSON><PERSON><PERSON>", "wiki.aiTable.fieldTypeCheckbox": "Checkbox"}