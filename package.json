{"name": "pc-wiki-sky", "version": "6.6.0", "scripts": {"ng": "ng", "start": "ng serve wiki --serve-path /static/wiki/", "start-alone": "cross-env NODE_ENV=alone ng serve wiki-alone", "start:outside": "ng serve outside", "start:drawio": "node drawio-server.js", "build": "NODE_OPTIONS=--max_old_space_size=8000 ng build wiki", "build:cpx-i18n": "cpx \"src/assets/i18n/**/*\" \"outside/src/assets/i18n/\"", "build:drawio": "cpx \"drawio/**/*\" \"dist/drawio/\"", "build:outside": "NODE_OPTIONS=--max_old_space_size=8000 ng build outside", "start:serve": "cross-env HOST=localhost PORT=1234 node ./node_modules/y-websocket/bin/server.js", "build:alpha": "npm run build-app:alpha && npm run build-outside:alpha && npm run build:drawio", "build:prod": "npm run build-app:prod && npm run build-outside:prod && npm run build:drawio", "build:on-premises": "npm run build-app:on-premises && npm run build-outside:on-premises && npm run build:drawio", "build-app:alpha": "NODE_ENV=alpha NODE_OPTIONS=--max_old_space_size=8000 ng build wiki --configuration production", "build-app:prod": "NODE_ENV=production NODE_OPTIONS=--max_old_space_size=8000 ng build wiki --configuration production", "build-app:on-premises": "NODE_ENV=on-premises NODE_OPTIONS=--max_old_space_size=8000 ng build wiki --configuration alpha", "build-outside:alpha": "NODE_ENV=alpha NODE_OPTIONS=--max_old_space_size=8000 ng build outside --configuration alpha", "build-outside:prod": "NODE_ENV=production NODE_OPTIONS=--max_old_space_size=8000 ng build outside --configuration production", "build-outside:on-premises": "PC_STATIC_PATH=static/wiki/outside/ NODE_ENV=on-premises NODE_OPTIONS=--max_old_space_size=8000 ng build outside --configuration alpha", "build-drawio:alpha": "npm run build:drawio", "build-drawio:prod": "npm run build:drawio", "build-drawio:on-premises": "npm run build:drawio", "test": "ng test wiki", "lint": "ng lint --cache", "e2e": "ng e2e", "format": "prettier --check --write \"**/*\"", "release": "wpm release", "pub": "wpm publish", "eslint": "eslint --fix"}, "private": true, "repository": {"type": "git", "url": "https://github.com/atinc/pc-wiki-sky"}, "dependencies": {"@ai-table/grid": "0.1.33", "@ai-table/state": "0.1.33", "@ai-table/utils": "0.1.33", "@angular/animations": "^19.2.7", "@angular/cdk": "^19.2.10", "@angular/common": "^19.2.7", "@angular/compiler": "^19.2.7", "@angular/core": "^19.2.7", "@angular/forms": "^19.2.7", "@angular/platform-browser": "^19.2.7", "@angular/platform-browser-dynamic": "^19.2.7", "@angular/router": "^19.2.7", "@atinc/ngx-styx": "19.3.21", "@atinc/selene": "2.3.25", "@ngx-translate/core": "16.0.3", "@plait/angular-board": "0.82.0-next.0", "@plait/angular-text": "0.82.0-next.0", "@plait/common": "0.82.0-next.0", "@plait/core": "0.82.0-next.0", "@plait/draw": "0.82.0-next.0", "@plait/flow": "0.82.0-next.0", "@plait/graph-viz": "0.82.0-next.0", "@plait/layouts": "0.82.0-next.0", "@plait/mind": "0.82.0-next.0", "@plait/text-plugins": "0.82.0-next.0", "@sentry/angular": "^7.108.0", "@sentry/browser": "^7.43.0", "@sentry/tracing": "^7.108.0", "@tethys/cdk": "19.0.7", "@tethys/icons": "^1.4.79", "@tethys/pro": "^19.0.5", "@tethys/store": "19.0.0", "@worktile/planet": "19.0.0", "@worktile/planet-postcss-prefixwrap": "^1.19.12", "@worktile/theia": "~19.2.1", "@worktile/tiny-marked": "0.0.17", "@worktile/util-kits": "0.0.8", "@worktile/y-slate": "^0.1.24", "angular-gridster2": "^19.0.0", "angularx-qrcode": "~19.0.0", "blueimp-md5": "^2.11.0", "bootstrap": "5.3.1", "core-js": "^3.6.4", "cropperjs": "^1.5.13", "cross-env": "^7.0.3", "date-fns": "4.1.0", "debug": "^4.3.4", "flowchart.js": "^1.17.1", "graphology": "^0.25.4", "graphology-layout": "^0.6.1", "graphology-layout-forceatlas2": "^0.10.1", "highcharts": "^7.2.1", "immer": "^10.0.3", "immutable": "^3.8.2", "is-hotkey": "^0.2.0", "jquery": "^2.1.4", "katex": "^0.16.0", "laser-pen": "^1.0.0", "lib0": "^0.2.42", "lodash": "^4.17.21", "lz-string": "1.4.4", "marked": "^12.0.2", "math-random": "^2.0.1", "mermaid": "9.1.5", "moment": "2.24.0", "moment-timezone": "0.5.46", "ng-codemirror": "^19.0.0", "ngx-bootstrap": "5.2.0", "ngx-tethys": "19.0.18", "node-htmldiff": "^0.9.3", "path": "^0.12.7", "points-on-curve": "^1.0.0", "postcss-prefixwrap": "^1.22.2", "roughjs": "^4.5.2", "rxjs": "~7.8.0", "sass-loader": "^13.3.2", "slate": "^0.117.2", "slate-dom": "^0.116.0", "slate-history": "^0.115.0", "slate-angular": "^19.1.0-next.4", "sortablejs": "^1.10.2", "tiny-invariant": "^1.3.3", "tslib": "^2.6.2", "url-search-params-polyfill": "^8.1.0", "y-protocols": "^1.0.5", "yjs": "13.6.26", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-builders/custom-esbuild": "^19.1.0", "@angular-builders/custom-webpack": "~19.0.1", "@angular-devkit/build-angular": "^19.2.9", "@angular-eslint/builder": "19.3.0", "@angular-eslint/eslint-plugin": "19.3.0", "@angular-eslint/eslint-plugin-template": "19.3.0", "@angular-eslint/schematics": "19.3.0", "@angular-eslint/template-parser": "19.3.0", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.7", "@angular/language-service": "^19.2.7", "@atinc/devkit-commitlint-config": "^1.1.0", "@atinc/devkit-config": "^1.1.0", "@commitlint/cli": "^11.0.0", "@commitlint/config-conventional": "^7.5.0", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "~2.0.3", "@types/katex": "^0.14.0", "@types/lodash": "^4.14.178", "@types/mermaid": "^8.2.9", "@types/node": "^12.11.1", "@types/sass": "^1.16.1", "@typescript-eslint/eslint-plugin": "^5.43.0", "@typescript-eslint/parser": "^5.43.0", "@worktile/angular-devkit": "^19.0.0", "@worktile/browserslist-config": "19.0.0", "@worktile/ngx-sortablejs": "^19.0.0", "@worktile/pkg-manager": "^0.0.11", "bson-objectid": "^2.0.4", "concurrently": "4.1.0", "cpx": "^1.5.0", "eslint": "^8.48.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "express": "^5.1.0", "husky": "^8.0.3", "jasmine-core": "~5.1.1", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.2", "karma-chrome-launcher": "~3.2.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "nyc": "^15.1.0", "prettier": "^3.3.3", "pretty-quick": "^3.1.3", "ts-node": "~8.3.0", "typescript": "~5.8.3", "webpack": "5.90.3"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "pretty-quick --staged"}}, "browserslist": ["extends @worktile/browserslist-config"]}