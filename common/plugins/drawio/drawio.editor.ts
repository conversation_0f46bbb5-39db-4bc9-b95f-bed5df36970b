import { DrawioElement } from '@wiki/common/custom-types';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { TheEditor, TheTransforms } from '@worktile/theia';
import { Editor, Element, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { ThyDialog, ThyDialogSizes } from 'ngx-tethys/dialog';
import { WikiCommonGraphEditorComponent } from './graph-editor/graph-editor.component';

interface DrawioAttribute {
    data: { xml: string };
    height: number;
}

export const DrawioEditor = {
    insert(editor: Editor, props: Partial<DrawioAttribute>) {
        const elements: DrawioElement = {
            type: WikiPluginTypes.drawio,
            data: props.data,
            children: [{ text: '' }]
        };
        TheTransforms.insertElements(editor, elements);
    },
    setAttribute(editor: Editor, element: DrawioElement, props: Partial<DrawioAttribute>) {
        Transforms.setNodes(editor, props, {
            at: AngularEditor.findPath(editor, element)
        });
    },
    removeNode(editor: Editor, element: Element) {
        Transforms.move(editor, { reverse: true });
        const selection = editor.selection;
        const path = TheEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at: path });
        Transforms.select(editor, selection);
        TheEditor.focus(editor);
    },
    openDrawio(editor: TheEditor, element?: DrawioElement) {
        const thyDialog = editor.injector.get(ThyDialog);
        const dialogRef = thyDialog.open(WikiCommonGraphEditorComponent, {
            size: ThyDialogSizes.full,
            initialState: {
                editor: editor,
                element: element,
                closeGraphEditor: () => thyDialog.close()
            }
        });
        return dialogRef;
    }
};
