import { ChangeDetectionStrategy, Component, ElementRef, inject, OnInit, OnD<PERSON>roy, viewChild, input } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { AppRootContext, StyxTranslateService } from '@atinc/ngx-styx';
import { TheEditor } from '@worktile/theia';
import { DrawioElement } from '@wiki/common/custom-types';
import { DrawioEditor } from '../drawio.editor';

@Component({
    selector: 'graph-editor',
    templateUrl: './graph-editor.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false,
    host: {
        class: 'graph-editor'
    }
})
export class WikiCommonGraphEditorComponent implements OnInit, OnDestroy {
    readonly drawioFrame = viewChild<ElementRef<HTMLIFrameElement>>('drawioFrame');

    readonly data = input<string>();

    readonly editor = input<TheEditor>();

    readonly element = input<DrawioElement>();

    readonly closeGraphEditor = input<() => void>();

    public drawioUrl: SafeResourceUrl;

    private drawioOrigin: string;

    private sanitizer = inject(DomSanitizer);

    private translate = inject(StyxTranslateService);

    private appRootContext = inject(AppRootContext);

    private draftXml: string;

    private boundHandleMessage: (event: MessageEvent) => void;

    ngOnInit() {
        this.generateDrawioUrl();
    }

    ngAfterViewInit() {
        this.boundHandleMessage = this.handleMessage.bind(this);
        window.addEventListener('message', this.boundHandleMessage);
    }

    private generateDrawioUrl() {
        const teamBaseUrl = this.appRootContext.teamBaseUrl;
        const params = new URLSearchParams({
            embed: '1',
            lang: this.translate.currentLang.replace('zh-cn', 'zh'),
            spin: '1',
            proto: 'json',
            configure: '1',
            dark: '0'
        });
        const rawUrl = `${teamBaseUrl}/drawio/index.html?${params.toString()}`;
        this.drawioOrigin = new URL(teamBaseUrl).origin;
        this.drawioUrl = this.sanitizer.bypassSecurityTrustResourceUrl(rawUrl);
    }

    private handleMessage(event: MessageEvent): void {
        if (event.origin !== this.drawioOrigin) {
            return;
        }

        const message = event.data;

        if (typeof message === 'string') {
            try {
                const parsedMessage = JSON.parse(message);

                if (parsedMessage.event == 'configure') {
                    this.configure();
                } else if (parsedMessage.event === 'init') {
                    this.init();
                } else if (parsedMessage.event === 'save') {
                    this.saveData({ xml: parsedMessage.xml });
                    this.closeGraphEditor()();
                } else if (parsedMessage.event === 'autosave') {
                    this.draftXml = parsedMessage.xml;
                } else if (parsedMessage.event === 'exit') {
                    this.closeGraphEditor()();
                }
            } catch (error) {
                console.log(`${this.element()?.key} received message: ${message} in graph-editor.`);
            }
        }
    }

    private configure(): void {
        const message = JSON.stringify({
            action: 'configure',
            config: {
                css: `
                .geTabContainer, .geMenubarContainer .geMenubar .geItem:nth-child(1), .geMenubarContainer .geMenubar .geItem:nth-child(6) {
                    display: none;
                }
                `
            }
        });
        this.drawioFrame().nativeElement.contentWindow.postMessage(message, this.drawioOrigin);
    }

    private init(): void {
        const message = JSON.stringify({
            action: 'load',
            autosave: 1,
            xml: this.element()?.data?.xml || null
        });
        this.drawioFrame().nativeElement.contentWindow.postMessage(message, this.drawioOrigin);
    }

    private saveData(data: { xml: string }): void {
        if (this.element()) {
            DrawioEditor.setAttribute(this.editor(), this.element(), { data });
        } else {
            DrawioEditor.insert(this.editor(), { data });
        }
    }

    saveAndExit() {
        if (this.draftXml) {
            this.saveData({ xml: this.draftXml });
        }
        this.closeGraphEditor()();
    }

    ngOnDestroy() {
        if (this.boundHandleMessage) {
            window.removeEventListener('message', this.boundHandleMessage);
        }
    }
}
