@use 'ngx-tethys/styles/variables.scss';
@use '@atinc/ngx-styx/styles/variables.scss' as styx-variables;
@use '../../../styles/variables.scss' as commonVariables;

.graph-editor {
    height: 100%;
    display: flex;
    flex-direction: column;

    .drawio-header {
        min-height: 52px;
        z-index: 1000;
        padding-left: 12px;
        box-shadow: styx-variables.$layout-header-box-shadow;
        border-bottom: commonVariables.$wiki-border-default;

        > :not(.header-main) {
            display: none;
        }
    }

    .drawio-frame {
        flex: 1;
    }
}
