@use 'ngx-tethys/styles/variables.scss';

.wiki-drawio-container {
    width: calc(100% - 2px);
    max-width: 100%;
    user-select: text;

    .common-plugin-card-element {
        width: 100%;
        min-height: 460px;
        height: 460px;
        display: flex;
        align-items: center;
        justify-content: center;

        .loading-frame {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .drawio-frame {
            border-radius: 0.25rem;
        }
    }

    &.wiki-fullscreen-node {
        margin: 0 !important;
        .common-plugin-card-element {
            border: none;
            height: 100%;
        }
    }
}
