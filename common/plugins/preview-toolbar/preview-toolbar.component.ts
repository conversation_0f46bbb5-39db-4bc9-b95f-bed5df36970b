import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    DestroyRef,
    ElementRef,
    HostBinding,
    inject,
    Input,
    NgZone,
    OnDestroy,
    OnInit,
    Optional,
    Renderer2
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AppRootContext, AttachmentEntity, DateFormatPipe, getDrivePreviewUrl, StyxTranslateService } from '@atinc/ngx-styx';
import { PlaitBoard } from '@plait/core';
import { PageInfo } from '@wiki/app/entities/page-info';
import { AttachmentElement, WikiElement } from '@wiki/common/custom-types';
import { PageAttachmentEntity } from '@wiki/common/interface';
import { TheExtensionMode } from '@wiki/common/interface/extension-mode';
import { FileApiService } from '@wiki/common/services/file-api.service';
import { WikiPluginTypes } from '@wiki/common/types/editor.types';
import { setVisibility } from '@wiki/common/util/block-position';
import { base64ToBlob, boardToImage } from '@wiki/common/util/board';
import { findNodeEntryByPoint, findRelativeElementByPoint, verifyAvailableAppByBroadObjectType } from '@wiki/common/util/common';
import { download } from '@wiki/common/util/download';
import { isSharedMode } from '@wiki/common/util/extension-mode';
import { ElementKinds, THE_MODE_TOKEN, TheEditor } from '@worktile/theia';
import { Subscription, fromEvent, merge } from 'rxjs';
import { Editor } from 'slate';
import { AngularEditor, ELEMENT_TO_COMPONENT } from 'slate-angular';
import { FullscreenService, FullscreenState } from '../../services/fullscreen.service';
import { WikiCommonDiagramBoardComponent } from '../diagram-board/diagram-board.component';
import { WikiBaseFileElementComponent } from '../file/base';
import { PREVIEW_FULLSCREEN_ICON_OFFSET, TABLE_SPACING, TEXT_DIAGRAM_SPACING } from './constants';
import { ActivatedRoute, Router } from '@angular/router';
import { TinyDate } from 'ngx-tethys/util';
import { WikiCommonDrawioComponent } from '../drawio/drawio.component';
import { getCommonRelationOption } from '@wiki/common/types';
import { WikiRelationReportComponent } from '../relation-report/relation-report.component';

@Component({
    selector: '[wikiPreviewToolbar]',
    templateUrl: './preview-toolbar.component.html',
    standalone: false
})
export class WikiCommonPreviewFullscreenComponent implements OnInit, AfterViewInit, OnDestroy {
    @Input() editor: TheEditor;

    @Input() page: PageInfo;

    @Input() pageContentContainer = 'common-document-content-container';

    @HostBinding('class')
    hostClass = 'preview-toolbar-container pb-2';

    targetElement: WikiElement;

    targetDOMNode: HTMLElement;

    editableElement: Element;

    toolbarElementTypes = [
        WikiPluginTypes.textDiagram,
        WikiPluginTypes.diagramBoard,
        WikiPluginTypes.drawio,
        WikiPluginTypes.audio,
        WikiPluginTypes.video,
        WikiPluginTypes.relationWorkItemList,
        WikiPluginTypes.relationTestCaseList,
        WikiPluginTypes.relationIdeaList,
        WikiPluginTypes.relationTicketList,
        WikiPluginTypes.relationObjectiveList,
        WikiPluginTypes.attachment,
        ElementKinds.table,
        WikiPluginTypes.relationReport
    ];

    verifyAvailableAppTypes = [WikiPluginTypes.relationReport];

    embedmentTypes = [WikiPluginTypes.audio, WikiPluginTypes.video];

    fullscreenState: FullscreenState = FullscreenState.off;

    fullscreenSubscription: Subscription;

    isEmbedment = false;

    isSeparateHideFullScreen = false;

    boardTypes = [WikiPluginTypes.diagramBoard];

    drawioTypes = [WikiPluginTypes.drawio];

    reportTypes = [WikiPluginTypes.relationReport];

    isBoard = false;

    isDrawio = false;

    isAttachment = false;

    isAttachmentText = false;

    isOpenNewWindow = false;

    board: PlaitBoard;

    isSharedMode: boolean;

    fileEntity: PageAttachmentEntity;

    private translate = inject(StyxTranslateService);

    private boundHandleMessage: (event: MessageEvent) => void;

    constructor(
        @Optional()
        private elementRef: ElementRef,
        private ngZone: NgZone,
        private router: Router,
        private route: ActivatedRoute,
        private changeDetectorRef: ChangeDetectorRef,
        private renderer: Renderer2,
        private fullscreenService: FullscreenService,
        private appRootContext: AppRootContext,
        private destroyRef: DestroyRef,
        protected fileApiService: FileApiService
    ) {}

    ngOnInit() {
        const modeConfig = this.editor.injector.get(THE_MODE_TOKEN);
        this.isSharedMode = isSharedMode(modeConfig?.mode as TheExtensionMode);
        this.fullscreenSubscription = this.fullscreenService.subscribe(this.targetElement, value => {
            this.fullscreenState = value.state;
        });
    }

    ngAfterViewInit(): void {
        if (this.isSharedMode) {
            setVisibility(this.elementRef.nativeElement, false);
            return;
        }
        this.editableElement = this.elementRef.nativeElement.closest(`.${this.pageContentContainer}`);
        this.ngZone.runOutsideAngular(() => {
            if (this.editableElement) {
                merge(fromEvent<MouseEvent>(this.editableElement, `mousemove`), fromEvent<MouseEvent>(this.editableElement, `mouseleave`))
                    .pipe(takeUntilDestroyed(this.destroyRef))
                    .subscribe(e => this.fullscreenState === FullscreenState.off && this.setIcon(e));
            }
        });
        setVisibility(this.elementRef.nativeElement, false);

        this.boundHandleMessage = this.handleDrawioMessage.bind(this);
        window.addEventListener('message', this.boundHandleMessage);
    }

    private handleDrawioMessage(event: MessageEvent) {
        if (event.data && typeof event.data === 'string') {
            try {
                const parsedMessage = JSON.parse(event.data);
                if (parsedMessage.event === 'mouseenter') {
                    this.handleDrawioMouseEnter(parsedMessage.data);
                }
            } catch (error) {}
        }
    }

    private handleDrawioMouseEnter(data: { x: number; y: number; key: string; parentRect: { left: number; top: number } }) {
        if (this.fullscreenState === FullscreenState.off) {
            const scrollElement = this.editableElement.parentElement;
            const mouseEvent = new MouseEvent('mousemove', {
                clientX: data.parentRect.left - (scrollElement?.scrollLeft || 0),
                clientY: data.parentRect.top - (scrollElement?.scrollTop || 0),
                bubbles: true
            });
            this.setIcon(mouseEvent);
        }
    }

    setIcon(event: MouseEvent) {
        const pointElement = findRelativeElementByPoint(this.editableElement, this.editor, event.x, event.y);
        // 空状态时不显示组件
        if (
            (pointElement?.closest('common-empty-content') || pointElement?.querySelector('common-empty-content')) &&
            !pointElement?.closest('relation-report')
        ) {
            setVisibility(this.elementRef.nativeElement, false);
            return;
        }
        const availablePlugin = this.toolbarElementTypes.find(plugin => {
            return findNodeEntryByPoint(this.editableElement, this.editor, event.x, event.y, undefined, `.slate-element-${plugin}`);
        });

        const nodeEntry = findNodeEntryByPoint(
            this.editableElement,
            this.editor,
            event.x,
            event.y,
            undefined,
            `.slate-element-${availablePlugin}`
        );

        if (nodeEntry && !Editor.isEmpty(this.editor, nodeEntry[0])) {
            const targetNode = nodeEntry[0];
            this.targetDOMNode = AngularEditor.toDOMNode(this.editor, targetNode);
            this.isAttachment = targetNode.type === WikiPluginTypes.attachment;
            this.isAttachmentText = targetNode.type === WikiPluginTypes.attachment && (targetNode as AttachmentElement).mode === 'text';
            if (this.verifyAvailableAppTypes.includes(targetNode.type as WikiPluginTypes)) {
                const relationOption = getCommonRelationOption(this.translate)[targetNode.type];
                const isAvailableApp = verifyAvailableAppByBroadObjectType(this.editor, relationOption?.broadObjectType);
                this.isOpenNewWindow = [WikiPluginTypes.relationReport].includes(targetNode.type as WikiPluginTypes) && isAvailableApp;
            } else {
                this.isOpenNewWindow = false;
            }

            if (this.toolbarElementTypes.includes(targetNode.type)) {
                this.targetElement = targetNode;
                this.isEmbedment = this.embedmentTypes.includes(targetNode.type as WikiPluginTypes);

                // 嵌入类型元素，如视频、音频
                if (this.isEmbedment || this.isAttachmentText) {
                    const fileComponent = ELEMENT_TO_COMPONENT.get(this.targetElement) as unknown as WikiBaseFileElementComponent;
                    this.fileEntity = fileComponent.fileEntity;
                }

                this.isDrawio = this.drawioTypes.includes(targetNode.type as WikiPluginTypes);

                // 画板处理
                this.isBoard = this.boardTypes.includes(targetNode.type as WikiPluginTypes);
                if (this.isBoard) {
                    const boardComponent = ELEMENT_TO_COMPONENT.get(this.targetElement) as unknown as WikiCommonDiagramBoardComponent;
                    this.board = boardComponent?.board;
                }

                // 处理报表
                if (this.reportTypes.includes(targetNode.type as WikiPluginTypes)) {
                    const reportComponent = ELEMENT_TO_COMPONENT.get(this.targetElement) as unknown as WikiRelationReportComponent;
                    this.isSeparateHideFullScreen = reportComponent.isEmpty || !reportComponent.reportComponentLoaded;
                }

                const spacing = targetNode.type === ElementKinds.table ? TABLE_SPACING : TEXT_DIAGRAM_SPACING;
                const containerElement = this.elementRef.nativeElement.closest(`.wiki-preview-document`);

                let rootBoundingClientRect = this.targetDOMNode.getBoundingClientRect();
                let containerBoundingClientRect = containerElement.getBoundingClientRect();
                let offsetLeft = rootBoundingClientRect.left - containerBoundingClientRect.left + PREVIEW_FULLSCREEN_ICON_OFFSET;
                let offsetTop = rootBoundingClientRect.top - containerBoundingClientRect.top + spacing;

                const x = offsetLeft + 'px',
                    y = offsetTop + 'px';

                y && this.renderer.setStyle(this.elementRef.nativeElement, 'top', y);
                x && this.renderer.setStyle(this.elementRef.nativeElement, 'left', x);

                setVisibility(this.elementRef.nativeElement, true);
            } else {
                setVisibility(this.elementRef.nativeElement, false);
            }
        } else if (pointElement?.closest('.preview-toolbar-container') === this.elementRef.nativeElement) {
            return;
        } else {
            setVisibility(this.elementRef.nativeElement, false);
        }
        this.changeDetectorRef.detectChanges();
    }

    setFullscreen(event: MouseEvent) {
        if (this.route.snapshot.paramMap.get('pageIdOrShortId')) {
            this.router.navigate([], {
                queryParams: { inner: this.targetElement.key }
            });
        }
        this.fullscreenService.setFullscreen(this.editor, event, this.targetElement, true);
        setVisibility(this.elementRef.nativeElement, false);
        event.stopPropagation();
    }

    openNewWindow() {
        const relationReportComponent = ELEMENT_TO_COMPONENT.get(this.targetElement) as {
            openNewWindow?: () => void;
        };
        relationReportComponent?.openNewWindow?.();
    }

    downloadFile() {
        if (this.isAttachmentText) {
            this.fileApiService
                .addAttachmentDownloadLog(this.page._id, this.fileEntity._id, (this.fileEntity as AttachmentEntity).attachment_version_id)
                .subscribe();
        }
        const { config } = this.appRootContext.globalInfo;
        const fileSrc = `${config.box.baseUrl}/file/origin-url?version=${this.fileEntity.addition.current_version}&action=download&token=${this.fileEntity.token}`;
        window.open(fileSrc, this.isAttachmentText ? '_self' : '_blank');
    }

    downloadBoardImage() {
        if (this.targetElement?.type !== WikiPluginTypes.diagramBoard) {
            return;
        }
        boardToImage(this.board).then(image => {
            const pngImage = base64ToBlob(image);
            const dateTimeAutoFormat = new DateFormatPipe();
            const date = dateTimeAutoFormat.transform(new TinyDate().getTime(), 'yyyyMMdd');
            const imageName = `${this.page.name}-图片-${date}.png`;
            download(pngImage, imageName);
        });
    }

    downloadDrawioImage(e: Event) {
        const drawioComponent = ELEMENT_TO_COMPONENT.get(this.targetElement) as unknown as WikiCommonDrawioComponent;
        drawioComponent.downloadGraphics(e);
    }

    readonlyPreviewHandle(event: MouseEvent) {
        const previewUrl = getDrivePreviewUrl(this.fileEntity);
        event.preventDefault();
        window.open(previewUrl);
    }

    ngOnDestroy() {
        this.fullscreenSubscription?.unsubscribe();
        if (this.boundHandleMessage) {
            window.removeEventListener('message', this.boundHandleMessage);
        }
    }
}
