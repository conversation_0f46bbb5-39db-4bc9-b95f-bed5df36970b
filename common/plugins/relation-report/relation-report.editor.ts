import { createEmptyParagraph, TheEditor, TheTransforms } from '@worktile/theia';
import { of } from 'rxjs';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { RelationReportElement } from '../../custom-types';
import { CommonBroadObjectService } from '../../services/broad-object.service';
import { WikiPluginTypes } from '../../types/editor.types';
import { RelationReportSelectionInfo } from './type';

export const RelationReportEditor = {
    insert(editor: TheEditor, items: RelationReportSelectionInfo[]) {
        const elements: RelationReportElement[] = items.map(item => ({
            type: WikiPluginTypes.relationReport,
            mode: 'card',
            children: [{ text: '' }],
            _id: item._id,
            data: {
                id: item._id,
                shortId: item.short_id,
                name: item.name,
                viewId: item.view_id
            }
        }));
        if (elements.length) {
            TheTransforms.insertElements(editor, elements, true);
        }
    },

    selectReport(editor: TheEditor, handle: (items: RelationReportSelectionInfo[]) => void) {
        const broadObjectService = editor.injector.get(CommonBroadObjectService);
        broadObjectService.openReportSelection(items => {
            handle(items);
            return of(true);
        });
    },

    setAttribute(editor: Editor, element: RelationReportElement, value: { [key: string]: unknown }) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.setNodes(editor, value, { at });
    },

    removeNode(editor: Editor, element: RelationReportElement) {
        const at = AngularEditor.findPath(editor, element);
        Transforms.removeNodes(editor, { at });
        Transforms.insertNodes(editor, createEmptyParagraph(), { at });
        AngularEditor.focus(editor);
    }
};
