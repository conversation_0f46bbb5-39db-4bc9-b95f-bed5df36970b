import { Overlay } from '@angular/cdk/overlay';
import {
    ChangeDetectionStrategy,
    Component,
    HostBinding,
    NgZone,
    OnDestroy,
    OnInit,
    TemplateRef,
    ViewChild,
    ViewContainerRef,
    inject
} from '@angular/core';
import { StyxTranslateService } from '@atinc/ngx-styx/i18n/core';
import { RelationReportElement } from '@wiki/common/custom-types';
import { FullscreenService, FullscreenState } from '@wiki/common/services/fullscreen.service';
import { PageStore } from '@wiki/common/stores/page.store';
import { getCommonRelationOption, RelationItemOption } from '@wiki/common/types/relation-types';
import {
    TheBaseElement,
    TheContextService,
    TheModeType,
    TheQueries,
    createEmptyParagraph,
    getMode,
    topLeftPosition,
    updatePopoverPosition
} from '@worktile/theia';
import { ThyPopover, ThyPopoverRef } from 'ngx-tethys/popover';
import { ThyResizeEvent } from 'ngx-tethys/resizable';
import { Subscription, take } from 'rxjs';
import { Editor, Transforms } from 'slate';
import { AngularEditor } from 'slate-angular';
import { isMobileMode, isSharedMode } from '../../util/extension-mode';
import { PlanetComponentRef } from '@worktile/planet';
import { UtilService } from '@atinc/ngx-styx';
import { TheExtensionMode } from '@wiki/common/interface';
import { verifyAvailableAppByBroadObjectType } from '@wiki/common/util/common';
import { RelationReportInfo } from './type';

@Component({
    selector: 'relation-report, [relationReport]',
    templateUrl: './relation-report.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: false
})
export class WikiRelationReportComponent extends TheBaseElement<RelationReportElement, Editor> implements OnInit, OnDestroy {
    translate = inject(StyxTranslateService);
    public componentRef: PlanetComponentRef;

    @HostBinding('class') className = 'wiki-relation-report-container';

    toolbarPopoverRef: ThyPopoverRef<any>;

    resizeHeight: number;

    isFullscreen: boolean;

    fullscreen$: Subscription;

    resizeBounds = null;

    minHeight = 460;

    loadingDone: boolean;

    public initialState: {
        report?: RelationReportInfo;
        resizeDebounceDelay?: number | null;
        reportDetailUrl?: (path: string) => void;
        reportRenderCompleted?: (e: boolean) => void;
    } = {};

    get isToolbarOpened() {
        return this.toolbarPopoverRef && this.toolbarPopoverRef.getOverlayRef() && this.toolbarPopoverRef.getOverlayRef().hasAttached();
    }

    @ViewChild('toolbar', { read: TemplateRef, static: true })
    toolbar: TemplateRef<any>;

    iconName: string;

    mode: TheModeType;

    isAvailableApp = false;

    relationOption: RelationItemOption;

    get _id() {
        return this.element?._id;
    }

    get pageId() {
        return this.pageStore.snapshot.page._id;
    }

    get height(): string | null {
        if (this.isFullscreen) {
            return '100%';
        } else {
            const height = this.resizeHeight || this.element.height;
            if (height) {
                return `${height}px`;
            } else {
                return null;
            }
        }
    }

    public isEmpty: boolean;

    public reportComponentLoaded: boolean = false;

    public isSharedMode = false;

    public emptyText: string;

    public pageStore = inject(PageStore);
    public viewContainerRef = inject(ViewContainerRef);
    private ngZone = inject(NgZone);
    private overlay = inject(Overlay);
    private thyPopover = inject(ThyPopover);
    private contextService = inject(TheContextService);
    private fullscreenService = inject(FullscreenService);
    private util = inject(UtilService);

    private reportDetailRoute = '';

    ngOnInit() {
        super.ngOnInit();
        const mode = getMode(this.editor);
        this.isSharedMode = isSharedMode(mode as TheExtensionMode);
        this.emptyText = getCommonRelationOption(this.translate)[this.element.type].notFoundText;
        this.relationOption = getCommonRelationOption(this.translate)[this.element?.type];
        this.initialState = {
            report: this.element.data,
            resizeDebounceDelay: null, // 取消图表刷新延迟
            reportDetailUrl: (path: string) => (this.reportDetailRoute = path),
            reportRenderCompleted: (e: boolean) => this.reportRenderCompleted(e)
        };
        this.fullscreenChange();
        if (this.isInPingCodePortal()) {
            this.isAvailableApp = verifyAvailableAppByBroadObjectType(this.editor, this.relationOption?.broadObjectType);
        }
    }

    isInPingCodePortal() {
        return !isMobileMode(this.mode) && !isSharedMode(this.mode as TheExtensionMode);
    }

    ngAfterViewInit() {
        if (!this.readonly) {
            this.ngZone.onStable.pipe(take(1)).subscribe(() => {
                this.resizeBounds = {
                    nativeElement: this.contextService.getEditableElement()
                };
                this.cdr.markForCheck();
            });
        }
    }

    public componentLoaded(componentRef: PlanetComponentRef) {
        this.reportComponentLoaded = true;
    }

    reportRenderCompleted(e: boolean) {
        this.isEmpty = !e;
    }

    public openNewWindow() {
        if (this.reportDetailRoute) {
            const url = this.util.generateTeamUrl(this.reportDetailRoute);
            window.open(url, '_blank');
        }
    }

    onContextChange() {
        super.onContextChange();
        if (!this.mode) {
            this.mode = getMode(this.editor);
        }
        if (isMobileMode(this.mode)) {
            return;
        }
        if (!this.isFullscreen && TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) {
            this.openToolbar();
        } else {
            this.closeToolbar();
        }
    }

    ngOnDestroy() {
        this.fullscreen$?.unsubscribe();
    }

    fullscreenChange() {
        this.fullscreen$ = this.fullscreenService.subscribe(this.element, value => {
            this.isFullscreen = value.state === FullscreenState.on;
            if (this.isFullscreen) {
                this.resizeHeight = null;
            } else {
                if (TheQueries.isGlobalCollapsed(this.editor) && this.isCollapsedAndNonReadonly) {
                    this.openToolbar();
                }
            }
            this.cdr.detectChanges();
        });
    }

    openToolbar() {
        if (this.isToolbarOpened) {
            return;
        }

        const origin = this.elementRef.nativeElement;
        this.toolbarPopoverRef = this.thyPopover.open(this.toolbar, {
            origin,
            viewContainerRef: this.viewContainerRef,
            panelClass: 'the-plugin-toolbar-popover',
            minWidth: 0,
            placement: 'topLeft',
            hasBackdrop: false,
            manualClosure: true,
            insideClosable: false,
            outsideClosable: false,
            scrollStrategy: this.overlay.scrollStrategies.reposition(),
            autoAdaptive: true
        });

        const overlayRef = this.toolbarPopoverRef?.getOverlayRef();
        updatePopoverPosition(overlayRef, origin, [topLeftPosition]);
    }

    closeToolbar() {
        if (this.isToolbarOpened) {
            this.toolbarPopoverRef.close();
        }
    }

    onResize({ height }: ThyResizeEvent) {
        this.resizeHeight = height;
    }

    onEndResize() {
        if (!this.readonly) {
            const at = AngularEditor.findPath(this.editor, this.element);
            Transforms.setNodes(this.editor, { height: this.resizeHeight }, { at });
        }
    }

    toggleFullscreen(e: MouseEvent) {
        e.preventDefault();
        e.stopPropagation();
        this.closeToolbar();
        this.fullscreenService.setFullscreen(this.editor, e, this.element, true);
    }

    removeNode(e: Event) {
        const at = AngularEditor.findPath(this.editor, this.element);
        Transforms.removeNodes(this.editor, { at });
        Transforms.insertNodes(this.editor, createEmptyParagraph(), { at });
        AngularEditor.focus(this.editor);
    }
}
