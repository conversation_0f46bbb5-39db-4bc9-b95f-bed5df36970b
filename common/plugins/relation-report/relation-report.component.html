<div
  class="common-plugin-card-element"
  thyResizable
  [thyMinHeight]="minHeight"
  [thyBounds]="resizeBounds"
  [style.height]="height"
  (thyResize)="onResize($event)"
  (thyResizeEnd)="onEndResize()"
>
  @if (initialState.report && !isSharedMode) {
    <ng-container
      planetComponentOutlet="insight-chart"
      planetComponentOutletApp="insight"
      [planetComponentOutletInitialState]="initialState"
      (planetComponentLoaded)="componentLoaded($event)"
    ></ng-container>
  } @else if (isSharedMode) {
    <div class="shared-report h-100 p-5">
      <div class="shared-report-title mb-2 text-truncate flexible-text-container">
        {{ element?.data?.name }}
      </div>
      <thy-empty class="shared-report-empty" [thyContainer]="elementRef" thyTopAuto thySize="lg" [thyMessage]="emptyText"></thy-empty>
    </div>
  }
  @if (isCollapsedAndNonReadonly && !isFullscreen) {
    <thy-resize-handle thyDirection="bottom" class="the-resizable-handle-bottom"></thy-resize-handle>
  }
</div>

<ng-template #toolbar>
  <thy-actions thySize="xxs">
    @if (!isEmpty) {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="arrows-alt"
        styxI18nTracking
        [thyTooltip]="'common.fullScreen' | translate"
        thyTooltipPlacement="top"
        (pointerup)="toggleFullscreen($event)"
      ></a>
    }
    @if (isAvailableApp) {
      <a
        href="javascript:;"
        thyAction
        thyActionIcon="publish"
        styxI18nTracking
        [thyTooltip]="'styx.openNew' | translate"
        (mousedown)="openNewWindow($event)"
      ></a>
    }
    <thy-divider class="ml-1 mr-2 align-self-center" [thyVertical]="true" thyColor="light"></thy-divider>
    <a
      href="javascript:;"
      thyAction
      thyType="danger"
      thyActionIcon="trash"
      styxI18nTracking
      [thyTooltip]="'common.delete' | translate"
      thyTooltipPlacement="top"
      (click)="removeNode($event)"
    ></a>
  </thy-actions>
</ng-template>

<ng-template #placeholderTemplate>
  <div class="common-placeholder-loading">
    <thy-icon thyIconName="task-board"></thy-icon>
  </div>
</ng-template>
