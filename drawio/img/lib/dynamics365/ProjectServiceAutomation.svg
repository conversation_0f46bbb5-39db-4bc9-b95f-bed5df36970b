<svg xmlns="http://www.w3.org/2000/svg" width="90" height="84" fill="none" xmlns:v="https://vecta.io/nano"><g clip-path="url(#H)" transform="translate(-6 -6)"><mask id="A" mask-type="alpha" maskUnits="userSpaceOnUse" x="6" y="6" width="90" height="84"><path d="M6 10a4 4 0 0 1 4-4h42a4 4 0 0 1 4 4v17h36a4 4 0 0 1 4 4v34a4 4 0 0 1-4 4H56v17a4 4 0 0 1-4 4H10a4 4 0 0 1-4-4V56a4 4 0 0 1 4-4h36v-8H10a4 4 0 0 1-4-4z" fill="url(#D)"/></mask><g mask="url(#A)"><path d="M6 10a4 4 0 0 1 4-4h42a4 4 0 0 1 4 4v30a4 4 0 0 1-4 4H10a4 4 0 0 1-4-4z" fill="url(#E)"/><path d="M6 86a4 4 0 0 0 4 4h42a4 4 0 0 0 4-4V56a4 4 0 0 0-4-4H10a4 4 0 0 0-4 4z" fill="url(#F)"/><g filter="url(#B)"><rect x="46" y="29" width="50" height="42" rx="4" fill="#000" fill-opacity=".32"/></g><g filter="url(#C)"><rect x="46" y="27.38" width="50" height="42" rx="4" fill="#000" fill-opacity=".24"/></g><rect x="46" y="27" width="50" height="42" rx="4" fill="url(#G)"/></g></g><defs><filter id="B" x="38" y="21" width="66" height="58" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0"/><feBlend in="SourceGraphic"/><feGaussianBlur stdDeviation="4"/></filter><filter id="C" x="45.24" y="26.62" width="51.52" height="43.52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"><feFlood flood-opacity="0"/><feBlend in="SourceGraphic"/><feGaussianBlur stdDeviation=".38"/></filter><linearGradient id="D" x1="6" y1="6" x2="87.715" y2="94.418" href="#I"><stop stop-color="#ffc8a3"/><stop offset="1" stop-color="#f8b48a"/></linearGradient><linearGradient id="E" x1="56" y1="44" x2="25.554" y2="-5.925" href="#I"><stop stop-color="#d83b01"/><stop offset="1" stop-color="#ed7e43"/></linearGradient><linearGradient id="F" x1="71.625" y1="112.023" x2="-16.69" y2="76.643" href="#I"><stop offset=".004" stop-color="#922717"/><stop offset="1" stop-color="#d83b01"/></linearGradient><linearGradient id="G" x1="46" y1="27" x2="86.299" y2="75.45" href="#I"><stop stop-color="#ffc8a3"/><stop offset="1" stop-color="#f8b48a"/></linearGradient><clipPath id="H"><path fill="#fff" d="M0 0h96v96H0z"/></clipPath><linearGradient id="I" gradientUnits="userSpaceOnUse"/></defs></svg>