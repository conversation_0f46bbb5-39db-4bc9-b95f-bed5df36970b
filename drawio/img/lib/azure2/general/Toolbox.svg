<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="17"
   height="14.862558"
   viewBox="0 0 17 14.862558"
   version="1.1"
   id="svg50301"
   sodipodi:docname="Toolbox.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview50303"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="8.5"
     inkscape:cy="7.4309998"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg50301" />
  <defs
     id="defs50280">
    <linearGradient
       id="a1711304-680d-4c8b-88a3-f1ba4b9a6455"
       x1="9"
       y1="16.431"
       x2="9"
       y2="4.559"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(0.5,1.5684412)">
      <stop
         offset="0"
         stop-color="#0078d4"
         id="stop50269" />
      <stop
         offset="0.156"
         stop-color="#1380da"
         id="stop50271" />
      <stop
         offset="0.528"
         stop-color="#3c91e5"
         id="stop50273" />
      <stop
         offset="0.822"
         stop-color="#559cec"
         id="stop50275" />
      <stop
         offset="1"
         stop-color="#5ea0ef"
         id="stop50277" />
    </linearGradient>
  </defs>
  <title
     id="title50282">MsPortalFx.base.images-78</title>
  <g
     id="bb1c4aae-a407-4f6f-98e2-81891d0404e1"
     transform="translate(-0.5,-1.5684412)">
    <g
       id="g50296">
      <path
         d="m 13.135,4.659 h -0.85 V 2.422 a 0.1,0.1 0 0 0 -0.026,0 H 5.845 a 0.093,0.093 0 0 0 -0.026,0 v 2.237 h -0.85 v -2.28 a 0.846,0.846 0 0 1 0.876,-0.81 h 6.414 a 0.846,0.846 0 0 1 0.876,0.81 z"
         id="path50284"
         inkscape:connector-curvature="0"
         style="fill:#a3a3a3" />
      <rect
         x="0.5"
         y="4.559"
         width="17"
         height="11.872"
         rx="0.56699997"
         id="rect50286"
         style="fill:url(#a1711304-680d-4c8b-88a3-f1ba4b9a6455)" />
      <rect
         x="0.5"
         y="7.5939999"
         width="17"
         height="1.311"
         id="rect50288"
         style="fill:#9cebff" />
      <path
         d="M 6.617,8.905 H 11.5 v 1.531 a 0.281,0.281 0 0 1 -0.281,0.281 H 6.9 A 0.281,0.281 0 0 1 6.619,10.436 V 8.905 Z"
         id="path50290"
         inkscape:connector-curvature="0"
         style="fill:#005ba1" />
      <polygon
         points="5.144,15.378 6.015,12.662 0.5,10.894 0.5,13.889 "
         id="polygon50292"
         style="fill:#83b9f9" />
      <path
         d="m 17.5,10.745 a 2.718,2.718 0 1 0 0,4.837 z"
         id="path50294"
         inkscape:connector-curvature="0"
         style="fill:#5ea0ef" />
    </g>
  </g>
  <metadata
     id="metadata50299">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk: MsPortalFx.Base.Images.Polychromatic.Toolbox()</rdf:li>
      <rdf:li>category: General</rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>MsPortalFx.base.images-78</dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
