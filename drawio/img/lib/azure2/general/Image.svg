<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="17"
   height="11.76"
   viewBox="0 0 17 11.76"
   version="1.1"
   id="svg27877"
   sodipodi:docname="Image.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview27879"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="8.5"
     inkscape:cy="5.8800001"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg27877" />
  <defs
     id="defs27860">
    <linearGradient
       id="f6a16bda-952e-4567-8fdb-a66e2f210003"
       x1="9"
       y1="3.1199999"
       x2="9"
       y2="14.88"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(0.5,3.1199999)">
      <stop
         offset="0"
         stop-color="#32d4f5"
         id="stop27847" />
      <stop
         offset="0.228"
         stop-color="#31d0f1"
         id="stop27849" />
      <stop
         offset="0.463"
         stop-color="#2cc3e6"
         id="stop27851" />
      <stop
         offset="0.703"
         stop-color="#25afd4"
         id="stop27853" />
      <stop
         offset="0.944"
         stop-color="#1c92ba"
         id="stop27855" />
      <stop
         offset="1"
         stop-color="#198ab3"
         id="stop27857" />
    </linearGradient>
  </defs>
  <title
     id="title27862">MsPortalFx.base.images-74</title>
  <g
     id="ae6f4164-ac1c-4313-a292-cc2c642036fa"
     transform="translate(-0.5,-3.1199999)">
    <g
       id="g27872">
      <rect
         x="0.5"
         y="3.1199999"
         width="17"
         height="11.76"
         rx="0.57200003"
         id="rect27864"
         style="fill:url(#f6a16bda-952e-4567-8fdb-a66e2f210003)" />
      <path
         d="M 17.463,9.912 16.235,8.8 a 1.374,1.374 0 0 0 -1.785,0 l -6.622,6.08 h 9.1 A 0.572,0.572 0 0 0 17.5,14.308 v -4.4 z"
         id="path27866"
         inkscape:connector-curvature="0"
         style="fill:#86d633" />
      <path
         d="m 15.69,14.88 -5.208,-4.731 a 1.06,1.06 0 0 0 -1.376,0 L 3.9,14.88 Z"
         id="path27868"
         inkscape:connector-curvature="0"
         style="fill:#b4ec36" />
      <path
         d="M 9,7.852 A 1.555,1.555 0 0 0 7.649,6.352 1.962,1.962 0 0 0 5.628,4.479 2.027,2.027 0 0 0 3.7,5.792 1.847,1.847 0 0 0 2.072,7.578 1.885,1.885 0 0 0 4.023,9.39 c 0.058,0 0.115,0 0.172,-0.008 H 7.354 A 0.326,0.326 0 0 0 7.437,9.37 1.575,1.575 0 0 0 9,7.852 Z"
         id="path27870"
         inkscape:connector-curvature="0"
         style="fill:#ffffff" />
    </g>
  </g>
  <metadata
     id="metadata27875">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk: MsPortalFx.Base.Images.Polychromatic.Image()</rdf:li>
      <rdf:li>category: General</rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>MsPortalFx.base.images-74</dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
