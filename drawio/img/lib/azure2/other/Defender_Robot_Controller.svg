<svg
	xmlns="http://www.w3.org/2000/svg" width="17.469" height="17.951" fill="none">
	<path d="M9.409 4.074l.771-.655c.176-.15.445-.121.599.065s.137.458-.039.608l-.903.767a.41.41 0 0 1-.308.094l-1.695-.185c-.153-.017-.311-.148-.364-.279l-.635-1.561-1.433-.156a.45.45 0 0 1-.396-.476c.015-.238.217-.409.452-.384l1.433.156.826-1.402a.43.43 0 0 1 .395-.196l1.695.185a.45.45 0 0 1 .292.159l.792.951c.155.186.137.458-.039.608s-.445.121-.599-.065l-.676-.812-1.258-.137-.719 1.22.552 1.358z" fill="#605e5c"/>
	<path d="M9.66 11.267l-.01-.01c-.455-.396-2.051-1.602-2.667-1.602l-2.465 1.968.96 1.315c-.01.059-.01.119-.01.178v2.044c0 .277.222.495.505.495h4.04c.283 0 .505-.218.505-.495v-2.044c0-.722-.313-1.395-.859-1.849zM5.363 3.864l-.826 4.052c-.023.115-.085.213-.169.281-.091.073-.208.113-.333.104l-1.782-.126c-.481-.032-.904.297-1.005.782a.48.48 0 0 1-.59.369C.39 9.257.217 8.981.271 8.708l1.28-6.343c.059-.274.32-.441.59-.372.691.174.138.893.601 1.48.352.447.983.533 1.406.191l.407-.329c.163-.132.394-.14.576-.021s.275.34.232.55z" fill="#a19f9d"/>
	<path d="M10.751 14.4H4.838c-1.359 0-2.464 1.159-2.464 2.583 0 .285.22.517.493.517h9.855c.272 0 .493-.231.493-.517 0-1.424-1.105-2.583-2.464-2.583z" fill="url(#A)"/>
	<path d="M9.821 10.655c-.545-.246-1.152-.28-1.717-.095-1.151.377-1.932 1.624-1.591 3.037l-5.282-2.403C.037 10.61-.329 9.174.312 8.165c.52-.821 1.539-1.133 2.426-.74l7.083 3.23.019.009z" fill="url(#B)"/>
	<path d="M5.047 4.643l-1.598-.284c-1.174-.209-2.002-1.349-1.843-2.541C1.762.637 2.828-.179 4.023.034l1.598.284c.294.052.5.337.461.635l-.431 3.243c-.04.299-.31.498-.604.446z" fill="url(#C)"/>
	<path d="M10.721 14.64c1.398 0 2.531-1.185 2.531-2.646s-1.133-2.646-2.531-2.646-2.531 1.185-2.531 2.646 1.133 2.646 2.531 2.646z" fill="#fff"/>
	<path d="M17.056 12.817l.162-.061v-.173-1.246-.17l-.159-.062-.168-.066h0l-.011-.004-1.165-.399-.254-.647.605-1.345.069-.154-.116-.122-.836-.874-.129-.135-.166.086-.168.087h0l-.002.001-1.085.577-.604-.259-.486-1.395-.059-.168h-.178-1.192-.177l-.059.166-.062.174h0l-.003.009-.384 1.223-.601.258-1.254-.638-.166-.084-.128.134-.836.874-.119.125.075.156.083.174h0v.002l.559 1.147-.255.649-1.358.508-.162.061v.173 1.244.17l.159.062.168.066h0l.01.004 1.166.4.254.646-.605 1.346-.069.154.116.122.836.874.13.136.167-.088.168-.088h.001l1.085-.576.604.259.486 1.404.058.168h.178 1.192.176l.059-.165.063-.175h0l.003-.01.383-1.223.603-.259 1.276.627.164.081.127-.132.836-.874.119-.124-.074-.155-.083-.175h0l-.001-.002-.559-1.147.253-.646zm-5.312 1.323c-.402 0-.796-.124-1.132-.359a2.13 2.13 0 0 1-.756-.964 2.25 2.25 0 0 1-.117-1.247c.08-.419.276-.802.563-1.102s.651-.503 1.045-.585a1.95 1.95 0 0 1 1.175.121 2.07 2.07 0 0 1 .918.789c.226.354.347.771.347 1.198 0 .573-.218 1.121-.603 1.523s-.903.625-1.442.625z" fill="url(#D)" stroke="#fff" stroke-width=".5"/>
	<defs>
		<linearGradient id="A" x1="7.795" y1="13.887" x2="7.795" y2="17.966" gradientUnits="userSpaceOnUse">
			<stop stop-color="#e6e6e6"/>
			<stop offset="1" stop-color="#999"/>
		</linearGradient>
		<linearGradient id="B" x1="3.366" y1="5.38" x2="6.454" y2="14.812" gradientUnits="userSpaceOnUse">
			<stop stop-color="#e6e6e6"/>
			<stop offset="1" stop-color="#999"/>
		</linearGradient>
		<linearGradient id="C" x1="4.118" y1="-.681" x2="3.113" y2="4.966" gradientUnits="userSpaceOnUse">
			<stop stop-color="#e6e6e6"/>
			<stop offset="1" stop-color="#999"/>
		</linearGradient>
		<linearGradient id="D" x1="11.744" y1="17.451" x2="11.744" y2="6.538" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0078d4"/>
			<stop offset=".156" stop-color="#1380da"/>
			<stop offset=".528" stop-color="#3c91e5"/>
			<stop offset=".822" stop-color="#559cec"/>
			<stop offset="1" stop-color="#5ea0ef"/>
		</linearGradient>
	</defs>
</svg>