<svg
	xmlns="http://www.w3.org/2000/svg" width="12.6" height="18" fill="none">
	<path d="M6.963 10.328c.987.516 1.658 1.527 1.658 2.69 0 1.686-1.41 3.054-3.15 3.054s-3.15-1.367-3.15-3.053c0-1.163.671-2.174 1.658-2.69V3.696c0-.799.668-1.446 1.492-1.446s1.492.648 1.492 1.446zM4.311 3.696c0-.621.52-1.125 1.161-1.125s1.161.504 1.161 1.125v6.482.107.242l.167.08c.887.46 1.491 1.367 1.491 2.411 0 1.509-1.262 2.732-2.818 2.732s-2.818-1.223-2.818-2.732c0-1.044.604-1.951 1.491-2.411l.167-.08v-.242-.039-.069zm2.321 6.482l.332.15c-.107-.056-.218-.106-.332-.15zm2.886-5.357c.641 0 1.161-.504 1.161-1.125s-.52-1.125-1.161-1.125-1.161.504-1.161 1.125.52 1.125 1.161 1.125zm0-.321c.458 0 .829-.36.829-.804s-.371-.804-.829-.804-.829.36-.829.804.371.804.829.804zm1.161 1.99c0 .621-.52 1.125-1.161 1.125s-1.16-.503-1.16-1.125.52-1.125 1.161-1.125 1.161.504 1.161 1.125zm-.332 0c0 .444-.371.804-.829.804s-.829-.36-.829-.804.371-.804.829-.804.829.36.829.804zM0 1.929C0 .863.891 0 1.989 0h8.621c1.099 0 1.99.863 1.99 1.929v14.143c0 1.065-.891 1.929-1.989 1.929H1.989C.891 18 0 17.137 0 16.071zM1.989.321h8.621c.916 0 1.658.72 1.658 1.607v14.143c0 .888-.742 1.607-1.658 1.607H1.989c-.916 0-1.658-.72-1.658-1.607V1.929c0-.888.742-1.607 1.658-1.607z" fill-rule="evenodd" fill="#9cebff"/>
	<path d="M9.5 8.308c.055 0 .1.04.107.092l.001.014v.475l.312-.271c.045-.039.113-.034.152.01s.035.111-.01.15l-.454.394v.413h.418l.399-.449c.036-.04.095-.047.139-.019l.013.01c.041.035.048.094.019.138l-.01.013-.274.308h.48c.055 0 .1.04.107.092l.001.014c0 .054-.04.098-.093.106l-.015.001h-.481l.274.309c.036.04.035.099.001.139l-.011.011c-.041.035-.101.035-.141.001l-.011-.011-.399-.449h-.418v.413l.454.395c.041.035.048.094.019.138l-.01.013c-.036.04-.095.047-.139.019l-.013-.009-.312-.271v.475c0 .054-.04.098-.093.106l-.015.001c-.055 0-.1-.04-.107-.092l-.001-.014v-.475l-.311.271c-.045.039-.113.035-.152-.01s-.035-.112.01-.15l.454-.394v-.413h-.386l-.43.485c-.039.044-.107.049-.152.01s-.049-.106-.01-.15l.306-.344h-.512c-.055 0-.1-.04-.107-.092L8.1 9.692c0-.054.04-.098.093-.106l.015-.001h.449l-.242-.273c-.039-.044-.035-.112.01-.15s.113-.034.152.01l.367.413h.449v-.413l-.454-.395c-.041-.035-.048-.094-.019-.138l.01-.013c.036-.04.095-.047.139-.019l.013.01.312.271v-.475c0-.049.034-.09.079-.103l.014-.003z" fill="#9cebff"/>
	<path fill-rule="evenodd" d="M10.611.321H1.989c-.916 0-1.658.72-1.658 1.607v14.143c0 .888.742 1.607 1.658 1.607h8.621c.916 0 1.658-.72 1.658-1.607V1.929c0-.888-.742-1.607-1.658-1.607zM6.963 10.328c.987.516 1.658 1.527 1.658 2.69 0 1.686-1.41 3.054-3.15 3.054s-3.15-1.367-3.15-3.053c0-1.163.671-2.174 1.658-2.69V3.696c0-.799.668-1.446 1.492-1.446s1.492.648 1.492 1.446zm2.555-5.506c.641 0 1.161-.504 1.161-1.125s-.52-1.125-1.161-1.125-1.161.504-1.161 1.125.52 1.125 1.161 1.125zm1.161 1.669c0 .621-.52 1.125-1.161 1.125S8.358 7.112 8.358 6.49s.52-1.125 1.161-1.125 1.161.504 1.161 1.125zM9.607 8.4c-.007-.052-.052-.092-.107-.092l-.015.001-.014.003c-.046.012-.079.054-.079.103v.475l-.312-.271-.013-.01c-.044-.028-.104-.021-.139.019l-.01.013c-.029.044-.021.102.019.138l.454.395v.413h-.449l-.367-.413c-.039-.044-.107-.049-.152-.01s-.049.106-.01.15l.242.273h-.449l-.015.001c-.053.007-.093.052-.093.106l.001.014c.007.052.052.092.107.092h.512l-.306.344c-.039.044-.035.111.01.15s.113.034.152-.01l.43-.485h.386v.413l-.454.395c-.045.039-.049.106-.01.15s.107.049.152.01l.311-.271v.475l.001.014c.007.052.052.092.107.092l.015-.001c.053-.007.093-.052.093-.106v-.475l.312.271.013.009c.044.028.104.021.139-.019l.01-.013c.028-.044.021-.102-.019-.138l-.454-.395v-.413h.418l.399.449.011.011c.04.034.1.034.141-.001l.011-.011c.034-.04.034-.099-.001-.139l-.274-.309h.481l.015-.001c.053-.007.093-.052.093-.106l-.001-.014c-.007-.052-.052-.092-.107-.092h-.48l.274-.308.01-.013c.029-.044.021-.102-.019-.138l-.013-.01c-.044-.028-.104-.021-.139.019l-.399.449h-.418v-.413l.454-.394c.045-.039.049-.106.01-.15s-.107-.049-.152-.01l-.312.271v-.475z" fill="#9cebff"/>
	<path d="M6.3 13.018c0 .444-.371.804-.829.804s-.829-.36-.829-.804c0-.347.227-.643.545-.755v-.336-3.945a.33.33 0 1 1 .663 0v3.973.349a.8.8 0 0 1 .45.715z" fill="url(#A)"/>
	<path d="M9.5 8.308c.055 0 .1.04.107.092l.001.014v.475l.312-.271c.045-.039.113-.034.152.01s.035.111-.01.15l-.454.394v.413h.418l.399-.449c.036-.04.095-.047.139-.019l.013.01c.041.035.048.094.019.138l-.01.013-.274.308h.48c.055 0 .1.04.107.092l.001.014c0 .054-.04.098-.093.106l-.015.001h-.481l.274.309c.036.04.035.099.001.139l-.011.011c-.041.035-.101.035-.141.001l-.011-.011-.399-.449h-.418v.413l.454.395c.041.035.048.094.019.138l-.01.013c-.036.04-.095.047-.139.019l-.013-.01-.312-.271v.475c0 .054-.041.098-.093.106l-.015.001c-.055 0-.1-.04-.107-.092l-.001-.014v-.475l-.311.271c-.045.039-.113.035-.152-.01s-.035-.112.01-.15l.454-.394v-.413h-.386l-.43.485c-.039.044-.107.049-.152.01s-.049-.106-.01-.15l.306-.344h-.512c-.055 0-.1-.04-.107-.092L8.1 9.692c0-.054.04-.098.093-.106l.015-.001h.449l-.242-.273c-.039-.044-.035-.112.01-.15s.113-.034.152.01l.367.413h.449v-.413l-.454-.395c-.041-.035-.048-.094-.019-.138l.01-.013c.036-.04.095-.047.139-.019l.013.01.312.271v-.475c0-.049.034-.09.079-.103l.014-.003z" fill="url(#B)"/>
	<path d="M9.518 7.294c.458 0 .829-.36.829-.804s-.371-.804-.829-.804-.829.36-.829.804.371.804.829.804z" fill="url(#B)"/>
	<path d="M10.347 3.696c0 .444-.371.804-.829.804s-.829-.36-.829-.804.371-.804.829-.804.829.36.829.804z" fill="url(#B)"/>
	<path d="M9.311 8.308c.055 0 .1.04.107.092l.001.014v.475l.312-.271c.045-.039.113-.034.152.01s.035.111-.01.15l-.454.394v.413h.418l.399-.449c.036-.04.095-.047.139-.019l.013.01c.041.035.048.094.019.138l-.01.013-.274.308h.48c.055 0 .1.04.107.092l.001.014c0 .054-.04.098-.093.106l-.015.001h-.481l.274.309c.036.04.035.099.001.139l-.011.011c-.041.035-.101.035-.141.001l-.011-.011-.399-.449h-.418v.413l.454.395c.041.035.048.094.02.138l-.01.013c-.036.04-.095.047-.139.019l-.013-.01-.312-.271v.475c0 .054-.04.098-.093.106l-.015.001c-.055 0-.1-.04-.107-.092l-.001-.014v-.475l-.311.271c-.045.039-.113.034-.152-.01s-.035-.112.01-.15l.454-.394v-.413h-.386l-.43.485c-.039.044-.107.049-.152.01s-.049-.106-.01-.15l.306-.344h-.512c-.054 0-.1-.04-.107-.092l-.001-.014c0-.054.04-.098.093-.106l.015-.001h.448l-.242-.273c-.039-.044-.035-.112.01-.15s.113-.034.152.01l.367.413h.449v-.413l-.454-.395c-.041-.035-.048-.094-.019-.138l.01-.013c.036-.04.095-.047.139-.019l.013.01.312.271v-.475c0-.049.034-.09.079-.103l.014-.003z" fill="url(#C)"/>
	<path d="M9.329 7.294c.458 0 .829-.36.829-.804s-.371-.804-.829-.804-.829.36-.829.804.371.804.829.804z" fill="url(#C)"/>
	<path d="M10.158 3.696c0 .444-.371.804-.829.804S8.5 4.14 8.5 3.696s.371-.804.829-.804.829.36.829.804z" fill="url(#C)"/>
	<defs>
		<radialGradient id="A" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(1.82932,0,0,2.44034,5.47138,10.7487)">
			<stop offset=".196" stop-color="#ffd70f"/>
			<stop offset=".438" stop-color="#ffcb12"/>
			<stop offset=".873" stop-color="#feac19"/>
			<stop offset="1" stop-color="#fea11b"/>
		</radialGradient>
		<linearGradient id="B" x1="7.295" y1="11.077" x2="7.295" y2="0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0078d4"/>
			<stop offset=".156" stop-color="#1380da"/>
			<stop offset=".528" stop-color="#3c91e5"/>
			<stop offset=".822" stop-color="#559cec"/>
			<stop offset="1" stop-color="#5ea0ef"/>
		</linearGradient>
		<linearGradient id="C" x1="7.105" y1="11.077" x2="7.105" y2="0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#0078d4"/>
			<stop offset=".156" stop-color="#1380da"/>
			<stop offset=".528" stop-color="#3c91e5"/>
			<stop offset=".822" stop-color="#559cec"/>
			<stop offset="1" stop-color="#5ea0ef"/>
		</linearGradient>
	</defs>
</svg>