<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="18"
   height="14.352234"
   viewBox="0 0 18 14.352234"
   version="1.1"
   id="svg2857"
   sodipodi:docname="AVS.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview2859"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="9.044"
     inkscape:cy="6.9400003"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="svg2857" />
  <defs
     id="defs2840">
    <linearGradient
       id="ff3b339c-673f-4785-8ec9-c7c05e10f5a2"
       x1="8.9560003"
       y1="14.347"
       x2="8.9560003"
       y2="1.59"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(-0.044,1.5877667)">
      <stop
         offset="0"
         stop-color="#0078d4"
         id="stop2830" />
      <stop
         offset="1"
         stop-color="#5ea0ef"
         id="stop2832" />
    </linearGradient>
    <linearGradient
       id="ebb540ac-df51-4d1a-9240-e4de893645e8"
       x1="9.0139999"
       y1="15.941"
       x2="9.0139999"
       y2="12.602"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(-0.044,1.5877667)">
      <stop
         offset="0"
         stop-color="#76bc2d"
         id="stop2835" />
      <stop
         offset="0.817"
         stop-color="#86d633"
         id="stop2837" />
    </linearGradient>
  </defs>
  <g
     id="ec090ac3-568c-4230-bc25-7e5ca8f80c39"
     transform="translate(0.044,-1.5877667)">
    <path
       d="M 17.956,10.352 A 4.045,4.045 0 0 0 14.446,6.464 5.1,5.1 0 0 0 9.2,1.59 5.228,5.228 0 0 0 4.2,5 4.827,4.827 0 0 0 -0.044,9.641 4.9,4.9 0 0 0 5.024,14.347 c 0.151,0 0.3,-0.007 0.447,-0.019 h 8.207 A 0.813,0.813 0 0 0 13.9,14.3 4.092,4.092 0 0 0 17.956,10.352 Z"
       id="path2842"
       inkscape:connector-curvature="0"
       style="fill:url(#ff3b339c-673f-4785-8ec9-c7c05e10f5a2)" />
    <path
       d="m 11.554,7.876 a 2.533,2.533 0 1 0 -3.769,2.2 v 3.279 h 2.431 V 10.1 A 2.525,2.525 0 0 0 11.554,7.876 Z M 9.466,12.6 H 8.535 v -2.24 a 2.6,2.6 0 0 0 0.486,0.049 2.514,2.514 0 0 0 0.445,-0.045 z m 0,-3.006 A 1.771,1.771 0 0 1 9.021,9.657 1.739,1.739 0 0 1 8.535,9.581 Z m 0.75,-0.4 -2.431,-0.037 a 1.785,1.785 0 1 1 2.431,0.034 z"
       id="path2844"
       inkscape:connector-curvature="0"
       style="fill:#ffffff" />
    <rect
       x="2.908"
       y="12.602"
       width="12.211"
       height="3.3380001"
       rx="0.40799999"
       id="rect2846"
       style="fill:url(#ebb540ac-df51-4d1a-9240-e4de893645e8)" />
    <rect
       x="13.269"
       y="13.186"
       width="0.912"
       height="0.912"
       rx="0.204"
       id="rect2848"
       style="fill:#ffffff" />
    <rect
       x="13.269"
       y="14.444"
       width="0.912"
       height="0.912"
       rx="0.204"
       id="rect2850"
       style="opacity:0.6;fill:#ffffff" />
    <path
       d="M 10.8,7.876 A 1.783,1.783 0 1 0 8.535,9.583 V 12.6 h 0.931 v -3 A 1.779,1.779 0 0 0 10.8,7.876 Z"
       id="path2852"
       inkscape:connector-curvature="0"
       style="fill:#76bc2d" />
  </g>
  <metadata
     id="metadata2855">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk:false</rdf:li>
      <rdf:li>category: Azure VMware Solution </rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
