<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="e99aaeca-7da4-48f7-8c69-4f9fb31d7cf7"
   width="18"
   height="16.52236"
   viewBox="0 0 18 16.52236"
   version="1.1"
   sodipodi:docname="StorSimple_Device_Managers.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview76067"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="9"
     inkscape:cy="8.2600006"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="e99aaeca-7da4-48f7-8c69-4f9fb31d7cf7" />
  <defs
     id="defs76046">
    <linearGradient
       id="b221527c-9a9d-4fa7-808a-53af4b8654b1"
       x1="9"
       y1="13.5"
       x2="9"
       y2="0.74000001"
       gradientUnits="userSpaceOnUse">
      <stop
         offset="0"
         stop-color="#0078d4"
         id="stop76035" />
      <stop
         offset="0.16"
         stop-color="#1380da"
         id="stop76037" />
      <stop
         offset="0.53"
         stop-color="#3c91e5"
         id="stop76039" />
      <stop
         offset="0.82"
         stop-color="#559cec"
         id="stop76041" />
      <stop
         offset="1"
         stop-color="#5ea0ef"
         id="stop76043" />
    </linearGradient>
  </defs>
  <title
     id="title76048">Icon-storage-89</title>
  <path
     d="m 18,8.7623585 a 4.06,4.06 0 0 0 -3.51,-3.89 5.1,5.1 0 0 0 -5.25,-4.87000004 5.23,5.23 0 0 0 -5,3.41000004 4.82,4.82 0 0 0 -4.24,4.64 4.9,4.9 0 0 0 5.07,4.7099995 h 0.44 8.21 a 1.46,1.46 0 0 0 0.22,0 A 4.1,4.1 0 0 0 18,8.7623585 Z"
     id="path76050"
     inkscape:connector-curvature="0"
     style="fill:url(#b221527c-9a9d-4fa7-808a-53af4b8654b1)" />
  <rect
     x="9.2299995"
     y="15.622359"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76052"
     style="fill:#198ab3" />
  <rect
     x="9.2299995"
     y="13.712358"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76054"
     style="fill:#32bedd" />
  <rect
     x="9.2299995"
     y="11.812359"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76056"
     style="fill:#50e6ff" />
  <rect
     x="4.9499998"
     y="7.0623589"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76058"
     style="fill:#f2f2f2" />
  <rect
     x="4.9499998"
     y="8.8123589"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76060"
     style="fill:#f2f2f2" />
  <rect
     x="4.9499998"
     y="10.572359"
     width="4.25"
     height="0.89999998"
     rx="0.30000001"
     id="rect76062"
     style="fill:#f2f2f2" />
  <metadata
     id="metadata76064">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk:MsPortalFx.Base.Images.Polychromatic.StorSimple()</rdf:li>
      <rdf:li>category: Storage</rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Icon-storage-89</dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
