<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="bfbb91d2-cb20-44cf-b1a9-0d16204a32d7"
   width="17.0005"
   height="13.880001"
   viewBox="0 0 17.0005 13.880001"
   version="1.1"
   sodipodi:docname="Data_Lake_Storage_Gen1.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview74632"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="8.5"
     inkscape:cy="6.94"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="bfbb91d2-cb20-44cf-b1a9-0d16204a32d7" />
  <defs
     id="defs74615">
    <linearGradient
       id="ab3b8b9f-732a-4d4f-a164-b86d9fbe1f71"
       x1="9.2399998"
       y1="0.95999998"
       x2="8.8500004"
       y2="16.52"
       gradientUnits="userSpaceOnUse">
      <stop
         offset="0"
         stop-color="#32d4f5"
         id="stop74602" />
      <stop
         offset="0.23"
         stop-color="#31d0f1"
         id="stop74604" />
      <stop
         offset="0.46"
         stop-color="#2cc3e6"
         id="stop74606" />
      <stop
         offset="0.7"
         stop-color="#25afd4"
         id="stop74608" />
      <stop
         offset="0.94"
         stop-color="#1c92ba"
         id="stop74610" />
      <stop
         offset="1"
         stop-color="#198ab3"
         id="stop74612" />
    </linearGradient>
  </defs>
  <title
     id="title74617">Icon-storage-90</title>
  <path
     d="M 16.6,1.540001 H 9.19 a 0.34,0.34 0 0 1 -0.22,-0.07 L 6.85,0.06000096 A 0.43,0.43 0 0 0 6.63,9.6153962e-7 H 0.4 A 0.4,0.4 0 0 0 0,0.39000096 V 13.490001 a 0.4,0.4 0 0 0 0.4,0.39 h 16.2 a 0.4,0.4 0 0 0 0.4,-0.39 v -11.55 a 0.4,0.4 0 0 0 -0.4,-0.4 z"
     id="path74619"
     inkscape:connector-curvature="0"
     style="fill:#005ba1" />
  <rect
     x="1.55"
     y="0.76000088"
     width="3.8599999"
     height="0.76999998"
     rx="0.16"
     id="rect74621"
     style="fill:#ffffff" />
  <rect
     x="1.55"
     y="0.76000088"
     width="0.76999998"
     height="0.76999998"
     rx="0.11"
     id="rect74623"
     style="fill:#1ac3f2" />
  <path
     d="M 16.6,1.520001 H 8.5 a 0.36,0.36 0 0 0 -0.28,0.12 l -1.35,1.3 a 0.43,0.43 0 0 1 -0.28,0.11 H 0.4 a 0.4,0.4 0 0 0 -0.4,0.4 v 10 a 0.4,0.4 0 0 0 0.4,0.39 h 16.2 a 0.4,0.4 0 0 0 0.4,-0.39 v -11.51 a 0.4,0.4 0 0 0 -0.4,-0.42 z"
     id="path74625"
     inkscape:connector-curvature="0"
     style="fill:url(#ab3b8b9f-732a-4d4f-a164-b86d9fbe1f71)" />
  <path
     d="m 9.68,3.640001 a 0.15,0.15 0 0 0 -0.08,0 c 0,0 -0.09,0 -0.13,0.07 l -3.16,4.59 a 0.16,0.16 0 0 0 0,0.16 0.19,0.19 0 0 0 0.15,0.1 h 1.87 l -0.83,2.78 a 0.13,0.13 0 0 0 0.07,0.18 0.1,0.1 0 0 0 0.07,0 c 0.06,0 0.09,0 0.13,-0.07 l 3.26,-4.66 c 0,0 0,-0.06 0,-0.1 a 0.17,0.17 0 0 0 -0.17,-0.16 h -2 l 0.81,-2.73 a 0.13,0.13 0 0 0 0.01,-0.16 z"
     id="path74627"
     inkscape:connector-curvature="0"
     style="fill:#ffffff" />
  <metadata
     id="metadata74629">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk:false</rdf:li>
      <rdf:li>category: Storage</rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Icon-storage-90</dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
