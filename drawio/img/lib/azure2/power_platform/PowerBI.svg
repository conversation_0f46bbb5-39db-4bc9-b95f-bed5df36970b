<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="72"
   height="96"
   viewBox="0 0 72 96"
   fill="none"
   version="1.1"
   id="svg57"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs34">
    <filter
       id="filter0_f"
       x="-0.03"
       y="-0.013333334"
       width="1.06"
       height="1.0266667">
      <feFlood
         flood-opacity="0"
         result="BackgroundImageFix"
         id="feFlood2" />
      <feBlend
         mode="normal"
         in="SourceGraphic"
         in2="BackgroundImageFix"
         result="shape"
         id="feBlend4" />
      <feGaussianBlur
         stdDeviation="0.4"
         result="effect1_foregroundBlur"
         id="feGaussianBlur6" />
    </filter>
    <filter
       id="filter1_f"
       x="-0.3"
       y="-0.13333333"
       width="1.6"
       height="1.2666667">
      <feFlood
         flood-opacity="0"
         result="BackgroundImageFix"
         id="feFlood9" />
      <feBlend
         mode="normal"
         in="SourceGraphic"
         in2="BackgroundImageFix"
         result="shape"
         id="feBlend11" />
      <feGaussianBlur
         stdDeviation="4"
         result="effect1_foregroundBlur"
         id="feGaussianBlur13" />
    </filter>
    <linearGradient
       id="paint0_linear"
       x1="46.666698"
       y1="-8.9779502e-07"
       x2="88.865303"
       y2="89.446602"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#E6AD10"
         id="stop16" />
      <stop
         offset="1"
         stop-color="#C87E0E"
         id="stop18" />
    </linearGradient>
    <linearGradient
       id="paint1_linear"
       x1="31.9974"
       y1="24"
       x2="67.485901"
       y2="92.826202"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#F6D751"
         id="stop21" />
      <stop
         offset="1"
         stop-color="#E6AD10"
         id="stop23" />
    </linearGradient>
    <linearGradient
       id="paint2_linear"
       x1="11.9974"
       y1="48"
       x2="31.608299"
       y2="94.477501"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#F9E589"
         id="stop26" />
      <stop
         offset="1"
         stop-color="#F6D751"
         id="stop28" />
    </linearGradient>
    <clipPath
       id="clip0">
      <rect
         width="96"
         height="96"
         fill="#ffffff"
         id="rect31"
         x="0"
         y="0" />
    </clipPath>
  </defs>
  <g
     clip-path="url(#clip0)"
     id="g55"
     transform="translate(-12)">
    <mask
       id="mask0"
       mask-type="alpha"
       maskUnits="userSpaceOnUse"
       x="12"
       y="0"
       width="72"
       height="96">
      <path
         d="m 52,4 c 0,-2.20914 1.7909,-4 4,-4 h 24 c 2.2091,0 4,1.79086 4,4 v 88 c 0,2.2091 -1.7909,4 -4,4 H 16 c -2.2091,0 -4,-1.7909 -4,-4 V 52 c 0,-2.2091 1.7909,-4 4,-4 H 32.0001 V 28 c 0,-2.2091 1.7909,-4 4,-4 H 52 Z"
         fill="#ffffff"
         id="path36" />
    </mask>
    <g
       mask="url(#mask0)"
       id="g53">
      <path
         d="M 84,0 V 96 H 52 V 0 Z"
         fill="url(#paint0_linear)"
         id="path39"
         style="fill:url(#paint0_linear)" />
      <g
         filter="url(#filter0_f)"
         id="g43">
        <path
           d="m 64,28.4 v 68 H 32 v -72 h 28 c 2.2091,0 4,1.7909 4,4 z"
           fill="#000000"
           fill-opacity="0.2"
           id="path41" />
      </g>
      <g
         filter="url(#filter1_f)"
         id="g47">
        <path
           d="M 64,30 V 98 H 32 V 26 h 28 c 2.2091,0 4,1.7909 4,4 z"
           fill="#000000"
           fill-opacity="0.18"
           id="path45" />
      </g>
      <path
         d="M 64,28 V 96 H 32 V 24 h 28 c 2.2091,0 4,1.7909 4,4 z"
         fill="url(#paint1_linear)"
         id="path49"
         style="fill:url(#paint1_linear)" />
      <path
         d="M 12,48 V 96 H 44 V 52 c 0,-2.2091 -1.7909,-4 -4,-4 z"
         fill="url(#paint2_linear)"
         id="path51"
         style="fill:url(#paint2_linear)" />
    </g>
  </g>
</svg>
