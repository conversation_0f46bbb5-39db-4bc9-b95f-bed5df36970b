<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   width="95.632622"
   height="76"
   viewBox="0 0 95.632622 76"
   fill="none"
   version="1.1"
   id="svg62"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:svg="http://www.w3.org/2000/svg">
  <defs
     id="defs37">
    <filter
       id="filter0_f"
       x="-0.0094117648"
       y="-0.012631579"
       width="1.0188235"
       height="1.0252632">
      <feFlood
         flood-opacity="0"
         result="BackgroundImageFix"
         id="feFlood2" />
      <feBlend
         mode="normal"
         in="SourceGraphic"
         in2="BackgroundImageFix"
         result="shape"
         id="feBlend4" />
      <feGaussianBlur
         stdDeviation="0.4"
         result="effect1_foregroundBlur"
         id="feGaussianBlur6" />
    </filter>
    <filter
       id="filter1_f"
       x="-0.094117647"
       y="-0.12631579"
       width="1.1882353"
       height="1.2526316">
      <feFlood
         flood-opacity="0"
         result="BackgroundImageFix"
         id="feFlood9" />
      <feBlend
         mode="normal"
         in="SourceGraphic"
         in2="BackgroundImageFix"
         result="shape"
         id="feBlend11" />
      <feGaussianBlur
         stdDeviation="4"
         result="effect1_foregroundBlur"
         id="feGaussianBlur13" />
    </filter>
    <linearGradient
       id="paint0_linear"
       x1="43"
       y1="55"
       x2="29"
       y2="10"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#0D36A5"
         id="stop16" />
      <stop
         offset="1"
         stop-color="#1152D4"
         id="stop18" />
    </linearGradient>
    <linearGradient
       id="paint1_linear"
       x1="46"
       y1="10"
       x2="46"
       y2="86"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#84CAFF"
         id="stop21" />
      <stop
         offset="1"
         stop-color="#61B1FB"
         id="stop23" />
    </linearGradient>
    <linearGradient
       id="paint2_linear"
       x1="37.5"
       y1="10"
       x2="37.5"
       y2="86"
       gradientUnits="userSpaceOnUse">
      <stop
         stop-color="#3B90F5"
         id="stop26" />
      <stop
         offset="1"
         stop-color="#2A78EE"
         id="stop28" />
    </linearGradient>
    <clipPath
       id="clip0">
      <rect
         width="96"
         height="96"
         fill="#ffffff"
         id="rect31"
         x="0"
         y="0" />
    </clipPath>
    <clipPath
       id="clip1">
      <rect
         width="96"
         height="96"
         fill="#ffffff"
         id="rect34"
         x="0"
         y="0" />
    </clipPath>
  </defs>
  <g
     clip-path="url(#clip0)"
     id="g60"
     transform="translate(0,-10)">
    <g
       clip-path="url(#clip1)"
       id="g58">
      <mask
         id="mask0"
         mask-type="alpha"
         maskUnits="userSpaceOnUse"
         x="-1"
         y="10"
         width="97"
         height="76">
        <path
           d="m 61.2116,10 c 1.138,0 2.2221,0.4847 2.9809,1.3328 l 30.4211,34 c 1.3587,1.5186 1.3587,3.8158 0,5.3344 l -30.4211,34 C 63.4337,85.5153 62.3496,86 61.2116,86 H 3.94634 C 0.488777,86 -1.34012,81.9095 0.965366,79.3328 L 29,48 0.965366,16.6672 C -1.34012,14.0905 0.488777,10 3.94634,10 Z"
           fill="#ffffff"
           id="path39" />
      </mask>
      <g
         mask="url(#mask0)"
         id="g56">
        <path
           d="M 63,10 29,48 -5,10 Z"
           fill="url(#paint0_linear)"
           id="path42"
           style="fill:url(#paint0_linear)" />
        <g
           filter="url(#filter0_f)"
           id="g46">
          <path
             d="m 63,10.4 -68,76 h 68 l 34,-38 z"
             fill="#000000"
             fill-opacity="0.24"
             id="path44" />
        </g>
        <g
           filter="url(#filter1_f)"
           id="g50">
          <path
             d="M 63,12 -5,88 H 63 L 97,50 Z"
             fill="#000000"
             fill-opacity="0.32"
             id="path48" />
        </g>
        <path
           d="M -5,86 63,10 97,48 63,86 Z"
           fill="url(#paint1_linear)"
           id="path52"
           style="fill:url(#paint1_linear)" />
        <path
           d="M -5,86 63,10 80,29 29,86 Z"
           fill="url(#paint2_linear)"
           id="path54"
           style="fill:url(#paint2_linear)" />
      </g>
    </g>
  </g>
</svg>
