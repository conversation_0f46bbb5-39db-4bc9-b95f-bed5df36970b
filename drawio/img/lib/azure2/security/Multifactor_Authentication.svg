<svg
	xmlns="http://www.w3.org/2000/svg" width="13.863" height="17.013">
	<defs>
		<linearGradient id="A" x1="9" y1="4.65" x2="9" y2="17.5" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#0078d4"/>
			<stop offset="1" stop-color="#1a508b"/>
		</linearGradient>
		<clipPath id="B">
			<path d="M15.929 7.908a2.971 2.971 0 0 0-2.546-2.94c-.072-.018-.126-.018-.2-.036a31.217 31.217 0 0 0-8.373 0c-.072.018-.143.018-.2.036a2.957 2.957 0 0 0-2.54 2.94 10.336 10.336 0 0 0 5.038 9.054 3.564 3.564 0 0 0 3.765 0 10.208 10.208 0 0 0 5.056-9.054z" fill="none"/>
		</clipPath>
		<linearGradient id="C" x1="9.062" y1="5.8" x2="9.062" y2="10.987" gradientUnits="userSpaceOnUse">
			<stop offset=".297" stop-color="#40c4f5"/>
			<stop offset="1" stop-color="#0095e6"/>
		</linearGradient>
		<linearGradient id="D" x1="9.062" y1="10.895" x2="9.062" y2="20.786" gradientUnits="userSpaceOnUse">
			<stop offset=".193" stop-color="#40c4f5"/>
			<stop offset="1" stop-color="#0095e6"/>
		</linearGradient>
	</defs>
	<g transform="translate(-2.068 -.486)">
		<path d="M4.806 5.025c.068-.018.119-.018.187-.035l1.173-.143a2.968 2.968 0 0 1 2.856-2.369 2.967 2.967 0 0 1 2.855 2.369l1.173.143c.068.017.136.017.187.035a3.909 3.909 0 0 1 .612.161 4.82 4.82 0 0 0-9.637 0 3.22 3.22 0 0 1 .594-.161z" fill="#215694"/>
		<path d="M15.929 7.908a2.971 2.971 0 0 0-2.546-2.94c-.072-.018-.126-.018-.2-.036a31.217 31.217 0 0 0-8.373 0c-.072.018-.143.018-.2.036a2.957 2.957 0 0 0-2.54 2.94 10.336 10.336 0 0 0 5.038 9.054 3.564 3.564 0 0 0 3.765 0 10.208 10.208 0 0 0 5.056-9.054z" fill="url(#A)"/>
		<g clip-path="url(#B)">
			<g opacity=".4">
				<ellipse cx="9.063" cy="7.999" rx="2.331" ry="2.211" fill="#004795"/>
			</g>
			<g opacity=".4">
				<ellipse cx="9.062" cy="15.696" rx="4.661" ry="4.554" fill="#004795"/>
			</g>
			<circle cx="9.062" cy="8.415" r="2.331" fill="url(#C)"/>
			<circle cx="9.062" cy="16.124" r="4.661" fill="url(#D)"/>
		</g>
	</g>
</svg>