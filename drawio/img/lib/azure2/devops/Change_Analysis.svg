<svg
	xmlns="http://www.w3.org/2000/svg" width="16.996" height="16.913">
	<defs>
		<linearGradient id="A" x1="-28.849" y1="86.754" x2="-28.849" y2="90.154" gradientTransform="matrix(0.996,0.094,0.094,-0.996,26.61,101.05031)" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#5e9624"/>
			<stop offset=".82" stop-color="#86d633"/>
		</linearGradient>
		<linearGradient id="B" x1="7.82" y1="16.227" x2="7.82" y2="19.627" gradientTransform="matrix(1,0.011,0.011,-1,2.609,32.49231)" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#5e9624"/>
			<stop offset=".82" stop-color="#86d633"/>
		</linearGradient>
		<linearGradient id="C" x1="-20.227" y1="87.696" x2="-20.227" y2="91.096" gradientTransform="matrix(0.997,0.091,0.091,-0.997,27.29,100.69931)" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#5e9624"/>
			<stop offset=".82" stop-color="#86d633"/>
		</linearGradient>
		<linearGradient id="D" x1="-33.251" y1="80.916" x2="-33.251" y2="84.316" gradientTransform="matrix(0.997,0.091,0.091,-0.997,27.296,100.69931)" gradientUnits="userSpaceOnUse">
			<stop offset="0" stop-color="#5e9624"/>
			<stop offset=".82" stop-color="#86d633"/>
		</linearGradient>
	</defs>
	<path fill="#76bc2d" d="M6.207 9.223l4.41 4.54 4.16-4.41 1.03.97-5.18 5.49-4.37-4.52-4.17 4.67-1.06-.94z"/>
	<circle cx="6.229" cy="10.27" r="1.7" fill="url(#A)"/>
	<circle cx="10.619" cy="14.649" r="1.7" fill="url(#B)"/>
	<circle cx="15.296" cy="9.69" r="1.7" fill="url(#C)"/>
	<circle cx="1.7" cy="15.213" r="1.7" fill="url(#D)"/>
	<path d="M13.677 7.721l-3.18 3.221-3.209-3.221a4.45 4.45 0 0 1-.086-6.293l.086-.085a4.47 4.47 0 0 1 6.323-.066l.066.066a4.471 4.471 0 0 1 .055 6.323z" fill="#0078d4" fill-rule="evenodd"/>
	<path d="M7.757 3.646a2.878 2.878 0 0 0 .291 2.241 2.778 2.778 0 0 0 .522.636 2.919 2.919 0 0 0 .656.469 3.306 3.306 0 0 0 .761.26 2.7 2.7 0 0 0 .823 0l-.072-.781a.167.167 0 0 0-.157-.146 1.938 1.938 0 0 1-.417 0 2.3 2.3 0 0 1-.51-.177 1.944 1.944 0 0 1-.449-.313 1.826 1.826 0 0 1-.354-.438 1.742 1.742 0 0 1-.25-.709 2.055 2.055 0 0 1 0-.75l.271.448a.166.166 0 0 0 .219 0l.365-.094a.167.167 0 0 0 .052-.219l-.9-1.469a.165.165 0 0 0-.2-.1l-1.521.9a.166.166 0 0 0 0 .219l.187.323a.167.167 0 0 0 .219.062zm3.158-1.115a1.746 1.746 0 0 1 .51.177 1.883 1.883 0 0 1 .8.74 1.865 1.865 0 0 1 .25.719 2.071 2.071 0 0 1 0 .761l-.26-.448a.167.167 0 0 0-.218-.067l-.323.2a.157.157 0 0 0-.068.211v.008l.907 1.512a.157.157 0 0 0 .21.067h.009l1.511-.907a.156.156 0 0 0 0-.219l-.188-.323a.168.168 0 0 0-.219 0l-.521.313a2.824 2.824 0 0 0 .082-1.149 2.686 2.686 0 0 0-.375-1.1 2.773 2.773 0 0 0-.511-.646 3.254 3.254 0 0 0-.667-.459 2.752 2.752 0 0 0-.761-.26 2.766 2.766 0 0 0-.646-.1.166.166 0 0 0-.146.177v.615a.168.168 0 0 0 .157.146 1.8 1.8 0 0 1 .467.032z" fill="#fff"/>
</svg>