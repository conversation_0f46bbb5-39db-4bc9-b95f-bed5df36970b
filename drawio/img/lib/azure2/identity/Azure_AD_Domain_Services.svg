<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="ada1a734-f5a0-4f30-8c41-a30875acb17e"
   width="18"
   height="16.379999"
   viewBox="0 0 18 16.379999"
   version="1.1"
   sodipodi:docname="Azure_AD_Domain_Services.svg"
   inkscape:version="0.92.3 (2405546, 2018-03-11)">
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1920"
     inkscape:window-height="1017"
     id="namedview53220"
     showgrid="false"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0"
     inkscape:zoom="48.111111"
     inkscape:cx="9"
     inkscape:cy="8.23"
     inkscape:window-x="-8"
     inkscape:window-y="-8"
     inkscape:window-maximized="1"
     inkscape:current-layer="ada1a734-f5a0-4f30-8c41-a30875acb17e" />
  <defs
     id="defs53195">
    <linearGradient
       id="f92ae47d-0835-4bac-a4f5-649f41e19a24"
       x1="13.25"
       y1="13.06"
       x2="8.6199999"
       y2="4.29"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(0,0.85)">
      <stop
         offset="0"
         stop-color="#1988d9"
         id="stop53179" />
      <stop
         offset="0.9"
         stop-color="#54aef0"
         id="stop53181" />
    </linearGradient>
    <linearGradient
       id="f418fd20-54c9-41f7-a889-8b791ff60efa"
       x1="11.26"
       y1="10.51"
       x2="14.46"
       y2="16.030001"
       gradientUnits="userSpaceOnUse"
       gradientTransform="translate(0,0.85)">
      <stop
         offset="0.1"
         stop-color="#54aef0"
         id="stop53184" />
      <stop
         offset="0.29"
         stop-color="#4fabee"
         id="stop53186" />
      <stop
         offset="0.51"
         stop-color="#41a2e9"
         id="stop53188" />
      <stop
         offset="0.74"
         stop-color="#2a93e0"
         id="stop53190" />
      <stop
         offset="0.88"
         stop-color="#1988d9"
         id="stop53192" />
    </linearGradient>
  </defs>
  <title
     id="title53197">Icon-identity-222</title>
  <polygon
     points="16.99,10.21 18,11.38 8.93,17.23 0,11.39 1.01,10.23 8.93,15.37 "
     id="polygon53199"
     style="fill:#50e6ff"
     transform="translate(0,-0.85)" />
  <polygon
     points="16.4,9.58 8.93,14.3 1.61,9.57 8.93,0.85 "
     id="polygon53201"
     style="fill:#ffffff"
     transform="translate(0,-0.85)" />
  <polygon
     points="1.61,9.57 8.93,0.85 8.93,14.3 "
     id="polygon53203"
     style="fill:#50e6ff"
     transform="translate(0,-0.85)" />
  <polygon
     points="16.4,9.58 8.93,0.85 8.93,14.3 "
     id="polygon53205"
     style="fill:url(#f92ae47d-0835-4bac-a4f5-649f41e19a24)"
     transform="translate(0,-0.85)" />
  <polygon
     points="8.93,14.3 8.93,7.8 16.4,9.58 "
     id="polygon53207"
     style="fill:#53b1e0"
     transform="translate(0,-0.85)" />
  <polygon
     points="8.93,7.8 8.93,14.3 1.61,9.57 "
     id="polygon53209"
     style="fill:#9cebff"
     transform="translate(0,-0.85)" />
  <polygon
     points="16.99,10.21 8.93,15.37 8.93,17.23 18,11.38 "
     id="polygon53211"
     style="fill:url(#f418fd20-54c9-41f7-a889-8b791ff60efa)"
     transform="translate(0,-0.85)" />
  <polygon
     points="13.53,17.12 18,15.3 13.53,9.04 9.06,15.3 "
     id="polygon53213"
     style="fill:#773adc"
     transform="translate(0,-0.85)" />
  <polygon
     points="13.53,17.12 13.53,9.04 9.06,15.3 "
     id="polygon53215"
     style="opacity:0.5;fill:#f9f9f9"
     transform="translate(0,-0.85)" />
  <metadata
     id="metadata53217">
    <rdf:RDF>
      <rdf:li>public:true</rdf:li>
      <rdf:li>sdk:false</rdf:li>
      <rdf:li>category: Identity</rdf:li>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title>Icon-identity-222</dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
</svg>
